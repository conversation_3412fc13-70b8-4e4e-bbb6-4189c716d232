package main

import (
	"context"
	"flag"
	"fmt"
	commonlog "git.blueorigin.work/e-commerce/shopping-common/common/log"
	"git.blueorigin.work/e-commerce/shopping-common/dev"
	"git.blueorigin.work/e-commerce/shopping-common/middle"
	"webapi/datasource"
	"webapi/datasource/metrics"
	"webapi/internal/config"
	"webapi/internal/handler"
	"webapi/internal/scheduler"
	"webapi/internal/scheduler/tasks"
	"webapi/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/webapi.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	c.RestConf.Timeout = 120000
	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	// 开启各项指标采集
	dev.StartPrometheusAgent(func() {
		metrics.EnableRedisMetrics(true)
		metrics.EnableGormMetrics(true)
		commonlog.EnableMetrics(true)
		server.Use(middle.NewRESTMetricsMiddleware(server).Handle)
	})

	// 创建service context
	ctx := svc.NewServiceContext(c, server)

	// 注册handler
	handler.RegisterHandlers(server, ctx)
	handler.RegisterXxlJobRoutes(server, ctx)

	// 初始化数据源
	datasource.InitDatasourceManager(*configFile)
	datasource.InitRedisDatasourceManager(*configFile)

	// 注册 xxl-job 定时任务
	scheduler.Init(context.Background(), ctx)

	err := tasks.ExportI18nToOSS(context.Background(), ctx, `{"tag":"20231001","namespace":"ohsome-app","languages":["zh-CN","en-US","zh-TW","ja-JP","ko-KR","vi-VN","th-TH"]}`)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
