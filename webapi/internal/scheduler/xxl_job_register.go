package scheduler

import (
	"context"
	"webapi/internal/scheduler/tasks"
	"webapi/internal/svc"
	"webapi/locale/enhancer"

	"git.blueorigin.work/e-commerce/shopping-common/job"
)

// Init xxl-job initialization
func Init(ctx context.Context, svcCtx *svc.ServiceContext) {
	svcCtx.JobExecutor.RegTask("scmErrorRequestNotify",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.ScmErrorRequestNotify(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "查询 SCM 请求异常通知"
		}))

	svcCtx.JobExecutor.RegTask("syncLivePerformanceFromFeishu",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.SyncLivePerformanceFromFeishu(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "同步飞书直播表现数据"
		}))

	svcCtx.JobExecutor.RegTask("shopTokenRefreshNotifier",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.ShopTokenRefreshNotifier(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "店铺Token刷新状态通知"
		}))

	svcCtx.JobExecutor.RegTask("orderPushMetricsNotifier",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.OrderPushMetricsNotifier(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "订单下推指标通知"
		}))

	svcCtx.JobExecutor.RegTask("syncInfluencerVideosFromFeishu",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.SyncInfluencerVideosFromFeishu(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "同步飞书达人视频数据"
		}))

	svcCtx.JobExecutor.RegTask("syncTikTokMetricsForInfluencerVideos",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.SyncTikTokMetricsForInfluencerVideos(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "同步TikTok达人视频指标数据"
		}))

	svcCtx.JobExecutor.RegTask("fulfillmentCenterQueueNotifier",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.FulfillmentCenterQueueNotifier(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "履约中心队列长度通知"
		}))

	svcCtx.JobExecutor.RegTask("ondutyNotifier",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.OndutyNotifier(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "值班通知"
		}))

	svcCtx.JobExecutor.RegTask("pendingOrdersThresholdAlert",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.PendingOrdersThresholdAlert(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "待处理订单阈值告警"
		}))

	svcCtx.JobExecutor.RegTask("syncCmsUsersAcrossRegions",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.SyncCmsUsersAcrossRegions(ctx, svcCtx); err != nil {
				return err.Error()
			}
			return "跨区域同步CMS用户数据"
		}))

	svcCtx.JobExecutor.RegTask("aiTranslateNotifier",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.AITranslateNotifier(ctx, svcCtx, param.ExecutorParams); err != nil {
				return err.Error()
			}
			return "AI翻译通知任务"
		}))

	svcCtx.JobExecutor.RegTask("exportI18nToOSS",
		enhancer.WithContextEnhancer(func(ctx context.Context, param *job.RunReq) string {
			if err := tasks.ExportI18nToOSS(ctx, svcCtx, param.ExecutorParams); err != nil {
				return err.Error()
			}
			return "导出语言包到OSS"
		}))
}
