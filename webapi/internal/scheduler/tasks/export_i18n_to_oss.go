package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"webapi/internal/logic/i18n"
	"webapi/internal/svc"
	"webapi/internal/types"

	"git.blueorigin.work/e-commerce/shopping-common/common/log"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

// ExportI18nToOSSParam xxl-job 参数结构
type ExportI18nToOSSParam struct {
	Tag       string   `json:"tag"`       // 版本标签，默认为当前时间戳
	Namespace string   `json:"namespace"` // 命名空间，默认为 "ohsome-app"
	Languages []string `json:"languages"` // 语言列表，默认为指定的7种语言
}

// ExportI18nToOSS 导出语言包到 OSS 的 xxl-job 任务
func ExportI18nToOSS(ctx context.Context, svcCtx *svc.ServiceContext, executorParams string) error {
	logger := log.NewLogger(ctx)
	logger.Info("开始执行导出语言包到 OSS 任务")

	// 解析参数
	param, err := parseExportParams(executorParams)
	if err != nil {
		logger.Errorf("解析任务参数失败: %v", err)
		return err
	}

	logger.Infof("任务参数: tag=%s, namespace=%s, languages=%v", param.Tag, param.Namespace, param.Languages)

	// 导出结果统计
	var results []ExportResult
	var successCount, failCount int

	// 为每种语言和设备类型导出语言包
	devices := []string{"android", "ios"}

	for _, device := range devices {
		for _, language := range param.Languages {
			result := exportLanguagePackage(ctx, svcCtx, param.Namespace, language, device, param.Tag)
			results = append(results, result)

			if result.Success {
				successCount++
				logger.Infof("成功导出 %s %s 语言包", device, language)
			} else {
				failCount++
				logger.Errorf("导出 %s %s 语言包失败: %v", device, language, result.Error)
			}
		}
	}

	// 发送飞书通知
	if err := sendExportNotification(svcCtx, param, results, successCount, failCount); err != nil {
		logger.Errorf("发送飞书通知失败: %v", err)
		return err
	}

	logger.Infof("导出语言包任务完成，成功: %d, 失败: %d", successCount, failCount)
	return nil
}

// ExportResult 导出结果
type ExportResult struct {
	Device   string `json:"device"`
	Language string `json:"language"`
	Success  bool   `json:"success"`
	Error    string `json:"error,omitempty"`
	OSSPath  string `json:"oss_path,omitempty"`
	FileSize int64  `json:"file_size,omitempty"`
}

// parseExportParams 解析任务参数
func parseExportParams(executorParams string) (*ExportI18nToOSSParam, error) {
	// 默认参数
	param := &ExportI18nToOSSParam{
		Tag:       fmt.Sprintf("v%s", time.Now().Format("20060102150405")),
		Namespace: "ohsome-app",
		Languages: []string{"zh-CN", "zh-TW", "en-US", "th-TH", "vi-VN", "id-ID", "km-KH"},
	}

	// 如果有参数，则解析覆盖默认值
	if executorParams != "" {
		if err := json.Unmarshal([]byte(executorParams), param); err != nil {
			return nil, fmt.Errorf("解析参数失败: %v", err)
		}
	}

	// 验证参数
	if param.Tag == "" {
		param.Tag = fmt.Sprintf("v%s", time.Now().Format("20060102150405"))
	}
	if param.Namespace == "" {
		param.Namespace = "ohsome-app"
	}
	if len(param.Languages) == 0 {
		param.Languages = []string{"zh-CN", "zh-TW", "en-US", "th-TH", "vi-VN", "id-ID", "km-KH"}
	}

	return param, nil
}

// exportLanguagePackage 导出单个语言包
func exportLanguagePackage(ctx context.Context, svcCtx *svc.ServiceContext, namespace, language, device, tag string) ExportResult {
	logger := log.NewLogger(ctx)

	result := ExportResult{
		Device:   device,
		Language: language,
		Success:  false,
	}

	// 使用现有的导出逻辑
	exportLogic := i18n.NewExportTranslationsLogic(ctx, svcCtx)

	exportResp, err := exportLogic.ExportTranslations(&types.ExportTranslationsReq{
		LanguageCode: language,
		Namespace:    namespace,
		Device:       device,
	})

	if err != nil {
		result.Error = fmt.Sprintf("导出语言包失败: %v", err)
		return result
	}

	// 生成文件名和路径
	fileName := generateFileName(device, language, tag)
	ossPath := fmt.Sprintf("i18n/%s/%s", tag, fileName)

	// 上传到 OSS
	fileSize, err := uploadToOSS(ctx, svcCtx, ossPath, exportResp.Data)
	if err != nil {
		result.Error = fmt.Sprintf("上传到 OSS 失败: %v", err)
		return result
	}

	result.Success = true
	result.OSSPath = ossPath
	result.FileSize = fileSize

	logger.Infof("成功导出并上传 %s %s 语言包到 %s，文件大小: %d bytes", device, language, ossPath, fileSize)
	return result
}

// generateFileName 生成文件名
func generateFileName(device, language, tag string) string {
	switch device {
	case "android":
		return fmt.Sprintf("strings_%s.xml", language)
	case "ios":
		return fmt.Sprintf("Localizable_%s.strings", language)
	default:
		return fmt.Sprintf("i18n_%s_%s_%s.json", device, language, tag)
	}
}

// uploadToOSS 上传文件到 OSS
func uploadToOSS(ctx context.Context, svcCtx *svc.ServiceContext, ossPath, content string) (int64, error) {
	logger := log.NewLogger(ctx)

	// 创建 OSS 客户端
	client, err := oss.New(svcCtx.Config.OSS.Endpoint, svcCtx.Config.OSS.AccessKeyId, svcCtx.Config.OSS.AccessKeySecret)
	if err != nil {
		return 0, fmt.Errorf("创建 OSS 客户端失败: %v", err)
	}

	// 获取 bucket
	bucket, err := client.Bucket(svcCtx.Config.OSS.BucketName)
	if err != nil {
		return 0, fmt.Errorf("获取 OSS bucket 失败: %v", err)
	}

	// 上传文件
	contentBytes := []byte(content)
	err = bucket.PutObject(ossPath, strings.NewReader(content))
	if err != nil {
		return 0, fmt.Errorf("上传文件到 OSS 失败: %v", err)
	}

	logger.Infof("成功上传文件到 OSS: %s", ossPath)
	return int64(len(contentBytes)), nil
}

// sendExportNotification 发送导出结果通知到飞书
func sendExportNotification(svcCtx *svc.ServiceContext, param *ExportI18nToOSSParam, results []ExportResult, successCount, failCount int) error {
	// 构建通知内容
	markdown := buildExportNotificationMarkdown(param, results, successCount, failCount)

	// 构建飞书消息体
	message := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"title": map[string]interface{}{
					"tag":     "plain_text",
					"content": fmt.Sprintf("📱 语言包导出报告 (%s)", time.Now().Format("2006-01-02 15:04:05")),
				},
				"template": getTemplateColor(successCount, failCount),
			},
			"elements": []map[string]interface{}{
				{
					"tag":     "markdown",
					"content": markdown,
				},
			},
		},
	}

	// 发送到飞书
	resp, err := svcCtx.RestyClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(message).
		Post(svcCtx.Config.FeiShu.AnalysisRobotWebHook)

	if err != nil {
		return fmt.Errorf("发送飞书通知失败: %v", err)
	}

	if resp.StatusCode() != 200 {
		return fmt.Errorf("飞书API返回错误状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// buildExportNotificationMarkdown 构建通知内容
func buildExportNotificationMarkdown(param *ExportI18nToOSSParam, results []ExportResult, successCount, failCount int) string {
	var builder strings.Builder

	// 基本信息
	builder.WriteString(fmt.Sprintf("**版本标签:** %s\n", param.Tag))
	builder.WriteString(fmt.Sprintf("**命名空间:** %s\n", param.Namespace))
	builder.WriteString(fmt.Sprintf("**语言列表:** %s\n", strings.Join(param.Languages, ", ")))
	builder.WriteString(fmt.Sprintf("**导出时间:** %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	// 统计信息
	builder.WriteString("## 📊 导出统计\n")
	builder.WriteString(fmt.Sprintf("- **成功:** %d 个语言包\n", successCount))
	builder.WriteString(fmt.Sprintf("- **失败:** %d 个语言包\n", failCount))
	builder.WriteString(fmt.Sprintf("- **总计:** %d 个语言包\n\n", len(results)))

	// 成功的导出
	if successCount > 0 {
		builder.WriteString("## ✅ 成功导出\n")
		for _, result := range results {
			if result.Success {
				builder.WriteString(fmt.Sprintf("- **%s %s:** `%s` (%d bytes)\n",
					strings.ToUpper(result.Device), result.Language, result.OSSPath, result.FileSize))
			}
		}
		builder.WriteString("\n")
	}

	// 失败的导出
	if failCount > 0 {
		builder.WriteString("## ❌ 导出失败\n")
		for _, result := range results {
			if !result.Success {
				builder.WriteString(fmt.Sprintf("- **%s %s:** %s\n",
					strings.ToUpper(result.Device), result.Language, result.Error))
			}
		}
		builder.WriteString("\n")
	}

	// OSS 访问信息
	if successCount > 0 {
		builder.WriteString("## 🔗 访问信息\n")
		builder.WriteString(fmt.Sprintf("**OSS 路径前缀:** `i18n/%s/`\n", param.Tag))
		builder.WriteString("**文件命名规则:**\n")
		builder.WriteString("- Android: `strings_{language}.xml`\n")
		builder.WriteString("- iOS: `Localizable_{language}.strings`\n")
	}

	return builder.String()
}

// getTemplateColor 根据成功失败数量获取模板颜色
func getTemplateColor(successCount, failCount int) string {
	if failCount == 0 {
		return "green" // 全部成功
	} else if successCount == 0 {
		return "red" // 全部失败
	} else {
		return "orange" // 部分成功
	}
}
