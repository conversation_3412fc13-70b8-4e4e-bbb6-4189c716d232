package config

import "encoding/json"

// FeatureControlConfig 特性开关配置结构
type FeatureControlConfig struct {
	FeatureToggle FeatureToggle `yaml:"feature_toggle" json:"feature_toggle"`
}

// FeatureToggle 特性开关根配置
type FeatureToggle struct {
	// 全局启用开关（熔断开关）
	Enabled bool `yaml:"enabled" json:"enabled"`
	// 所有特性按 namespace 分组
	Namespaces map[string]NamespaceConfig `yaml:"namespaces" json:"namespaces"`
}

// NamespaceConfig 命名空间配置
type NamespaceConfig struct {
	Description string                   `yaml:"description" json:"description"`
	Enabled     bool                     `yaml:"enabled" json:"enabled"`
	Features    map[string]FeatureConfig `yaml:"features" json:"features"`
}

// FeatureConfig 特性配置
type FeatureConfig struct {
	Enabled bool `yaml:"enabled" json:"enabled"`
	// 使用 interface{} 来支持任意类型的配置项
	Config map[string]interface{} `yaml:",inline" json:"-"`
}

// MarshalJSON 自定义 JSON 序列化
func (f FeatureConfig) MarshalJSON() ([]byte, error) {
	// 构建最终输出的 map
	out := make(map[string]interface{})
	out["enabled"] = f.Enabled
	// 把 Config 中的所有 key-value 提升到外层
	for k, v := range f.Config {
		out[k] = v
	}
	return json.Marshal(out)
}

// UnmarshalJSON 自定义 JSON 反序列化
func (f *FeatureConfig) UnmarshalJSON(data []byte) error {
	// 先解析到临时 map
	temp := make(map[string]interface{})
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	// 初始化 Config map
	f.Config = make(map[string]interface{})

	// 处理每个字段
	for k, v := range temp {
		if k == "enabled" {
			if enabled, ok := v.(bool); ok {
				f.Enabled = enabled
			}
		} else {
			// 其他字段都放到 Config 中
			f.Config[k] = v
		}
	}

	return nil
}
