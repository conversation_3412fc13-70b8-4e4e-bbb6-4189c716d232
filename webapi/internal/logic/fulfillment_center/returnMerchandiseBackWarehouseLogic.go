package fulfillment_center

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
	"webapi/dal"
	"webapi/dal/model"
	"webapi/dal/query"
	"webapi/datasource/config"
	"webapi/internal/common/utils"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReturnMerchandiseBackWarehouseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReturnMerchandiseBackWarehouseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReturnMerchandiseBackWarehouseLogic {
	ctx = context.WithValue(ctx, `site`, config.SiteFulfillment)
	return &ReturnMerchandiseBackWarehouseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReturnMerchandiseBackWarehouseLogic) ReturnMerchandiseBackWarehouse(req *types.ReturnMerchandiseBackWarehouseReq) (resp *types.ReturnMerchandiseBackWarehouseResp, err error) {
	// 是否是退货入库 是否退货入库，不是返回报错
	warehouseIn, err := dal.GetWarehouseInEnhanced(l.ctx).GetReturnMerchandise(req.WarehouseInNo)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf(`退货单号已处理或者不正确`)
	}
	if err != nil {
		return nil, fmt.Errorf(`db query error`)
	}
	q := dal.DefaultQ(l.ctx)
	// 校验是否已存在 rma_request 记录
	rmaReq, err := q.RmaRequest.WithContext(l.ctx).
		Where(q.RmaRequest.RmaNo.Eq(req.WarehouseInNo)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf(`db query error`)
	}
	if rmaReq != nil && rmaReq.ID > 0 {
		return nil, nil
	}
	// 获取退货入库单关联商品 warehouse_in_item
	inItems, err := q.WarehouseInItem.WithContext(l.ctx).
		Where(q.WarehouseInItem.ReceiptID.Eq(warehouseIn.ID)).
		Find()
	if err != nil {
		return nil, fmt.Errorf(`db query error`)
	}
	if len(inItems) == 0 {
		return nil, fmt.Errorf(`no warehouse_in_item `)
	}

	var (
		timeNow   = time.Now()
		createdBy = utils.GetEmailFromCtx(l.ctx)
		rmaItems  = make(model.RmaRequestItemSlice, 0, len(inItems))
	)
	rmaReq = &model.RmaRequest{
		RmaNo:     req.WarehouseInNo,
		OrderID:   strings.TrimPrefix(req.WarehouseInNo, `R-`),
		CreatedBy: createdBy,
		CreatedAt: timeNow,
		UpdatedBy: createdBy,
		UpdatedAt: timeNow,
	}
	for _, item := range inItems {
		rmaItems = append(rmaItems, &model.RmaRequestItem{
			RmaNo:     rmaReq.RmaNo,
			Sku:       item.Sku,
			Quantity:  item.Quantity,
			CreatedAt: timeNow,
			UpdatedAt: timeNow,
			CreatedBy: createdBy,
			UpdatedBy: createdBy,
		})
	}
	err = q.Transaction(func(tx *query.Query) error {
		if err = tx.RmaRequest.WithContext(l.ctx).Create(rmaReq); err != nil {
			return err
		}
		if err = tx.RmaRequestItem.WithContext(l.ctx).CreateInBatches(rmaItems, 1000); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, fmt.Errorf(`fix rma request of no: %s failed, db transation error: %v`,
			req.WarehouseInNo, err)
	}
	return
}
