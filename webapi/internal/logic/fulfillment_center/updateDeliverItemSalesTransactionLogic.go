package fulfillment_center

import (
	"context"
	"encoding/json"
	"fmt"

	"webapi/dal/query"
	"webapi/datasource"
	"webapi/datasource/config"
	"webapi/internal/svc"
	"webapi/internal/types"
	"webapi/locale/consts"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDeliverItemSalesTransactionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDeliverItemSalesTransactionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDeliverItemSalesTransactionLogic {
	return &UpdateDeliverItemSalesTransactionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// EventOriginalData 定义事件原始数据结构
type EventOriginalData struct {
	OrderCode string      `json:"orderCode"`
	GoodsList []GoodsItem `json:"goodsList"`
}

// GoodsItem 定义商品项结构
type GoodsItem struct {
	SkuId       string `json:"skuId"`
	GoodsNum    int    `json:"goodsNum"`
	TradingLink string `json:"tradingLink"`
}

// SalesTransactionItem 定义销售交易项结构
type SalesTransactionItem struct {
	Sku                 string `json:"sku"`
	Quantity            int    `json:"quantity"`
	SubjectCode         string `json:"subjectCode"`
	TransactionLinkCode string `json:"transactionLinkCode"`
}

func (l *UpdateDeliverItemSalesTransactionLogic) UpdateDeliverItemSalesTransaction(req *types.UpdateDeliverItemSalesTransactionReq) (resp *types.UpdateDeliverItemSalesTransactionResp, err error) {
	// 指定对应的 region
	l.ctx = context.WithValue(l.ctx, consts.XLocaleHeaderKey, req.Region)

	// 1. 解析 EventOriginalData JSON
	var eventData EventOriginalData
	err = json.Unmarshal([]byte(req.EventOriginalData), &eventData)
	if err != nil {
		l.Errorf("解析 EventOriginalData 失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("解析 EventOriginalData 失败: %v", err),
		}, nil
	}

	// 2. 根据 event_doc_id 查找 deliver 记录
	db := datasource.GetDsCtxOrDefault(l.ctx, config.SiteFulfillment)
	q := query.Use(db)

	// 查找 deliver 记录 (event_doc_id 对应 deliver.code)
	delivers, err := q.Deliver.WithContext(l.ctx).Where(q.Deliver.Code.Eq(req.EventDocId)).Find()
	if err != nil {
		l.Errorf("查找 deliver 记录失败: %v", err)
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("查找 deliver 记录失败: %v", err),
		}, nil
	}

	if len(delivers) == 0 {
		return &types.UpdateDeliverItemSalesTransactionResp{
			Success: false,
			Message: fmt.Sprintf("未找到 event_doc_id 为 %s 的 deliver 记录", req.EventDocId),
		}, nil
	}

	// 3. 获取 deliver_id 列表
	var deliverIDs []int32
	for _, deliver := range delivers {
		deliverIDs = append(deliverIDs, deliver.ID)
	}

	// 4. 处理每个商品的 trading_link
	updatedCount := 0
	for _, goods := range eventData.GoodsList {
		// 解析 tradingLink JSON 字符串
		var tradingLinkData []SalesTransactionItem
		if goods.TradingLink != "" {
			err = json.Unmarshal([]byte(goods.TradingLink), &tradingLinkData)
			if err != nil {
				l.Errorf("解析商品 %s 的 tradingLink 失败: %v", goods.SkuId, err)
				continue
			}
		}

		// 查找对应的 deliver_item 记录
		deliverItems, err := q.DeliverItem.WithContext(l.ctx).
			Where(q.DeliverItem.DeliverID.In(deliverIDs...)).
			Where(q.DeliverItem.Sku.Eq(goods.SkuId)).
			Find()
		if err != nil {
			l.Errorf("查找 deliver_item 记录失败 (sku: %s): %v", goods.SkuId, err)
			continue
		}

		if len(deliverItems) == 0 {
			l.Infof("未找到 event_doc_id 为 %s, sku 为 %s 的 deliver_item 记录", req.EventDocId, goods.SkuId)
			continue
		}

		// 处理每个 deliver_item 记录
		for _, item := range deliverItems {
			// 直接使用 tradingLink 中的数据作为 sales_transaction_master
			// 由于数据库字段类型是 json，直接序列化为 JSON 字符串
			mergedJSON, err := json.Marshal(tradingLinkData)
			if err != nil {
				l.Errorf("序列化 sales_transaction_master 失败 (sku: %s): %v", goods.SkuId, err)
				continue
			}

			// 更新数据库
			_, err = q.DeliverItem.WithContext(l.ctx).
				Where(q.DeliverItem.ID.Eq(item.ID)).
				Update(q.DeliverItem.SalesTransactionMaster, string(mergedJSON))
			if err != nil {
				l.Errorf("更新 deliver_item 记录失败 (sku: %s): %v", goods.SkuId, err)
				continue
			}

			updatedCount++
			l.Infof("成功更新 deliver_item ID: %d, sku: %s, sales_transaction_master: %s", item.ID, goods.SkuId, string(mergedJSON))
		}
	}

	return &types.UpdateDeliverItemSalesTransactionResp{
		Success: true,
		Message: fmt.Sprintf("成功更新 %d 条 deliver_item 记录", updatedCount),
	}, nil
}
