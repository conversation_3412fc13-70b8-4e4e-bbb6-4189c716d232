package release

import (
	"context"
	"fmt"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AiConfigCompareLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAiConfigCompareLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AiConfigCompareLogic {
	return &AiConfigCompareLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type ChatGPTResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
}

func (l *AiConfigCompareLogic) AiConfigCompare(req *types.AIConfigCompareReq) (resp *types.AIConfigCompareResp, err error) {
	// 构建 AI 提示词
	prompt := fmt.Sprintf(`请对比以下两个 YAML 配置文件的差异，并提供详细的分析报告：

应用名称：%s

Git 分支中的配置文件内容：
%s

生产环境 ConfigMap 中的配置内容：
%s

请按照以下格式返回分析结果：

1. 主要差异点（列出具体的配置项差异）
2. 总结（简要说明整体差异情况）
3. 建议（针对发布时需要注意的配置更新建议）

请用中文回答，并且要具体指出哪些配置项有变化、新增或删除。`, req.ApplicationName, req.GitContent, req.ConfigMapContent)

	// 调用 DeepSeek API 进行配置对比分析
	analysis, err := l.callDeepSeekAPI(prompt)
	if err != nil {
		l.Logger.Errorf("Failed to call DeepSeek API: %v", err)
		return nil, err
	}

	// 解析 AI 返回的结果
	differences, summary, suggestions := l.parseAIResponse(analysis)

	// 返回响应
	resp = &types.AIConfigCompareResp{
		Differences: differences,
		Summary:     summary,
		Suggestions: suggestions,
	}

	return resp, nil
}

// callDeepSeekAPI 调用 DeepSeek API 进行配置对比分析
func (l *AiConfigCompareLogic) callDeepSeekAPI(prompt string) (string, error) {
	var chatResp ChatGPTResponse
	_, err := l.svcCtx.RestyClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Authorization", "Bearer "+l.svcCtx.Config.DeepSeek.ApiKey).
		SetBody(map[string]interface{}{
			"model": "deepseek-chat",
			"messages": []map[string]string{
				{
					"role":    "user",
					"content": prompt,
				},
			},
			"temperature": 0.3, // 降低随机性，提高一致性
		}).
		SetResult(&chatResp).
		Post("https://api.deepseek.com/v1/chat/completions")

	if err != nil {
		return "", fmt.Errorf("failed to call DeepSeek API: %v", err)
	}

	if len(chatResp.Choices) == 0 {
		return "", fmt.Errorf("no response from DeepSeek")
	}

	return chatResp.Choices[0].Message.Content, nil
}

// parseAIResponse 解析 AI 返回的结果
func (l *AiConfigCompareLogic) parseAIResponse(response string) (differences []string, summary string, suggestions []string) {
	// 这里可以实现更复杂的解析逻辑
	// 目前简单地将整个响应作为总结返回
	// 在实际使用中，可以根据 AI 返回的格式进行更精确的解析

	differences = []string{response}
	summary = "AI 配置对比分析完成"
	suggestions = []string{"请根据上述分析结果更新生产环境配置"}

	return differences, summary, suggestions
}
