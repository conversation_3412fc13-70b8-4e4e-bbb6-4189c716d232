package release

import (
	"context"
	"fmt"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetApplicationConfigMapContentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetApplicationConfigMapContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetApplicationConfigMapContentLogic {
	return &GetApplicationConfigMapContentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetApplicationConfigMapContentLogic) GetApplicationConfigMapContent(req *types.GetApplicationConfigMapContentReq) (resp *types.GetApplicationConfigMapContentResp, err error) {
	// 获取应用信息
	app, err := l.svcCtx.ApplicationRepo.GetOne(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf("Failed to get application: %v", err)
		return nil, err
	}

	// 检查 ConfigMap 配置是否设置
	if app.Configmap == "" || app.ConfigmapKey == "" {
		l.Logger.Errorf("ConfigMap or ConfigMap key not set for application %d", req.Id)
		return nil, fmt.Errorf("ConfigMap configuration not set for application")
	}

	// 从 Kubernetes 获取 ConfigMap 内容
	// 使用默认命名空间 "ecteam"
	content, err := l.svcCtx.K8sService.GetConfigMapContent(l.ctx, "ecteam", app.Configmap, app.ConfigmapKey)
	if err != nil {
		l.Logger.Errorf("Failed to get ConfigMap content: %v", err)
		return nil, err
	}

	// 返回响应
	resp = &types.GetApplicationConfigMapContentResp{
		Content: content,
	}

	return resp, nil
}
