package release

import (
	"context"
	"encoding/base64"
	"fmt"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetApplicationConfigFileContentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetApplicationConfigFileContentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetApplicationConfigFileContentLogic {
	return &GetApplicationConfigFileContentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetApplicationConfigFileContentLogic) GetApplicationConfigFileContent(req *types.GetApplicationConfigFileContentReq) (resp *types.GetApplicationConfigFileContentResp, err error) {
	// 获取应用信息
	app, err := l.svcCtx.ApplicationRepo.GetOne(l.ctx, req.Id)
	if err != nil {
		l.Logger.<PERSON>rrorf("Failed to get application: %v", err)
		return nil, err
	}

	// 检查配置文件路径是否设置
	if app.ConfigFilePath == "" {
		l.Logger.Errorf("Config file path not set for application %d", req.Id)
		return nil, fmt.Errorf("config file path not set for application")
	}

	// 从 GitLab 获取文件内容
	content, err := l.svcCtx.GitLabSvc.GetFileContent(l.ctx, app.GitlabProjectId, app.ConfigFilePath, req.Branch)
	if err != nil {
		l.Logger.Errorf("Failed to get file content from GitLab: %v", err)
		return nil, err
	}
	// content is base64 encoded
	contentByte, err := base64.StdEncoding.DecodeString(string(content))
	if err != nil {
		l.Logger.Errorf("Failed to decode file content: %v", err)
		return nil, err
	}

	// 返回响应
	resp = &types.GetApplicationConfigFileContentResp{
		Content: string(contentByte),
	}

	return resp, nil
}
