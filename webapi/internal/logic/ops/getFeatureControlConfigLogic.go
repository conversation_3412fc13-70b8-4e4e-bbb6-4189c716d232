package ops

import (
	"context"

	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFeatureControlConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFeatureControlConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFeatureControlConfigLogic {
	return &GetFeatureControlConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetFeatureControlConfigLogic) GetFeatureControlConfig() (resp *types.GetFeatureControlConfigResp, err error) {
	// 从 Nacos 配置管理器获取特性开关配置
	config := l.svcCtx.FeatureControlConfigManager.GetConfig()
	if config == nil {
		return &types.GetFeatureControlConfigResp{
			Success: false,
			Message: "特性开关配置未找到",
		}, nil
	}

	// 转换配置结构
	featureToggle := types.FeatureToggle{
		Enabled:    config.FeatureToggle.Enabled,
		Namespaces: make(map[string]types.NamespaceConfig),
	}

	// 转换命名空间配置
	for nsName, nsConfig := range config.FeatureToggle.Namespaces {
		namespaceConfig := types.NamespaceConfig{
			Description: nsConfig.Description,
			Enabled:     nsConfig.Enabled,
			Features:    make(map[string]types.FeatureConfig),
		}

		// 转换特性配置
		for featureName, featureConfig := range nsConfig.Features {
			namespaceConfig.Features[featureName] = types.FeatureConfig{
				Enabled: featureConfig.Enabled,
				Config:  featureConfig.Config,
			}
		}

		featureToggle.Namespaces[nsName] = namespaceConfig
	}

	return &types.GetFeatureControlConfigResp{
		Success: true,
		Data: types.FeatureControlConfig{
			FeatureToggle: featureToggle,
		},
	}, nil
}
