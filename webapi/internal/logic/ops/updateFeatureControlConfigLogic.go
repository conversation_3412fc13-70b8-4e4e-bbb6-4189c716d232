package ops

import (
	"context"

	"webapi/internal/config"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateFeatureControlConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateFeatureControlConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateFeatureControlConfigLogic {
	return &UpdateFeatureControlConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateFeatureControlConfigLogic) UpdateFeatureControlConfig(req *types.UpdateFeatureControlConfigReq) (resp *types.UpdateFeatureControlConfigResp, err error) {
	// 首先获取当前配置
	currentConfig := l.svcCtx.FeatureControlConfigManager.GetConfig()
	if currentConfig == nil {
		// 如果当前配置为空，则初始化一个空配置
		currentConfig = &config.FeatureControlConfig{
			FeatureToggle: config.FeatureToggle{
				Enabled:    true,
				Namespaces: make(map[string]config.NamespaceConfig),
			},
		}
		l.Infof("当前配置为空，使用默认配置")
	}

	// 深拷贝当前配置作为基础
	featureControlConfig := l.deepCopyConfig(currentConfig)

	// 更新全局开关
	featureControlConfig.FeatureToggle.Enabled = req.FeatureToggle.Enabled

	// 用请求中的数据更新对应的命名空间配置
	for nsName, nsConfig := range req.FeatureToggle.Namespaces {
		namespaceConfig := config.NamespaceConfig{
			Description: nsConfig.Description,
			Enabled:     nsConfig.Enabled,
			Features:    make(map[string]config.FeatureConfig),
		}

		// 转换特性配置
		for featureName, featureConfig := range nsConfig.Features {
			namespaceConfig.Features[featureName] = config.FeatureConfig{
				Enabled: featureConfig.Enabled,
				Config:  featureConfig.Config,
			}
		}

		// 覆盖或添加命名空间配置
		featureControlConfig.FeatureToggle.Namespaces[nsName] = namespaceConfig
		l.Infof("更新命名空间配置: %s", nsName)
	}

	// 更新 Nacos 配置
	err = l.svcCtx.FeatureControlConfigManager.UpdateConfig(featureControlConfig)
	if err != nil {
		l.Errorf("更新特性开关配置失败: %v", err)
		return &types.UpdateFeatureControlConfigResp{
			Success: false,
			Message: "更新配置失败: " + err.Error(),
		}, nil
	}

	return &types.UpdateFeatureControlConfigResp{
		Success: true,
	}, nil
}

// deepCopyConfig 深拷贝配置对象，避免修改原始配置
func (l *UpdateFeatureControlConfigLogic) deepCopyConfig(src *config.FeatureControlConfig) *config.FeatureControlConfig {
	if src == nil {
		return nil
	}

	dst := &config.FeatureControlConfig{
		FeatureToggle: config.FeatureToggle{
			Enabled:    src.FeatureToggle.Enabled,
			Namespaces: make(map[string]config.NamespaceConfig),
		},
	}

	// 深拷贝所有命名空间配置
	for nsName, nsConfig := range src.FeatureToggle.Namespaces {
		namespaceConfig := config.NamespaceConfig{
			Description: nsConfig.Description,
			Enabled:     nsConfig.Enabled,
			Features:    make(map[string]config.FeatureConfig),
		}

		// 深拷贝所有特性配置
		for featureName, featureConfig := range nsConfig.Features {
			// 深拷贝配置项
			configCopy := make(map[string]interface{})
			for k, v := range featureConfig.Config {
				configCopy[k] = v
			}

			namespaceConfig.Features[featureName] = config.FeatureConfig{
				Enabled: featureConfig.Enabled,
				Config:  configCopy,
			}
		}

		dst.FeatureToggle.Namespaces[nsName] = namespaceConfig
	}

	return dst
}
