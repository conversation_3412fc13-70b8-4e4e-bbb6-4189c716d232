package release

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	logic "webapi/internal/logic/release"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func GetApplicationConfigMapContentHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetApplicationConfigMapContentReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewGetApplicationConfigMapContentLogic(r.Context(), svcCtx)
		resp, err := l.GetApplicationConfigMapContent(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
