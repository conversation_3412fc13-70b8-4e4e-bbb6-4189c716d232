package release

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	logic "webapi/internal/logic/release"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func AiConfigCompareHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AIConfigCompareReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewAiConfigCompareLogic(r.Context(), svcCtx)
		resp, err := l.AiConfigCompare(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
