package ops

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/logic/ops"
	"webapi/internal/svc"
)

func GetFeatureControlConfigHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := ops.NewGetFeatureControlConfigLogic(r.Context(), svcCtx)
		resp, err := l.GetFeatureControlConfig()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
