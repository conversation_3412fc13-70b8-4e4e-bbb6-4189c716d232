package ops

import (
	"encoding/json"
	"io"
	"net/http"

	"webapi/internal/logic/ops"
	"webapi/internal/svc"
	"webapi/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func UpdateFeatureControlConfigHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 读取原始请求体
		body, err := io.ReadAll(r.Body)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		// 使用手动 JSON 解析，确保调用我们的自定义 UnmarshalJSON 方法
		var req types.UpdateFeatureControlConfigReq
		if err := json.Unmarshal(body, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := ops.NewUpdateFeatureControlConfigLogic(r.Context(), svcCtx)
		resp, err := l.UpdateFeatureControlConfig(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
