package fulfillment_center

import (
	"context"
	"net/http"
	"webapi/internal/common/result"
	"webapi/internal/logic/fulfillment_center"
	"webapi/locale/consts"

	"github.com/zeromicro/go-zero/rest/httpx"
	"webapi/internal/svc"
	"webapi/internal/types"
)

func ReturnMerchandiseBackWarehouseHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ReturnMerchandiseBackWarehouseReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		ctx := context.WithValue(r.Context(), consts.XLocaleHeaderKey, req.Region)
		l := fulfillment_center.NewReturnMerchandiseBackWarehouseLogic(ctx, svcCtx)
		resp, err := l.ReturnMerchandiseBackWarehouse(&req)
		result.Response(w, resp, err)
	}
}
