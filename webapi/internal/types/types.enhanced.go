package types

import (
	"encoding/json"
)

func (f FeatureConfig) MarshalJSON() ([]byte, error) {
	// 构建最终输出的 map
	out := make(map[string]interface{})
	out["enabled"] = f.Enabled
	// 把 Config 中的所有 key-value 提升到外层
	for k, v := range f.Config {
		out[k] = v
	}
	return json.Marshal(out)
}

func (f *FeatureConfig) UnmarshalJSON(data []byte) error {
	// 先解析成通用 map
	var raw map[string]interface{}
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	// 初始化 Config map
	f.Config = make(map[string]interface{})

	// 处理每个字段
	for k, v := range raw {
		if k == "enabled" {
			if b, ok := v.(bool); ok {
				f.Enabled = b
			}
		} else {
			// 其他字段都放入 Config
			f.Config[k] = v
		}
	}

	return nil
}
