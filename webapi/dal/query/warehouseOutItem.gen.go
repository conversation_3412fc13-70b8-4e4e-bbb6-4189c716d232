// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseOutItem(db *gorm.DB, opts ...gen.DOOption) warehouseOutItem {
	_warehouseOutItem := warehouseOutItem{}

	_warehouseOutItem.warehouseOutItemDo.UseDB(db, opts...)
	_warehouseOutItem.warehouseOutItemDo.UseModel(&model.WarehouseOutItem{})

	tableName := _warehouseOutItem.warehouseOutItemDo.TableName()
	_warehouseOutItem.ALL = field.NewAsterisk(tableName)
	_warehouseOutItem.ID = field.NewInt32(tableName, "id")
	_warehouseOutItem.ReceiptID = field.NewInt32(tableName, "receipt_id")
	_warehouseOutItem.Sku = field.NewString(tableName, "sku")
	_warehouseOutItem.Name = field.NewString(tableName, "name")
	_warehouseOutItem.Price = field.NewFloat64(tableName, "price")
	_warehouseOutItem.Currency = field.NewString(tableName, "currency")
	_warehouseOutItem.Quantity = field.NewInt32(tableName, "quantity")
	_warehouseOutItem.SalesTransactionMaster = field.NewString(tableName, "sales_transaction_master")

	_warehouseOutItem.fillFieldMap()

	return _warehouseOutItem
}

type warehouseOutItem struct {
	warehouseOutItemDo warehouseOutItemDo

	ALL                    field.Asterisk
	ID                     field.Int32
	ReceiptID              field.Int32
	Sku                    field.String
	Name                   field.String
	Price                  field.Float64
	Currency               field.String
	Quantity               field.Int32
	SalesTransactionMaster field.String // 交易链路主体

	fieldMap map[string]field.Expr
}

func (w warehouseOutItem) Table(newTableName string) *warehouseOutItem {
	w.warehouseOutItemDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseOutItem) As(alias string) *warehouseOutItem {
	w.warehouseOutItemDo.DO = *(w.warehouseOutItemDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseOutItem) updateTableName(table string) *warehouseOutItem {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.ReceiptID = field.NewInt32(table, "receipt_id")
	w.Sku = field.NewString(table, "sku")
	w.Name = field.NewString(table, "name")
	w.Price = field.NewFloat64(table, "price")
	w.Currency = field.NewString(table, "currency")
	w.Quantity = field.NewInt32(table, "quantity")
	w.SalesTransactionMaster = field.NewString(table, "sales_transaction_master")

	w.fillFieldMap()

	return w
}

func (w *warehouseOutItem) WithContext(ctx context.Context) IWarehouseOutItemDo {
	return w.warehouseOutItemDo.WithContext(ctx)
}

func (w warehouseOutItem) TableName() string { return w.warehouseOutItemDo.TableName() }

func (w warehouseOutItem) Alias() string { return w.warehouseOutItemDo.Alias() }

func (w warehouseOutItem) Columns(cols ...field.Expr) gen.Columns {
	return w.warehouseOutItemDo.Columns(cols...)
}

func (w *warehouseOutItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseOutItem) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["receipt_id"] = w.ReceiptID
	w.fieldMap["sku"] = w.Sku
	w.fieldMap["name"] = w.Name
	w.fieldMap["price"] = w.Price
	w.fieldMap["currency"] = w.Currency
	w.fieldMap["quantity"] = w.Quantity
	w.fieldMap["sales_transaction_master"] = w.SalesTransactionMaster
}

func (w warehouseOutItem) clone(db *gorm.DB) warehouseOutItem {
	w.warehouseOutItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseOutItem) replaceDB(db *gorm.DB) warehouseOutItem {
	w.warehouseOutItemDo.ReplaceDB(db)
	return w
}

type warehouseOutItemDo struct{ gen.DO }

type IWarehouseOutItemDo interface {
	gen.SubQuery
	Debug() IWarehouseOutItemDo
	WithContext(ctx context.Context) IWarehouseOutItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseOutItemDo
	WriteDB() IWarehouseOutItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseOutItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseOutItemDo
	Not(conds ...gen.Condition) IWarehouseOutItemDo
	Or(conds ...gen.Condition) IWarehouseOutItemDo
	Select(conds ...field.Expr) IWarehouseOutItemDo
	Where(conds ...gen.Condition) IWarehouseOutItemDo
	Order(conds ...field.Expr) IWarehouseOutItemDo
	Distinct(cols ...field.Expr) IWarehouseOutItemDo
	Omit(cols ...field.Expr) IWarehouseOutItemDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo
	Group(cols ...field.Expr) IWarehouseOutItemDo
	Having(conds ...gen.Condition) IWarehouseOutItemDo
	Limit(limit int) IWarehouseOutItemDo
	Offset(offset int) IWarehouseOutItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutItemDo
	Unscoped() IWarehouseOutItemDo
	Create(values ...*model.WarehouseOutItem) error
	CreateInBatches(values model.WarehouseOutItemSlice, batchSize int) error
	Save(values ...*model.WarehouseOutItem) error
	First() (*model.WarehouseOutItem, error)
	Take() (*model.WarehouseOutItem, error)
	Last() (*model.WarehouseOutItem, error)
	Find() (model.WarehouseOutItemSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutItemSlice, err error)
	FindInBatches(result *model.WarehouseOutItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseOutItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseOutItemDo
	Assign(attrs ...field.AssignExpr) IWarehouseOutItemDo
	Joins(fields ...field.RelationField) IWarehouseOutItemDo
	Preload(fields ...field.RelationField) IWarehouseOutItemDo
	FirstOrInit() (*model.WarehouseOutItem, error)
	FirstOrCreate() (*model.WarehouseOutItem, error)
	FindByPage(offset int, limit int) (result model.WarehouseOutItemSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseOutItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseOutItemDo) Debug() IWarehouseOutItemDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseOutItemDo) WithContext(ctx context.Context) IWarehouseOutItemDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseOutItemDo) ReadDB() IWarehouseOutItemDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseOutItemDo) WriteDB() IWarehouseOutItemDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseOutItemDo) Session(config *gorm.Session) IWarehouseOutItemDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseOutItemDo) Clauses(conds ...clause.Expression) IWarehouseOutItemDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseOutItemDo) Returning(value interface{}, columns ...string) IWarehouseOutItemDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseOutItemDo) Not(conds ...gen.Condition) IWarehouseOutItemDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseOutItemDo) Or(conds ...gen.Condition) IWarehouseOutItemDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseOutItemDo) Select(conds ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseOutItemDo) Where(conds ...gen.Condition) IWarehouseOutItemDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseOutItemDo) Order(conds ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseOutItemDo) Distinct(cols ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseOutItemDo) Omit(cols ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseOutItemDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseOutItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseOutItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseOutItemDo) Group(cols ...field.Expr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseOutItemDo) Having(conds ...gen.Condition) IWarehouseOutItemDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseOutItemDo) Limit(limit int) IWarehouseOutItemDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseOutItemDo) Offset(offset int) IWarehouseOutItemDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseOutItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutItemDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseOutItemDo) Unscoped() IWarehouseOutItemDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseOutItemDo) Create(values ...*model.WarehouseOutItem) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseOutItemDo) CreateInBatches(values model.WarehouseOutItemSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseOutItemDo) Save(values ...*model.WarehouseOutItem) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseOutItemDo) First() (*model.WarehouseOutItem, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutItem), nil
	}
}

func (w warehouseOutItemDo) Take() (*model.WarehouseOutItem, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutItem), nil
	}
}

func (w warehouseOutItemDo) Last() (*model.WarehouseOutItem, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutItem), nil
	}
}

func (w warehouseOutItemDo) Find() (model.WarehouseOutItemSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseOutItemSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseOutItem); ok {
		return model.WarehouseOutItemSlice(slice), err
	}
	return model.WarehouseOutItemSlice{}, err

}

func (w warehouseOutItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutItemSlice, err error) {
	buf := make([]*model.WarehouseOutItem, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseOutItemDo) FindInBatches(result *model.WarehouseOutItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseOutItemDo) Attrs(attrs ...field.AssignExpr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseOutItemDo) Assign(attrs ...field.AssignExpr) IWarehouseOutItemDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseOutItemDo) Joins(fields ...field.RelationField) IWarehouseOutItemDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseOutItemDo) Preload(fields ...field.RelationField) IWarehouseOutItemDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseOutItemDo) FirstOrInit() (*model.WarehouseOutItem, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutItem), nil
	}
}

func (w warehouseOutItemDo) FirstOrCreate() (*model.WarehouseOutItem, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutItem), nil
	}
}

func (w warehouseOutItemDo) FindByPage(offset int, limit int) (result model.WarehouseOutItemSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseOutItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseOutItemDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseOutItemDo) Delete(models ...*model.WarehouseOutItem) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseOutItemDo) withDO(do gen.Dao) *warehouseOutItemDo {
	w.DO = *do.(*gen.DO)
	return w
}
