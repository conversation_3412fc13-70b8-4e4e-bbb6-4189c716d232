// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseInItem(db *gorm.DB, opts ...gen.DOOption) warehouseInItem {
	_warehouseInItem := warehouseInItem{}

	_warehouseInItem.warehouseInItemDo.UseDB(db, opts...)
	_warehouseInItem.warehouseInItemDo.UseModel(&model.WarehouseInItem{})

	tableName := _warehouseInItem.warehouseInItemDo.TableName()
	_warehouseInItem.ALL = field.NewAsterisk(tableName)
	_warehouseInItem.ID = field.NewInt32(tableName, "id")
	_warehouseInItem.ReceiptID = field.NewInt32(tableName, "receipt_id")
	_warehouseInItem.Sku = field.NewString(tableName, "sku")
	_warehouseInItem.Name = field.NewString(tableName, "name")
	_warehouseInItem.Price = field.NewFloat64(tableName, "price")
	_warehouseInItem.Currency = field.NewString(tableName, "currency")
	_warehouseInItem.Quantity = field.NewInt32(tableName, "quantity")
	_warehouseInItem.SalesTransactionMaster = field.NewString(tableName, "sales_transaction_master")

	_warehouseInItem.fillFieldMap()

	return _warehouseInItem
}

type warehouseInItem struct {
	warehouseInItemDo warehouseInItemDo

	ALL                    field.Asterisk
	ID                     field.Int32
	ReceiptID              field.Int32
	Sku                    field.String
	Name                   field.String
	Price                  field.Float64
	Currency               field.String
	Quantity               field.Int32
	SalesTransactionMaster field.String // 交易链路主体

	fieldMap map[string]field.Expr
}

func (w warehouseInItem) Table(newTableName string) *warehouseInItem {
	w.warehouseInItemDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseInItem) As(alias string) *warehouseInItem {
	w.warehouseInItemDo.DO = *(w.warehouseInItemDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseInItem) updateTableName(table string) *warehouseInItem {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.ReceiptID = field.NewInt32(table, "receipt_id")
	w.Sku = field.NewString(table, "sku")
	w.Name = field.NewString(table, "name")
	w.Price = field.NewFloat64(table, "price")
	w.Currency = field.NewString(table, "currency")
	w.Quantity = field.NewInt32(table, "quantity")
	w.SalesTransactionMaster = field.NewString(table, "sales_transaction_master")

	w.fillFieldMap()

	return w
}

func (w *warehouseInItem) WithContext(ctx context.Context) IWarehouseInItemDo {
	return w.warehouseInItemDo.WithContext(ctx)
}

func (w warehouseInItem) TableName() string { return w.warehouseInItemDo.TableName() }

func (w warehouseInItem) Alias() string { return w.warehouseInItemDo.Alias() }

func (w warehouseInItem) Columns(cols ...field.Expr) gen.Columns {
	return w.warehouseInItemDo.Columns(cols...)
}

func (w *warehouseInItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseInItem) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["receipt_id"] = w.ReceiptID
	w.fieldMap["sku"] = w.Sku
	w.fieldMap["name"] = w.Name
	w.fieldMap["price"] = w.Price
	w.fieldMap["currency"] = w.Currency
	w.fieldMap["quantity"] = w.Quantity
	w.fieldMap["sales_transaction_master"] = w.SalesTransactionMaster
}

func (w warehouseInItem) clone(db *gorm.DB) warehouseInItem {
	w.warehouseInItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseInItem) replaceDB(db *gorm.DB) warehouseInItem {
	w.warehouseInItemDo.ReplaceDB(db)
	return w
}

type warehouseInItemDo struct{ gen.DO }

type IWarehouseInItemDo interface {
	gen.SubQuery
	Debug() IWarehouseInItemDo
	WithContext(ctx context.Context) IWarehouseInItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseInItemDo
	WriteDB() IWarehouseInItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseInItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseInItemDo
	Not(conds ...gen.Condition) IWarehouseInItemDo
	Or(conds ...gen.Condition) IWarehouseInItemDo
	Select(conds ...field.Expr) IWarehouseInItemDo
	Where(conds ...gen.Condition) IWarehouseInItemDo
	Order(conds ...field.Expr) IWarehouseInItemDo
	Distinct(cols ...field.Expr) IWarehouseInItemDo
	Omit(cols ...field.Expr) IWarehouseInItemDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo
	Group(cols ...field.Expr) IWarehouseInItemDo
	Having(conds ...gen.Condition) IWarehouseInItemDo
	Limit(limit int) IWarehouseInItemDo
	Offset(offset int) IWarehouseInItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseInItemDo
	Unscoped() IWarehouseInItemDo
	Create(values ...*model.WarehouseInItem) error
	CreateInBatches(values model.WarehouseInItemSlice, batchSize int) error
	Save(values ...*model.WarehouseInItem) error
	First() (*model.WarehouseInItem, error)
	Take() (*model.WarehouseInItem, error)
	Last() (*model.WarehouseInItem, error)
	Find() (model.WarehouseInItemSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseInItemSlice, err error)
	FindInBatches(result *model.WarehouseInItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseInItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseInItemDo
	Assign(attrs ...field.AssignExpr) IWarehouseInItemDo
	Joins(fields ...field.RelationField) IWarehouseInItemDo
	Preload(fields ...field.RelationField) IWarehouseInItemDo
	FirstOrInit() (*model.WarehouseInItem, error)
	FirstOrCreate() (*model.WarehouseInItem, error)
	FindByPage(offset int, limit int) (result model.WarehouseInItemSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseInItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseInItemDo) Debug() IWarehouseInItemDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseInItemDo) WithContext(ctx context.Context) IWarehouseInItemDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseInItemDo) ReadDB() IWarehouseInItemDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseInItemDo) WriteDB() IWarehouseInItemDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseInItemDo) Session(config *gorm.Session) IWarehouseInItemDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseInItemDo) Clauses(conds ...clause.Expression) IWarehouseInItemDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseInItemDo) Returning(value interface{}, columns ...string) IWarehouseInItemDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseInItemDo) Not(conds ...gen.Condition) IWarehouseInItemDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseInItemDo) Or(conds ...gen.Condition) IWarehouseInItemDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseInItemDo) Select(conds ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseInItemDo) Where(conds ...gen.Condition) IWarehouseInItemDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseInItemDo) Order(conds ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseInItemDo) Distinct(cols ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseInItemDo) Omit(cols ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseInItemDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseInItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseInItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseInItemDo) Group(cols ...field.Expr) IWarehouseInItemDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseInItemDo) Having(conds ...gen.Condition) IWarehouseInItemDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseInItemDo) Limit(limit int) IWarehouseInItemDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseInItemDo) Offset(offset int) IWarehouseInItemDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseInItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseInItemDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseInItemDo) Unscoped() IWarehouseInItemDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseInItemDo) Create(values ...*model.WarehouseInItem) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseInItemDo) CreateInBatches(values model.WarehouseInItemSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseInItemDo) Save(values ...*model.WarehouseInItem) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseInItemDo) First() (*model.WarehouseInItem, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseInItem), nil
	}
}

func (w warehouseInItemDo) Take() (*model.WarehouseInItem, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseInItem), nil
	}
}

func (w warehouseInItemDo) Last() (*model.WarehouseInItem, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseInItem), nil
	}
}

func (w warehouseInItemDo) Find() (model.WarehouseInItemSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseInItemSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseInItem); ok {
		return model.WarehouseInItemSlice(slice), err
	}
	return model.WarehouseInItemSlice{}, err

}

func (w warehouseInItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseInItemSlice, err error) {
	buf := make([]*model.WarehouseInItem, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseInItemDo) FindInBatches(result *model.WarehouseInItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseInItemDo) Attrs(attrs ...field.AssignExpr) IWarehouseInItemDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseInItemDo) Assign(attrs ...field.AssignExpr) IWarehouseInItemDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseInItemDo) Joins(fields ...field.RelationField) IWarehouseInItemDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseInItemDo) Preload(fields ...field.RelationField) IWarehouseInItemDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseInItemDo) FirstOrInit() (*model.WarehouseInItem, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseInItem), nil
	}
}

func (w warehouseInItemDo) FirstOrCreate() (*model.WarehouseInItem, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseInItem), nil
	}
}

func (w warehouseInItemDo) FindByPage(offset int, limit int) (result model.WarehouseInItemSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseInItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseInItemDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseInItemDo) Delete(models ...*model.WarehouseInItem) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseInItemDo) withDO(do gen.Dao) *warehouseInItemDo {
	w.DO = *do.(*gen.DO)
	return w
}
