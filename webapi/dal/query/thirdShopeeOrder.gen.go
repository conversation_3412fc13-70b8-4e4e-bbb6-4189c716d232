// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdShopeeOrder(db *gorm.DB, opts ...gen.DOOption) thirdShopeeOrder {
	_thirdShopeeOrder := thirdShopeeOrder{}

	_thirdShopeeOrder.thirdShopeeOrderDo.UseDB(db, opts...)
	_thirdShopeeOrder.thirdShopeeOrderDo.UseModel(&model.ThirdShopeeOrder{})

	tableName := _thirdShopeeOrder.thirdShopeeOrderDo.TableName()
	_thirdShopeeOrder.ALL = field.NewAsterisk(tableName)
	_thirdShopeeOrder.OrderSn = field.NewString(tableName, "order_sn")
	_thirdShopeeOrder.ActualShippingFee = field.NewFloat64(tableName, "actual_shipping_fee")
	_thirdShopeeOrder.Region = field.NewString(tableName, "region")
	_thirdShopeeOrder.Currency = field.NewString(tableName, "currency")
	_thirdShopeeOrder.Cod = field.NewBool(tableName, "cod")
	_thirdShopeeOrder.TotalAmount = field.NewFloat64(tableName, "total_amount")
	_thirdShopeeOrder.OrderStatus = field.NewString(tableName, "order_status")
	_thirdShopeeOrder.PendingTerms = field.NewString(tableName, "pending_terms")
	_thirdShopeeOrder.ShippingCarrier = field.NewString(tableName, "shipping_carrier")
	_thirdShopeeOrder.PaymentMethod = field.NewString(tableName, "payment_method")
	_thirdShopeeOrder.EstimatedShippingFee = field.NewFloat64(tableName, "estimated_shipping_fee")
	_thirdShopeeOrder.MessageToSeller = field.NewString(tableName, "message_to_seller")
	_thirdShopeeOrder.CreateTime = field.NewInt64(tableName, "create_time")
	_thirdShopeeOrder.UpdateTime = field.NewInt64(tableName, "update_time")
	_thirdShopeeOrder.DaysToShip = field.NewInt32(tableName, "days_to_ship")
	_thirdShopeeOrder.ShipByDate = field.NewInt64(tableName, "ship_by_date")
	_thirdShopeeOrder.BuyerUserID = field.NewInt32(tableName, "buyer_user_id")
	_thirdShopeeOrder.BuyerUsername = field.NewString(tableName, "buyer_username")
	_thirdShopeeOrder.RecipientAddress = field.NewString(tableName, "recipient_address")
	_thirdShopeeOrder.GoodsToDeclare = field.NewBool(tableName, "goods_to_declare")
	_thirdShopeeOrder.Note = field.NewString(tableName, "note")
	_thirdShopeeOrder.NoteUpdateTime = field.NewInt64(tableName, "note_update_time")
	_thirdShopeeOrder.ItemList = field.NewString(tableName, "item_list")
	_thirdShopeeOrder.PayTime = field.NewInt64(tableName, "pay_time")
	_thirdShopeeOrder.Dropshipper = field.NewString(tableName, "dropshipper")
	_thirdShopeeOrder.DropshipperPhone = field.NewString(tableName, "dropshipper_phone")
	_thirdShopeeOrder.SplitUp = field.NewBool(tableName, "split_up")
	_thirdShopeeOrder.BuyerCancelReason = field.NewString(tableName, "buyer_cancel_reason")
	_thirdShopeeOrder.CancelBy = field.NewString(tableName, "cancel_by")
	_thirdShopeeOrder.CancelReason = field.NewString(tableName, "cancel_reason")
	_thirdShopeeOrder.ActualShippingFeeConfirmed = field.NewBool(tableName, "actual_shipping_fee_confirmed")
	_thirdShopeeOrder.BuyerCpfID = field.NewString(tableName, "buyer_cpf_id")
	_thirdShopeeOrder.FulfillmentFlag = field.NewString(tableName, "fulfillment_flag")
	_thirdShopeeOrder.PickupDoneTime = field.NewInt64(tableName, "pickup_done_time")
	_thirdShopeeOrder.PackageList = field.NewString(tableName, "package_list")
	_thirdShopeeOrder.InvoiceData = field.NewString(tableName, "invoice_data")
	_thirdShopeeOrder.CheckoutShippingCarrier = field.NewString(tableName, "checkout_shipping_carrier")
	_thirdShopeeOrder.ReverseShippingFee = field.NewFloat64(tableName, "reverse_shipping_fee")
	_thirdShopeeOrder.OrderChargeableWeightGram = field.NewInt32(tableName, "order_chargeable_weight_gram")
	_thirdShopeeOrder.EdtFrom = field.NewInt64(tableName, "edt_from")
	_thirdShopeeOrder.EdtTo = field.NewInt64(tableName, "edt_to")
	_thirdShopeeOrder.PrescriptionImages = field.NewString(tableName, "prescription_images")
	_thirdShopeeOrder.PrescriptionCheckStatus = field.NewInt32(tableName, "prescription_check_status")

	_thirdShopeeOrder.fillFieldMap()

	return _thirdShopeeOrder
}

type thirdShopeeOrder struct {
	thirdShopeeOrderDo thirdShopeeOrderDo

	ALL                        field.Asterisk
	OrderSn                    field.String
	ActualShippingFee          field.Float64
	Region                     field.String
	Currency                   field.String
	Cod                        field.Bool
	TotalAmount                field.Float64
	OrderStatus                field.String
	PendingTerms               field.String
	ShippingCarrier            field.String
	PaymentMethod              field.String
	EstimatedShippingFee       field.Float64
	MessageToSeller            field.String
	CreateTime                 field.Int64
	UpdateTime                 field.Int64
	DaysToShip                 field.Int32
	ShipByDate                 field.Int64
	BuyerUserID                field.Int32
	BuyerUsername              field.String
	RecipientAddress           field.String
	GoodsToDeclare             field.Bool
	Note                       field.String
	NoteUpdateTime             field.Int64
	ItemList                   field.String
	PayTime                    field.Int64
	Dropshipper                field.String
	DropshipperPhone           field.String
	SplitUp                    field.Bool
	BuyerCancelReason          field.String
	CancelBy                   field.String
	CancelReason               field.String
	ActualShippingFeeConfirmed field.Bool
	BuyerCpfID                 field.String
	FulfillmentFlag            field.String
	PickupDoneTime             field.Int64
	PackageList                field.String
	InvoiceData                field.String
	CheckoutShippingCarrier    field.String
	ReverseShippingFee         field.Float64
	OrderChargeableWeightGram  field.Int32
	EdtFrom                    field.Int64
	EdtTo                      field.Int64
	PrescriptionImages         field.String
	PrescriptionCheckStatus    field.Int32

	fieldMap map[string]field.Expr
}

func (t thirdShopeeOrder) Table(newTableName string) *thirdShopeeOrder {
	t.thirdShopeeOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdShopeeOrder) As(alias string) *thirdShopeeOrder {
	t.thirdShopeeOrderDo.DO = *(t.thirdShopeeOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdShopeeOrder) updateTableName(table string) *thirdShopeeOrder {
	t.ALL = field.NewAsterisk(table)
	t.OrderSn = field.NewString(table, "order_sn")
	t.ActualShippingFee = field.NewFloat64(table, "actual_shipping_fee")
	t.Region = field.NewString(table, "region")
	t.Currency = field.NewString(table, "currency")
	t.Cod = field.NewBool(table, "cod")
	t.TotalAmount = field.NewFloat64(table, "total_amount")
	t.OrderStatus = field.NewString(table, "order_status")
	t.PendingTerms = field.NewString(table, "pending_terms")
	t.ShippingCarrier = field.NewString(table, "shipping_carrier")
	t.PaymentMethod = field.NewString(table, "payment_method")
	t.EstimatedShippingFee = field.NewFloat64(table, "estimated_shipping_fee")
	t.MessageToSeller = field.NewString(table, "message_to_seller")
	t.CreateTime = field.NewInt64(table, "create_time")
	t.UpdateTime = field.NewInt64(table, "update_time")
	t.DaysToShip = field.NewInt32(table, "days_to_ship")
	t.ShipByDate = field.NewInt64(table, "ship_by_date")
	t.BuyerUserID = field.NewInt32(table, "buyer_user_id")
	t.BuyerUsername = field.NewString(table, "buyer_username")
	t.RecipientAddress = field.NewString(table, "recipient_address")
	t.GoodsToDeclare = field.NewBool(table, "goods_to_declare")
	t.Note = field.NewString(table, "note")
	t.NoteUpdateTime = field.NewInt64(table, "note_update_time")
	t.ItemList = field.NewString(table, "item_list")
	t.PayTime = field.NewInt64(table, "pay_time")
	t.Dropshipper = field.NewString(table, "dropshipper")
	t.DropshipperPhone = field.NewString(table, "dropshipper_phone")
	t.SplitUp = field.NewBool(table, "split_up")
	t.BuyerCancelReason = field.NewString(table, "buyer_cancel_reason")
	t.CancelBy = field.NewString(table, "cancel_by")
	t.CancelReason = field.NewString(table, "cancel_reason")
	t.ActualShippingFeeConfirmed = field.NewBool(table, "actual_shipping_fee_confirmed")
	t.BuyerCpfID = field.NewString(table, "buyer_cpf_id")
	t.FulfillmentFlag = field.NewString(table, "fulfillment_flag")
	t.PickupDoneTime = field.NewInt64(table, "pickup_done_time")
	t.PackageList = field.NewString(table, "package_list")
	t.InvoiceData = field.NewString(table, "invoice_data")
	t.CheckoutShippingCarrier = field.NewString(table, "checkout_shipping_carrier")
	t.ReverseShippingFee = field.NewFloat64(table, "reverse_shipping_fee")
	t.OrderChargeableWeightGram = field.NewInt32(table, "order_chargeable_weight_gram")
	t.EdtFrom = field.NewInt64(table, "edt_from")
	t.EdtTo = field.NewInt64(table, "edt_to")
	t.PrescriptionImages = field.NewString(table, "prescription_images")
	t.PrescriptionCheckStatus = field.NewInt32(table, "prescription_check_status")

	t.fillFieldMap()

	return t
}

func (t *thirdShopeeOrder) WithContext(ctx context.Context) IThirdShopeeOrderDo {
	return t.thirdShopeeOrderDo.WithContext(ctx)
}

func (t thirdShopeeOrder) TableName() string { return t.thirdShopeeOrderDo.TableName() }

func (t thirdShopeeOrder) Alias() string { return t.thirdShopeeOrderDo.Alias() }

func (t thirdShopeeOrder) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdShopeeOrderDo.Columns(cols...)
}

func (t *thirdShopeeOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdShopeeOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 43)
	t.fieldMap["order_sn"] = t.OrderSn
	t.fieldMap["actual_shipping_fee"] = t.ActualShippingFee
	t.fieldMap["region"] = t.Region
	t.fieldMap["currency"] = t.Currency
	t.fieldMap["cod"] = t.Cod
	t.fieldMap["total_amount"] = t.TotalAmount
	t.fieldMap["order_status"] = t.OrderStatus
	t.fieldMap["pending_terms"] = t.PendingTerms
	t.fieldMap["shipping_carrier"] = t.ShippingCarrier
	t.fieldMap["payment_method"] = t.PaymentMethod
	t.fieldMap["estimated_shipping_fee"] = t.EstimatedShippingFee
	t.fieldMap["message_to_seller"] = t.MessageToSeller
	t.fieldMap["create_time"] = t.CreateTime
	t.fieldMap["update_time"] = t.UpdateTime
	t.fieldMap["days_to_ship"] = t.DaysToShip
	t.fieldMap["ship_by_date"] = t.ShipByDate
	t.fieldMap["buyer_user_id"] = t.BuyerUserID
	t.fieldMap["buyer_username"] = t.BuyerUsername
	t.fieldMap["recipient_address"] = t.RecipientAddress
	t.fieldMap["goods_to_declare"] = t.GoodsToDeclare
	t.fieldMap["note"] = t.Note
	t.fieldMap["note_update_time"] = t.NoteUpdateTime
	t.fieldMap["item_list"] = t.ItemList
	t.fieldMap["pay_time"] = t.PayTime
	t.fieldMap["dropshipper"] = t.Dropshipper
	t.fieldMap["dropshipper_phone"] = t.DropshipperPhone
	t.fieldMap["split_up"] = t.SplitUp
	t.fieldMap["buyer_cancel_reason"] = t.BuyerCancelReason
	t.fieldMap["cancel_by"] = t.CancelBy
	t.fieldMap["cancel_reason"] = t.CancelReason
	t.fieldMap["actual_shipping_fee_confirmed"] = t.ActualShippingFeeConfirmed
	t.fieldMap["buyer_cpf_id"] = t.BuyerCpfID
	t.fieldMap["fulfillment_flag"] = t.FulfillmentFlag
	t.fieldMap["pickup_done_time"] = t.PickupDoneTime
	t.fieldMap["package_list"] = t.PackageList
	t.fieldMap["invoice_data"] = t.InvoiceData
	t.fieldMap["checkout_shipping_carrier"] = t.CheckoutShippingCarrier
	t.fieldMap["reverse_shipping_fee"] = t.ReverseShippingFee
	t.fieldMap["order_chargeable_weight_gram"] = t.OrderChargeableWeightGram
	t.fieldMap["edt_from"] = t.EdtFrom
	t.fieldMap["edt_to"] = t.EdtTo
	t.fieldMap["prescription_images"] = t.PrescriptionImages
	t.fieldMap["prescription_check_status"] = t.PrescriptionCheckStatus
}

func (t thirdShopeeOrder) clone(db *gorm.DB) thirdShopeeOrder {
	t.thirdShopeeOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdShopeeOrder) replaceDB(db *gorm.DB) thirdShopeeOrder {
	t.thirdShopeeOrderDo.ReplaceDB(db)
	return t
}

type thirdShopeeOrderDo struct{ gen.DO }

type IThirdShopeeOrderDo interface {
	gen.SubQuery
	Debug() IThirdShopeeOrderDo
	WithContext(ctx context.Context) IThirdShopeeOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdShopeeOrderDo
	WriteDB() IThirdShopeeOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdShopeeOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdShopeeOrderDo
	Not(conds ...gen.Condition) IThirdShopeeOrderDo
	Or(conds ...gen.Condition) IThirdShopeeOrderDo
	Select(conds ...field.Expr) IThirdShopeeOrderDo
	Where(conds ...gen.Condition) IThirdShopeeOrderDo
	Order(conds ...field.Expr) IThirdShopeeOrderDo
	Distinct(cols ...field.Expr) IThirdShopeeOrderDo
	Omit(cols ...field.Expr) IThirdShopeeOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo
	Group(cols ...field.Expr) IThirdShopeeOrderDo
	Having(conds ...gen.Condition) IThirdShopeeOrderDo
	Limit(limit int) IThirdShopeeOrderDo
	Offset(offset int) IThirdShopeeOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopeeOrderDo
	Unscoped() IThirdShopeeOrderDo
	Create(values ...*model.ThirdShopeeOrder) error
	CreateInBatches(values model.ThirdShopeeOrderSlice, batchSize int) error
	Save(values ...*model.ThirdShopeeOrder) error
	First() (*model.ThirdShopeeOrder, error)
	Take() (*model.ThirdShopeeOrder, error)
	Last() (*model.ThirdShopeeOrder, error)
	Find() (model.ThirdShopeeOrderSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopeeOrderSlice, err error)
	FindInBatches(result *model.ThirdShopeeOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdShopeeOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdShopeeOrderDo
	Assign(attrs ...field.AssignExpr) IThirdShopeeOrderDo
	Joins(fields ...field.RelationField) IThirdShopeeOrderDo
	Preload(fields ...field.RelationField) IThirdShopeeOrderDo
	FirstOrInit() (*model.ThirdShopeeOrder, error)
	FirstOrCreate() (*model.ThirdShopeeOrder, error)
	FindByPage(offset int, limit int) (result model.ThirdShopeeOrderSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdShopeeOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdShopeeOrderDo) Debug() IThirdShopeeOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdShopeeOrderDo) WithContext(ctx context.Context) IThirdShopeeOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdShopeeOrderDo) ReadDB() IThirdShopeeOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdShopeeOrderDo) WriteDB() IThirdShopeeOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdShopeeOrderDo) Session(config *gorm.Session) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdShopeeOrderDo) Clauses(conds ...clause.Expression) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdShopeeOrderDo) Returning(value interface{}, columns ...string) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdShopeeOrderDo) Not(conds ...gen.Condition) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdShopeeOrderDo) Or(conds ...gen.Condition) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdShopeeOrderDo) Select(conds ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdShopeeOrderDo) Where(conds ...gen.Condition) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdShopeeOrderDo) Order(conds ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdShopeeOrderDo) Distinct(cols ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdShopeeOrderDo) Omit(cols ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdShopeeOrderDo) Join(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdShopeeOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdShopeeOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdShopeeOrderDo) Group(cols ...field.Expr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdShopeeOrderDo) Having(conds ...gen.Condition) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdShopeeOrderDo) Limit(limit int) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdShopeeOrderDo) Offset(offset int) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdShopeeOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdShopeeOrderDo) Unscoped() IThirdShopeeOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdShopeeOrderDo) Create(values ...*model.ThirdShopeeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdShopeeOrderDo) CreateInBatches(values model.ThirdShopeeOrderSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdShopeeOrderDo) Save(values ...*model.ThirdShopeeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdShopeeOrderDo) First() (*model.ThirdShopeeOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeOrder), nil
	}
}

func (t thirdShopeeOrderDo) Take() (*model.ThirdShopeeOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeOrder), nil
	}
}

func (t thirdShopeeOrderDo) Last() (*model.ThirdShopeeOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeOrder), nil
	}
}

func (t thirdShopeeOrderDo) Find() (model.ThirdShopeeOrderSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdShopeeOrderSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdShopeeOrder); ok {
		return model.ThirdShopeeOrderSlice(slice), err
	}
	return model.ThirdShopeeOrderSlice{}, err

}

func (t thirdShopeeOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopeeOrderSlice, err error) {
	buf := make([]*model.ThirdShopeeOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdShopeeOrderDo) FindInBatches(result *model.ThirdShopeeOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdShopeeOrderDo) Attrs(attrs ...field.AssignExpr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdShopeeOrderDo) Assign(attrs ...field.AssignExpr) IThirdShopeeOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdShopeeOrderDo) Joins(fields ...field.RelationField) IThirdShopeeOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdShopeeOrderDo) Preload(fields ...field.RelationField) IThirdShopeeOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdShopeeOrderDo) FirstOrInit() (*model.ThirdShopeeOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeOrder), nil
	}
}

func (t thirdShopeeOrderDo) FirstOrCreate() (*model.ThirdShopeeOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeOrder), nil
	}
}

func (t thirdShopeeOrderDo) FindByPage(offset int, limit int) (result model.ThirdShopeeOrderSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdShopeeOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdShopeeOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdShopeeOrderDo) Delete(models ...*model.ThirdShopeeOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdShopeeOrderDo) withDO(do gen.Dao) *thirdShopeeOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
