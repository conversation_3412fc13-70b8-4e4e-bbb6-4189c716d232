// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdShoplineProduct(db *gorm.DB, opts ...gen.DOOption) thirdShoplineProduct {
	_thirdShoplineProduct := thirdShoplineProduct{}

	_thirdShoplineProduct.thirdShoplineProductDo.UseDB(db, opts...)
	_thirdShoplineProduct.thirdShoplineProductDo.UseModel(&model.ThirdShoplineProduct{})

	tableName := _thirdShoplineProduct.thirdShoplineProductDo.TableName()
	_thirdShoplineProduct.ALL = field.NewAsterisk(tableName)
	_thirdShoplineProduct.ID = field.NewString(tableName, "id")
	_thirdShoplineProduct.InventoryItemID = field.NewString(tableName, "inventory_item_id")
	_thirdShoplineProduct.InventoryQuantity = field.NewInt32(tableName, "inventory_quantity")
	_thirdShoplineProduct.ProductID = field.NewString(tableName, "product_id")
	_thirdShoplineProduct.Sku = field.NewString(tableName, "sku")

	_thirdShoplineProduct.fillFieldMap()

	return _thirdShoplineProduct
}

type thirdShoplineProduct struct {
	thirdShoplineProductDo thirdShoplineProductDo

	ALL               field.Asterisk
	ID                field.String
	InventoryItemID   field.String
	InventoryQuantity field.Int32
	ProductID         field.String
	Sku               field.String

	fieldMap map[string]field.Expr
}

func (t thirdShoplineProduct) Table(newTableName string) *thirdShoplineProduct {
	t.thirdShoplineProductDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdShoplineProduct) As(alias string) *thirdShoplineProduct {
	t.thirdShoplineProductDo.DO = *(t.thirdShoplineProductDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdShoplineProduct) updateTableName(table string) *thirdShoplineProduct {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.InventoryItemID = field.NewString(table, "inventory_item_id")
	t.InventoryQuantity = field.NewInt32(table, "inventory_quantity")
	t.ProductID = field.NewString(table, "product_id")
	t.Sku = field.NewString(table, "sku")

	t.fillFieldMap()

	return t
}

func (t *thirdShoplineProduct) WithContext(ctx context.Context) IThirdShoplineProductDo {
	return t.thirdShoplineProductDo.WithContext(ctx)
}

func (t thirdShoplineProduct) TableName() string { return t.thirdShoplineProductDo.TableName() }

func (t thirdShoplineProduct) Alias() string { return t.thirdShoplineProductDo.Alias() }

func (t thirdShoplineProduct) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdShoplineProductDo.Columns(cols...)
}

func (t *thirdShoplineProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdShoplineProduct) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["id"] = t.ID
	t.fieldMap["inventory_item_id"] = t.InventoryItemID
	t.fieldMap["inventory_quantity"] = t.InventoryQuantity
	t.fieldMap["product_id"] = t.ProductID
	t.fieldMap["sku"] = t.Sku
}

func (t thirdShoplineProduct) clone(db *gorm.DB) thirdShoplineProduct {
	t.thirdShoplineProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdShoplineProduct) replaceDB(db *gorm.DB) thirdShoplineProduct {
	t.thirdShoplineProductDo.ReplaceDB(db)
	return t
}

type thirdShoplineProductDo struct{ gen.DO }

type IThirdShoplineProductDo interface {
	gen.SubQuery
	Debug() IThirdShoplineProductDo
	WithContext(ctx context.Context) IThirdShoplineProductDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdShoplineProductDo
	WriteDB() IThirdShoplineProductDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdShoplineProductDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdShoplineProductDo
	Not(conds ...gen.Condition) IThirdShoplineProductDo
	Or(conds ...gen.Condition) IThirdShoplineProductDo
	Select(conds ...field.Expr) IThirdShoplineProductDo
	Where(conds ...gen.Condition) IThirdShoplineProductDo
	Order(conds ...field.Expr) IThirdShoplineProductDo
	Distinct(cols ...field.Expr) IThirdShoplineProductDo
	Omit(cols ...field.Expr) IThirdShoplineProductDo
	Join(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo
	Group(cols ...field.Expr) IThirdShoplineProductDo
	Having(conds ...gen.Condition) IThirdShoplineProductDo
	Limit(limit int) IThirdShoplineProductDo
	Offset(offset int) IThirdShoplineProductDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShoplineProductDo
	Unscoped() IThirdShoplineProductDo
	Create(values ...*model.ThirdShoplineProduct) error
	CreateInBatches(values model.ThirdShoplineProductSlice, batchSize int) error
	Save(values ...*model.ThirdShoplineProduct) error
	First() (*model.ThirdShoplineProduct, error)
	Take() (*model.ThirdShoplineProduct, error)
	Last() (*model.ThirdShoplineProduct, error)
	Find() (model.ThirdShoplineProductSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShoplineProductSlice, err error)
	FindInBatches(result *model.ThirdShoplineProductSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdShoplineProduct) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdShoplineProductDo
	Assign(attrs ...field.AssignExpr) IThirdShoplineProductDo
	Joins(fields ...field.RelationField) IThirdShoplineProductDo
	Preload(fields ...field.RelationField) IThirdShoplineProductDo
	FirstOrInit() (*model.ThirdShoplineProduct, error)
	FirstOrCreate() (*model.ThirdShoplineProduct, error)
	FindByPage(offset int, limit int) (result model.ThirdShoplineProductSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdShoplineProductDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdShoplineProductDo) Debug() IThirdShoplineProductDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdShoplineProductDo) WithContext(ctx context.Context) IThirdShoplineProductDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdShoplineProductDo) ReadDB() IThirdShoplineProductDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdShoplineProductDo) WriteDB() IThirdShoplineProductDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdShoplineProductDo) Session(config *gorm.Session) IThirdShoplineProductDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdShoplineProductDo) Clauses(conds ...clause.Expression) IThirdShoplineProductDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdShoplineProductDo) Returning(value interface{}, columns ...string) IThirdShoplineProductDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdShoplineProductDo) Not(conds ...gen.Condition) IThirdShoplineProductDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdShoplineProductDo) Or(conds ...gen.Condition) IThirdShoplineProductDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdShoplineProductDo) Select(conds ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdShoplineProductDo) Where(conds ...gen.Condition) IThirdShoplineProductDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdShoplineProductDo) Order(conds ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdShoplineProductDo) Distinct(cols ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdShoplineProductDo) Omit(cols ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdShoplineProductDo) Join(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdShoplineProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdShoplineProductDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdShoplineProductDo) Group(cols ...field.Expr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdShoplineProductDo) Having(conds ...gen.Condition) IThirdShoplineProductDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdShoplineProductDo) Limit(limit int) IThirdShoplineProductDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdShoplineProductDo) Offset(offset int) IThirdShoplineProductDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdShoplineProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShoplineProductDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdShoplineProductDo) Unscoped() IThirdShoplineProductDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdShoplineProductDo) Create(values ...*model.ThirdShoplineProduct) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdShoplineProductDo) CreateInBatches(values model.ThirdShoplineProductSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdShoplineProductDo) Save(values ...*model.ThirdShoplineProduct) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdShoplineProductDo) First() (*model.ThirdShoplineProduct, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineProduct), nil
	}
}

func (t thirdShoplineProductDo) Take() (*model.ThirdShoplineProduct, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineProduct), nil
	}
}

func (t thirdShoplineProductDo) Last() (*model.ThirdShoplineProduct, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineProduct), nil
	}
}

func (t thirdShoplineProductDo) Find() (model.ThirdShoplineProductSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdShoplineProductSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdShoplineProduct); ok {
		return model.ThirdShoplineProductSlice(slice), err
	}
	return model.ThirdShoplineProductSlice{}, err

}

func (t thirdShoplineProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShoplineProductSlice, err error) {
	buf := make([]*model.ThirdShoplineProduct, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdShoplineProductDo) FindInBatches(result *model.ThirdShoplineProductSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdShoplineProductDo) Attrs(attrs ...field.AssignExpr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdShoplineProductDo) Assign(attrs ...field.AssignExpr) IThirdShoplineProductDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdShoplineProductDo) Joins(fields ...field.RelationField) IThirdShoplineProductDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdShoplineProductDo) Preload(fields ...field.RelationField) IThirdShoplineProductDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdShoplineProductDo) FirstOrInit() (*model.ThirdShoplineProduct, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineProduct), nil
	}
}

func (t thirdShoplineProductDo) FirstOrCreate() (*model.ThirdShoplineProduct, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineProduct), nil
	}
}

func (t thirdShoplineProductDo) FindByPage(offset int, limit int) (result model.ThirdShoplineProductSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdShoplineProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdShoplineProductDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdShoplineProductDo) Delete(models ...*model.ThirdShoplineProduct) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdShoplineProductDo) withDO(do gen.Dao) *thirdShoplineProductDo {
	t.DO = *do.(*gen.DO)
	return t
}
