// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseStockSync(db *gorm.DB, opts ...gen.DOOption) warehouseStockSync {
	_warehouseStockSync := warehouseStockSync{}

	_warehouseStockSync.warehouseStockSyncDo.UseDB(db, opts...)
	_warehouseStockSync.warehouseStockSyncDo.UseModel(&model.WarehouseStockSync{})

	tableName := _warehouseStockSync.warehouseStockSyncDo.TableName()
	_warehouseStockSync.ALL = field.NewAsterisk(tableName)
	_warehouseStockSync.ID = field.NewInt32(tableName, "id")
	_warehouseStockSync.WarehouseCode = field.NewString(tableName, "warehouse_code")
	_warehouseStockSync.NotifyRegion = field.NewString(tableName, "notify_region")
	_warehouseStockSync.CreateTime = field.NewTime(tableName, "create_time")
	_warehouseStockSync.UpdateTime = field.NewTime(tableName, "update_time")

	_warehouseStockSync.fillFieldMap()

	return _warehouseStockSync
}

type warehouseStockSync struct {
	warehouseStockSyncDo warehouseStockSyncDo

	ALL           field.Asterisk
	ID            field.Int32
	WarehouseCode field.String // 仓库编码
	NotifyRegion  field.String // 通知地区
	CreateTime    field.Time   // 更新时间
	UpdateTime    field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (w warehouseStockSync) Table(newTableName string) *warehouseStockSync {
	w.warehouseStockSyncDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseStockSync) As(alias string) *warehouseStockSync {
	w.warehouseStockSyncDo.DO = *(w.warehouseStockSyncDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseStockSync) updateTableName(table string) *warehouseStockSync {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.WarehouseCode = field.NewString(table, "warehouse_code")
	w.NotifyRegion = field.NewString(table, "notify_region")
	w.CreateTime = field.NewTime(table, "create_time")
	w.UpdateTime = field.NewTime(table, "update_time")

	w.fillFieldMap()

	return w
}

func (w *warehouseStockSync) WithContext(ctx context.Context) IWarehouseStockSyncDo {
	return w.warehouseStockSyncDo.WithContext(ctx)
}

func (w warehouseStockSync) TableName() string { return w.warehouseStockSyncDo.TableName() }

func (w warehouseStockSync) Alias() string { return w.warehouseStockSyncDo.Alias() }

func (w warehouseStockSync) Columns(cols ...field.Expr) gen.Columns {
	return w.warehouseStockSyncDo.Columns(cols...)
}

func (w *warehouseStockSync) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseStockSync) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 5)
	w.fieldMap["id"] = w.ID
	w.fieldMap["warehouse_code"] = w.WarehouseCode
	w.fieldMap["notify_region"] = w.NotifyRegion
	w.fieldMap["create_time"] = w.CreateTime
	w.fieldMap["update_time"] = w.UpdateTime
}

func (w warehouseStockSync) clone(db *gorm.DB) warehouseStockSync {
	w.warehouseStockSyncDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseStockSync) replaceDB(db *gorm.DB) warehouseStockSync {
	w.warehouseStockSyncDo.ReplaceDB(db)
	return w
}

type warehouseStockSyncDo struct{ gen.DO }

type IWarehouseStockSyncDo interface {
	gen.SubQuery
	Debug() IWarehouseStockSyncDo
	WithContext(ctx context.Context) IWarehouseStockSyncDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseStockSyncDo
	WriteDB() IWarehouseStockSyncDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseStockSyncDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseStockSyncDo
	Not(conds ...gen.Condition) IWarehouseStockSyncDo
	Or(conds ...gen.Condition) IWarehouseStockSyncDo
	Select(conds ...field.Expr) IWarehouseStockSyncDo
	Where(conds ...gen.Condition) IWarehouseStockSyncDo
	Order(conds ...field.Expr) IWarehouseStockSyncDo
	Distinct(cols ...field.Expr) IWarehouseStockSyncDo
	Omit(cols ...field.Expr) IWarehouseStockSyncDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo
	Group(cols ...field.Expr) IWarehouseStockSyncDo
	Having(conds ...gen.Condition) IWarehouseStockSyncDo
	Limit(limit int) IWarehouseStockSyncDo
	Offset(offset int) IWarehouseStockSyncDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseStockSyncDo
	Unscoped() IWarehouseStockSyncDo
	Create(values ...*model.WarehouseStockSync) error
	CreateInBatches(values model.WarehouseStockSyncSlice, batchSize int) error
	Save(values ...*model.WarehouseStockSync) error
	First() (*model.WarehouseStockSync, error)
	Take() (*model.WarehouseStockSync, error)
	Last() (*model.WarehouseStockSync, error)
	Find() (model.WarehouseStockSyncSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseStockSyncSlice, err error)
	FindInBatches(result *model.WarehouseStockSyncSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseStockSync) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseStockSyncDo
	Assign(attrs ...field.AssignExpr) IWarehouseStockSyncDo
	Joins(fields ...field.RelationField) IWarehouseStockSyncDo
	Preload(fields ...field.RelationField) IWarehouseStockSyncDo
	FirstOrInit() (*model.WarehouseStockSync, error)
	FirstOrCreate() (*model.WarehouseStockSync, error)
	FindByPage(offset int, limit int) (result model.WarehouseStockSyncSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseStockSyncDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseStockSyncDo) Debug() IWarehouseStockSyncDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseStockSyncDo) WithContext(ctx context.Context) IWarehouseStockSyncDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseStockSyncDo) ReadDB() IWarehouseStockSyncDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseStockSyncDo) WriteDB() IWarehouseStockSyncDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseStockSyncDo) Session(config *gorm.Session) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseStockSyncDo) Clauses(conds ...clause.Expression) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseStockSyncDo) Returning(value interface{}, columns ...string) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseStockSyncDo) Not(conds ...gen.Condition) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseStockSyncDo) Or(conds ...gen.Condition) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseStockSyncDo) Select(conds ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseStockSyncDo) Where(conds ...gen.Condition) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseStockSyncDo) Order(conds ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseStockSyncDo) Distinct(cols ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseStockSyncDo) Omit(cols ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseStockSyncDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseStockSyncDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseStockSyncDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseStockSyncDo) Group(cols ...field.Expr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseStockSyncDo) Having(conds ...gen.Condition) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseStockSyncDo) Limit(limit int) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseStockSyncDo) Offset(offset int) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseStockSyncDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseStockSyncDo) Unscoped() IWarehouseStockSyncDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseStockSyncDo) Create(values ...*model.WarehouseStockSync) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseStockSyncDo) CreateInBatches(values model.WarehouseStockSyncSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseStockSyncDo) Save(values ...*model.WarehouseStockSync) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseStockSyncDo) First() (*model.WarehouseStockSync, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseStockSync), nil
	}
}

func (w warehouseStockSyncDo) Take() (*model.WarehouseStockSync, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseStockSync), nil
	}
}

func (w warehouseStockSyncDo) Last() (*model.WarehouseStockSync, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseStockSync), nil
	}
}

func (w warehouseStockSyncDo) Find() (model.WarehouseStockSyncSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseStockSyncSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseStockSync); ok {
		return model.WarehouseStockSyncSlice(slice), err
	}
	return model.WarehouseStockSyncSlice{}, err

}

func (w warehouseStockSyncDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseStockSyncSlice, err error) {
	buf := make([]*model.WarehouseStockSync, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseStockSyncDo) FindInBatches(result *model.WarehouseStockSyncSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseStockSyncDo) Attrs(attrs ...field.AssignExpr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseStockSyncDo) Assign(attrs ...field.AssignExpr) IWarehouseStockSyncDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseStockSyncDo) Joins(fields ...field.RelationField) IWarehouseStockSyncDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseStockSyncDo) Preload(fields ...field.RelationField) IWarehouseStockSyncDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseStockSyncDo) FirstOrInit() (*model.WarehouseStockSync, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseStockSync), nil
	}
}

func (w warehouseStockSyncDo) FirstOrCreate() (*model.WarehouseStockSync, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseStockSync), nil
	}
}

func (w warehouseStockSyncDo) FindByPage(offset int, limit int) (result model.WarehouseStockSyncSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseStockSyncDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseStockSyncDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseStockSyncDo) Delete(models ...*model.WarehouseStockSync) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseStockSyncDo) withDO(do gen.Dao) *warehouseStockSyncDo {
	w.DO = *do.(*gen.DO)
	return w
}
