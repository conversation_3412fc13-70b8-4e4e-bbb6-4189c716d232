// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdShopeeEscrowDetail(db *gorm.DB, opts ...gen.DOOption) thirdShopeeEscrowDetail {
	_thirdShopeeEscrowDetail := thirdShopeeEscrowDetail{}

	_thirdShopeeEscrowDetail.thirdShopeeEscrowDetailDo.UseDB(db, opts...)
	_thirdShopeeEscrowDetail.thirdShopeeEscrowDetailDo.UseModel(&model.ThirdShopeeEscrowDetail{})

	tableName := _thirdShopeeEscrowDetail.thirdShopeeEscrowDetailDo.TableName()
	_thirdShopeeEscrowDetail.ALL = field.NewAsterisk(tableName)
	_thirdShopeeEscrowDetail.OrderSn = field.NewString(tableName, "order_sn")
	_thirdShopeeEscrowDetail.BuyerUserName = field.NewString(tableName, "buyer_user_name")
	_thirdShopeeEscrowDetail.OrderIncome = field.NewString(tableName, "order_income")
	_thirdShopeeEscrowDetail.ReturnOrderSnList = field.NewString(tableName, "return_order_sn_list")

	_thirdShopeeEscrowDetail.fillFieldMap()

	return _thirdShopeeEscrowDetail
}

type thirdShopeeEscrowDetail struct {
	thirdShopeeEscrowDetailDo thirdShopeeEscrowDetailDo

	ALL               field.Asterisk
	OrderSn           field.String
	BuyerUserName     field.String
	OrderIncome       field.String
	ReturnOrderSnList field.String

	fieldMap map[string]field.Expr
}

func (t thirdShopeeEscrowDetail) Table(newTableName string) *thirdShopeeEscrowDetail {
	t.thirdShopeeEscrowDetailDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdShopeeEscrowDetail) As(alias string) *thirdShopeeEscrowDetail {
	t.thirdShopeeEscrowDetailDo.DO = *(t.thirdShopeeEscrowDetailDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdShopeeEscrowDetail) updateTableName(table string) *thirdShopeeEscrowDetail {
	t.ALL = field.NewAsterisk(table)
	t.OrderSn = field.NewString(table, "order_sn")
	t.BuyerUserName = field.NewString(table, "buyer_user_name")
	t.OrderIncome = field.NewString(table, "order_income")
	t.ReturnOrderSnList = field.NewString(table, "return_order_sn_list")

	t.fillFieldMap()

	return t
}

func (t *thirdShopeeEscrowDetail) WithContext(ctx context.Context) IThirdShopeeEscrowDetailDo {
	return t.thirdShopeeEscrowDetailDo.WithContext(ctx)
}

func (t thirdShopeeEscrowDetail) TableName() string { return t.thirdShopeeEscrowDetailDo.TableName() }

func (t thirdShopeeEscrowDetail) Alias() string { return t.thirdShopeeEscrowDetailDo.Alias() }

func (t thirdShopeeEscrowDetail) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdShopeeEscrowDetailDo.Columns(cols...)
}

func (t *thirdShopeeEscrowDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdShopeeEscrowDetail) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 4)
	t.fieldMap["order_sn"] = t.OrderSn
	t.fieldMap["buyer_user_name"] = t.BuyerUserName
	t.fieldMap["order_income"] = t.OrderIncome
	t.fieldMap["return_order_sn_list"] = t.ReturnOrderSnList
}

func (t thirdShopeeEscrowDetail) clone(db *gorm.DB) thirdShopeeEscrowDetail {
	t.thirdShopeeEscrowDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdShopeeEscrowDetail) replaceDB(db *gorm.DB) thirdShopeeEscrowDetail {
	t.thirdShopeeEscrowDetailDo.ReplaceDB(db)
	return t
}

type thirdShopeeEscrowDetailDo struct{ gen.DO }

type IThirdShopeeEscrowDetailDo interface {
	gen.SubQuery
	Debug() IThirdShopeeEscrowDetailDo
	WithContext(ctx context.Context) IThirdShopeeEscrowDetailDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdShopeeEscrowDetailDo
	WriteDB() IThirdShopeeEscrowDetailDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdShopeeEscrowDetailDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdShopeeEscrowDetailDo
	Not(conds ...gen.Condition) IThirdShopeeEscrowDetailDo
	Or(conds ...gen.Condition) IThirdShopeeEscrowDetailDo
	Select(conds ...field.Expr) IThirdShopeeEscrowDetailDo
	Where(conds ...gen.Condition) IThirdShopeeEscrowDetailDo
	Order(conds ...field.Expr) IThirdShopeeEscrowDetailDo
	Distinct(cols ...field.Expr) IThirdShopeeEscrowDetailDo
	Omit(cols ...field.Expr) IThirdShopeeEscrowDetailDo
	Join(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo
	Group(cols ...field.Expr) IThirdShopeeEscrowDetailDo
	Having(conds ...gen.Condition) IThirdShopeeEscrowDetailDo
	Limit(limit int) IThirdShopeeEscrowDetailDo
	Offset(offset int) IThirdShopeeEscrowDetailDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopeeEscrowDetailDo
	Unscoped() IThirdShopeeEscrowDetailDo
	Create(values ...*model.ThirdShopeeEscrowDetail) error
	CreateInBatches(values model.ThirdShopeeEscrowDetailSlice, batchSize int) error
	Save(values ...*model.ThirdShopeeEscrowDetail) error
	First() (*model.ThirdShopeeEscrowDetail, error)
	Take() (*model.ThirdShopeeEscrowDetail, error)
	Last() (*model.ThirdShopeeEscrowDetail, error)
	Find() (model.ThirdShopeeEscrowDetailSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopeeEscrowDetailSlice, err error)
	FindInBatches(result *model.ThirdShopeeEscrowDetailSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdShopeeEscrowDetail) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdShopeeEscrowDetailDo
	Assign(attrs ...field.AssignExpr) IThirdShopeeEscrowDetailDo
	Joins(fields ...field.RelationField) IThirdShopeeEscrowDetailDo
	Preload(fields ...field.RelationField) IThirdShopeeEscrowDetailDo
	FirstOrInit() (*model.ThirdShopeeEscrowDetail, error)
	FirstOrCreate() (*model.ThirdShopeeEscrowDetail, error)
	FindByPage(offset int, limit int) (result model.ThirdShopeeEscrowDetailSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdShopeeEscrowDetailDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdShopeeEscrowDetailDo) Debug() IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdShopeeEscrowDetailDo) WithContext(ctx context.Context) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdShopeeEscrowDetailDo) ReadDB() IThirdShopeeEscrowDetailDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdShopeeEscrowDetailDo) WriteDB() IThirdShopeeEscrowDetailDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdShopeeEscrowDetailDo) Session(config *gorm.Session) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdShopeeEscrowDetailDo) Clauses(conds ...clause.Expression) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdShopeeEscrowDetailDo) Returning(value interface{}, columns ...string) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdShopeeEscrowDetailDo) Not(conds ...gen.Condition) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdShopeeEscrowDetailDo) Or(conds ...gen.Condition) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdShopeeEscrowDetailDo) Select(conds ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdShopeeEscrowDetailDo) Where(conds ...gen.Condition) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdShopeeEscrowDetailDo) Order(conds ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdShopeeEscrowDetailDo) Distinct(cols ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdShopeeEscrowDetailDo) Omit(cols ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdShopeeEscrowDetailDo) Join(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdShopeeEscrowDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdShopeeEscrowDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdShopeeEscrowDetailDo) Group(cols ...field.Expr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdShopeeEscrowDetailDo) Having(conds ...gen.Condition) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdShopeeEscrowDetailDo) Limit(limit int) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdShopeeEscrowDetailDo) Offset(offset int) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdShopeeEscrowDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdShopeeEscrowDetailDo) Unscoped() IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdShopeeEscrowDetailDo) Create(values ...*model.ThirdShopeeEscrowDetail) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdShopeeEscrowDetailDo) CreateInBatches(values model.ThirdShopeeEscrowDetailSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdShopeeEscrowDetailDo) Save(values ...*model.ThirdShopeeEscrowDetail) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdShopeeEscrowDetailDo) First() (*model.ThirdShopeeEscrowDetail, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeEscrowDetail), nil
	}
}

func (t thirdShopeeEscrowDetailDo) Take() (*model.ThirdShopeeEscrowDetail, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeEscrowDetail), nil
	}
}

func (t thirdShopeeEscrowDetailDo) Last() (*model.ThirdShopeeEscrowDetail, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeEscrowDetail), nil
	}
}

func (t thirdShopeeEscrowDetailDo) Find() (model.ThirdShopeeEscrowDetailSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdShopeeEscrowDetailSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdShopeeEscrowDetail); ok {
		return model.ThirdShopeeEscrowDetailSlice(slice), err
	}
	return model.ThirdShopeeEscrowDetailSlice{}, err

}

func (t thirdShopeeEscrowDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopeeEscrowDetailSlice, err error) {
	buf := make([]*model.ThirdShopeeEscrowDetail, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdShopeeEscrowDetailDo) FindInBatches(result *model.ThirdShopeeEscrowDetailSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdShopeeEscrowDetailDo) Attrs(attrs ...field.AssignExpr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdShopeeEscrowDetailDo) Assign(attrs ...field.AssignExpr) IThirdShopeeEscrowDetailDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdShopeeEscrowDetailDo) Joins(fields ...field.RelationField) IThirdShopeeEscrowDetailDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdShopeeEscrowDetailDo) Preload(fields ...field.RelationField) IThirdShopeeEscrowDetailDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdShopeeEscrowDetailDo) FirstOrInit() (*model.ThirdShopeeEscrowDetail, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeEscrowDetail), nil
	}
}

func (t thirdShopeeEscrowDetailDo) FirstOrCreate() (*model.ThirdShopeeEscrowDetail, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShopeeEscrowDetail), nil
	}
}

func (t thirdShopeeEscrowDetailDo) FindByPage(offset int, limit int) (result model.ThirdShopeeEscrowDetailSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdShopeeEscrowDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdShopeeEscrowDetailDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdShopeeEscrowDetailDo) Delete(models ...*model.ThirdShopeeEscrowDetail) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdShopeeEscrowDetailDo) withDO(do gen.Dao) *thirdShopeeEscrowDetailDo {
	t.DO = *do.(*gen.DO)
	return t
}
