// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdShop(db *gorm.DB, opts ...gen.DOOption) thirdShop {
	_thirdShop := thirdShop{}

	_thirdShop.thirdShopDo.UseDB(db, opts...)
	_thirdShop.thirdShopDo.UseModel(&model.ThirdShop{})

	tableName := _thirdShop.thirdShopDo.TableName()
	_thirdShop.ALL = field.NewAsterisk(tableName)
	_thirdShop.ID = field.NewInt32(tableName, "id")
	_thirdShop.Platform = field.NewString(tableName, "platform")
	_thirdShop.ShopKey = field.NewString(tableName, "shop_key")
	_thirdShop.ShopName = field.NewString(tableName, "shop_name")
	_thirdShop.PlatformShopID = field.NewString(tableName, "platform_shop_id")
	_thirdShop.Currency = field.NewString(tableName, "currency")
	_thirdShop.Pro = field.NewBool(tableName, "pro")
	_thirdShop.MultiWarehouseFlag = field.NewInt32(tableName, "multi_warehouse_flag")
	_thirdShop.AppKey = field.NewString(tableName, "app_key")
	_thirdShop.Status = field.NewString(tableName, "status")

	_thirdShop.fillFieldMap()

	return _thirdShop
}

type thirdShop struct {
	thirdShopDo thirdShopDo

	ALL                field.Asterisk
	ID                 field.Int32
	Platform           field.String
	ShopKey            field.String
	ShopName           field.String
	PlatformShopID     field.String
	Currency           field.String
	Pro                field.Bool
	MultiWarehouseFlag field.Int32 // 是否开通多仓；0-否，1-是
	AppKey             field.String
	Status             field.String // 店铺状态: enable/disable

	fieldMap map[string]field.Expr
}

func (t thirdShop) Table(newTableName string) *thirdShop {
	t.thirdShopDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdShop) As(alias string) *thirdShop {
	t.thirdShopDo.DO = *(t.thirdShopDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdShop) updateTableName(table string) *thirdShop {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.Platform = field.NewString(table, "platform")
	t.ShopKey = field.NewString(table, "shop_key")
	t.ShopName = field.NewString(table, "shop_name")
	t.PlatformShopID = field.NewString(table, "platform_shop_id")
	t.Currency = field.NewString(table, "currency")
	t.Pro = field.NewBool(table, "pro")
	t.MultiWarehouseFlag = field.NewInt32(table, "multi_warehouse_flag")
	t.AppKey = field.NewString(table, "app_key")
	t.Status = field.NewString(table, "status")

	t.fillFieldMap()

	return t
}

func (t *thirdShop) WithContext(ctx context.Context) IThirdShopDo {
	return t.thirdShopDo.WithContext(ctx)
}

func (t thirdShop) TableName() string { return t.thirdShopDo.TableName() }

func (t thirdShop) Alias() string { return t.thirdShopDo.Alias() }

func (t thirdShop) Columns(cols ...field.Expr) gen.Columns { return t.thirdShopDo.Columns(cols...) }

func (t *thirdShop) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdShop) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["id"] = t.ID
	t.fieldMap["platform"] = t.Platform
	t.fieldMap["shop_key"] = t.ShopKey
	t.fieldMap["shop_name"] = t.ShopName
	t.fieldMap["platform_shop_id"] = t.PlatformShopID
	t.fieldMap["currency"] = t.Currency
	t.fieldMap["pro"] = t.Pro
	t.fieldMap["multi_warehouse_flag"] = t.MultiWarehouseFlag
	t.fieldMap["app_key"] = t.AppKey
	t.fieldMap["status"] = t.Status
}

func (t thirdShop) clone(db *gorm.DB) thirdShop {
	t.thirdShopDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdShop) replaceDB(db *gorm.DB) thirdShop {
	t.thirdShopDo.ReplaceDB(db)
	return t
}

type thirdShopDo struct{ gen.DO }

type IThirdShopDo interface {
	gen.SubQuery
	Debug() IThirdShopDo
	WithContext(ctx context.Context) IThirdShopDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdShopDo
	WriteDB() IThirdShopDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdShopDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdShopDo
	Not(conds ...gen.Condition) IThirdShopDo
	Or(conds ...gen.Condition) IThirdShopDo
	Select(conds ...field.Expr) IThirdShopDo
	Where(conds ...gen.Condition) IThirdShopDo
	Order(conds ...field.Expr) IThirdShopDo
	Distinct(cols ...field.Expr) IThirdShopDo
	Omit(cols ...field.Expr) IThirdShopDo
	Join(table schema.Tabler, on ...field.Expr) IThirdShopDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopDo
	Group(cols ...field.Expr) IThirdShopDo
	Having(conds ...gen.Condition) IThirdShopDo
	Limit(limit int) IThirdShopDo
	Offset(offset int) IThirdShopDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopDo
	Unscoped() IThirdShopDo
	Create(values ...*model.ThirdShop) error
	CreateInBatches(values model.ThirdShopSlice, batchSize int) error
	Save(values ...*model.ThirdShop) error
	First() (*model.ThirdShop, error)
	Take() (*model.ThirdShop, error)
	Last() (*model.ThirdShop, error)
	Find() (model.ThirdShopSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopSlice, err error)
	FindInBatches(result *model.ThirdShopSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdShop) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdShopDo
	Assign(attrs ...field.AssignExpr) IThirdShopDo
	Joins(fields ...field.RelationField) IThirdShopDo
	Preload(fields ...field.RelationField) IThirdShopDo
	FirstOrInit() (*model.ThirdShop, error)
	FirstOrCreate() (*model.ThirdShop, error)
	FindByPage(offset int, limit int) (result model.ThirdShopSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdShopDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdShopDo) Debug() IThirdShopDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdShopDo) WithContext(ctx context.Context) IThirdShopDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdShopDo) ReadDB() IThirdShopDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdShopDo) WriteDB() IThirdShopDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdShopDo) Session(config *gorm.Session) IThirdShopDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdShopDo) Clauses(conds ...clause.Expression) IThirdShopDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdShopDo) Returning(value interface{}, columns ...string) IThirdShopDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdShopDo) Not(conds ...gen.Condition) IThirdShopDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdShopDo) Or(conds ...gen.Condition) IThirdShopDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdShopDo) Select(conds ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdShopDo) Where(conds ...gen.Condition) IThirdShopDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdShopDo) Order(conds ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdShopDo) Distinct(cols ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdShopDo) Omit(cols ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdShopDo) Join(table schema.Tabler, on ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdShopDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdShopDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdShopDo) Group(cols ...field.Expr) IThirdShopDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdShopDo) Having(conds ...gen.Condition) IThirdShopDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdShopDo) Limit(limit int) IThirdShopDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdShopDo) Offset(offset int) IThirdShopDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdShopDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShopDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdShopDo) Unscoped() IThirdShopDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdShopDo) Create(values ...*model.ThirdShop) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdShopDo) CreateInBatches(values model.ThirdShopSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdShopDo) Save(values ...*model.ThirdShop) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdShopDo) First() (*model.ThirdShop, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShop), nil
	}
}

func (t thirdShopDo) Take() (*model.ThirdShop, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShop), nil
	}
}

func (t thirdShopDo) Last() (*model.ThirdShop, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShop), nil
	}
}

func (t thirdShopDo) Find() (model.ThirdShopSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdShopSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdShop); ok {
		return model.ThirdShopSlice(slice), err
	}
	return model.ThirdShopSlice{}, err

}

func (t thirdShopDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShopSlice, err error) {
	buf := make([]*model.ThirdShop, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdShopDo) FindInBatches(result *model.ThirdShopSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdShopDo) Attrs(attrs ...field.AssignExpr) IThirdShopDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdShopDo) Assign(attrs ...field.AssignExpr) IThirdShopDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdShopDo) Joins(fields ...field.RelationField) IThirdShopDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdShopDo) Preload(fields ...field.RelationField) IThirdShopDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdShopDo) FirstOrInit() (*model.ThirdShop, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShop), nil
	}
}

func (t thirdShopDo) FirstOrCreate() (*model.ThirdShop, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShop), nil
	}
}

func (t thirdShopDo) FindByPage(offset int, limit int) (result model.ThirdShopSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdShopDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdShopDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdShopDo) Delete(models ...*model.ThirdShop) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdShopDo) withDO(do gen.Dao) *thirdShopDo {
	t.DO = *do.(*gen.DO)
	return t
}
