// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockFreezeLog(db *gorm.DB, opts ...gen.DOOption) stockFreezeLog {
	_stockFreezeLog := stockFreezeLog{}

	_stockFreezeLog.stockFreezeLogDo.UseDB(db, opts...)
	_stockFreezeLog.stockFreezeLogDo.UseModel(&model.StockFreezeLog{})

	tableName := _stockFreezeLog.stockFreezeLogDo.TableName()
	_stockFreezeLog.ALL = field.NewAsterisk(tableName)
	_stockFreezeLog.ID = field.NewInt32(tableName, "id")
	_stockFreezeLog.Sku = field.NewString(tableName, "sku")
	_stockFreezeLog.Num = field.NewInt32(tableName, "num")
	_stockFreezeLog.OrderID = field.NewString(tableName, "order_id")
	_stockFreezeLog.QuantityAfterChange = field.NewInt32(tableName, "quantity_after_change")
	_stockFreezeLog.CreatedTime = field.NewTime(tableName, "created_time")
	_stockFreezeLog.Valid = field.NewBool(tableName, "valid")
	_stockFreezeLog.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_stockFreezeLog.fillFieldMap()

	return _stockFreezeLog
}

type stockFreezeLog struct {
	stockFreezeLogDo stockFreezeLogDo

	ALL                 field.Asterisk
	ID                  field.Int32
	Sku                 field.String
	Num                 field.Int32
	OrderID             field.String
	QuantityAfterChange field.Int32
	CreatedTime         field.Time
	Valid               field.Bool
	WarehouseCode       field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (s stockFreezeLog) Table(newTableName string) *stockFreezeLog {
	s.stockFreezeLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockFreezeLog) As(alias string) *stockFreezeLog {
	s.stockFreezeLogDo.DO = *(s.stockFreezeLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockFreezeLog) updateTableName(table string) *stockFreezeLog {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Sku = field.NewString(table, "sku")
	s.Num = field.NewInt32(table, "num")
	s.OrderID = field.NewString(table, "order_id")
	s.QuantityAfterChange = field.NewInt32(table, "quantity_after_change")
	s.CreatedTime = field.NewTime(table, "created_time")
	s.Valid = field.NewBool(table, "valid")
	s.WarehouseCode = field.NewString(table, "warehouse_code")

	s.fillFieldMap()

	return s
}

func (s *stockFreezeLog) WithContext(ctx context.Context) IStockFreezeLogDo {
	return s.stockFreezeLogDo.WithContext(ctx)
}

func (s stockFreezeLog) TableName() string { return s.stockFreezeLogDo.TableName() }

func (s stockFreezeLog) Alias() string { return s.stockFreezeLogDo.Alias() }

func (s stockFreezeLog) Columns(cols ...field.Expr) gen.Columns {
	return s.stockFreezeLogDo.Columns(cols...)
}

func (s *stockFreezeLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockFreezeLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["num"] = s.Num
	s.fieldMap["order_id"] = s.OrderID
	s.fieldMap["quantity_after_change"] = s.QuantityAfterChange
	s.fieldMap["created_time"] = s.CreatedTime
	s.fieldMap["valid"] = s.Valid
	s.fieldMap["warehouse_code"] = s.WarehouseCode
}

func (s stockFreezeLog) clone(db *gorm.DB) stockFreezeLog {
	s.stockFreezeLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockFreezeLog) replaceDB(db *gorm.DB) stockFreezeLog {
	s.stockFreezeLogDo.ReplaceDB(db)
	return s
}

type stockFreezeLogDo struct{ gen.DO }

type IStockFreezeLogDo interface {
	gen.SubQuery
	Debug() IStockFreezeLogDo
	WithContext(ctx context.Context) IStockFreezeLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockFreezeLogDo
	WriteDB() IStockFreezeLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockFreezeLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockFreezeLogDo
	Not(conds ...gen.Condition) IStockFreezeLogDo
	Or(conds ...gen.Condition) IStockFreezeLogDo
	Select(conds ...field.Expr) IStockFreezeLogDo
	Where(conds ...gen.Condition) IStockFreezeLogDo
	Order(conds ...field.Expr) IStockFreezeLogDo
	Distinct(cols ...field.Expr) IStockFreezeLogDo
	Omit(cols ...field.Expr) IStockFreezeLogDo
	Join(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo
	Group(cols ...field.Expr) IStockFreezeLogDo
	Having(conds ...gen.Condition) IStockFreezeLogDo
	Limit(limit int) IStockFreezeLogDo
	Offset(offset int) IStockFreezeLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockFreezeLogDo
	Unscoped() IStockFreezeLogDo
	Create(values ...*model.StockFreezeLog) error
	CreateInBatches(values model.StockFreezeLogSlice, batchSize int) error
	Save(values ...*model.StockFreezeLog) error
	First() (*model.StockFreezeLog, error)
	Take() (*model.StockFreezeLog, error)
	Last() (*model.StockFreezeLog, error)
	Find() (model.StockFreezeLogSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockFreezeLogSlice, err error)
	FindInBatches(result *model.StockFreezeLogSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockFreezeLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockFreezeLogDo
	Assign(attrs ...field.AssignExpr) IStockFreezeLogDo
	Joins(fields ...field.RelationField) IStockFreezeLogDo
	Preload(fields ...field.RelationField) IStockFreezeLogDo
	FirstOrInit() (*model.StockFreezeLog, error)
	FirstOrCreate() (*model.StockFreezeLog, error)
	FindByPage(offset int, limit int) (result model.StockFreezeLogSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockFreezeLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockFreezeLogDo) Debug() IStockFreezeLogDo {
	return s.withDO(s.DO.Debug())
}

func (s stockFreezeLogDo) WithContext(ctx context.Context) IStockFreezeLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockFreezeLogDo) ReadDB() IStockFreezeLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockFreezeLogDo) WriteDB() IStockFreezeLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockFreezeLogDo) Session(config *gorm.Session) IStockFreezeLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockFreezeLogDo) Clauses(conds ...clause.Expression) IStockFreezeLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockFreezeLogDo) Returning(value interface{}, columns ...string) IStockFreezeLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockFreezeLogDo) Not(conds ...gen.Condition) IStockFreezeLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockFreezeLogDo) Or(conds ...gen.Condition) IStockFreezeLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockFreezeLogDo) Select(conds ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockFreezeLogDo) Where(conds ...gen.Condition) IStockFreezeLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockFreezeLogDo) Order(conds ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockFreezeLogDo) Distinct(cols ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockFreezeLogDo) Omit(cols ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockFreezeLogDo) Join(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockFreezeLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockFreezeLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockFreezeLogDo) Group(cols ...field.Expr) IStockFreezeLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockFreezeLogDo) Having(conds ...gen.Condition) IStockFreezeLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockFreezeLogDo) Limit(limit int) IStockFreezeLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockFreezeLogDo) Offset(offset int) IStockFreezeLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockFreezeLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockFreezeLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockFreezeLogDo) Unscoped() IStockFreezeLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockFreezeLogDo) Create(values ...*model.StockFreezeLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockFreezeLogDo) CreateInBatches(values model.StockFreezeLogSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockFreezeLogDo) Save(values ...*model.StockFreezeLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockFreezeLogDo) First() (*model.StockFreezeLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockFreezeLog), nil
	}
}

func (s stockFreezeLogDo) Take() (*model.StockFreezeLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockFreezeLog), nil
	}
}

func (s stockFreezeLogDo) Last() (*model.StockFreezeLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockFreezeLog), nil
	}
}

func (s stockFreezeLogDo) Find() (model.StockFreezeLogSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockFreezeLogSlice{}, err
	}
	if slice, ok := result.([]*model.StockFreezeLog); ok {
		return model.StockFreezeLogSlice(slice), err
	}
	return model.StockFreezeLogSlice{}, err

}

func (s stockFreezeLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockFreezeLogSlice, err error) {
	buf := make([]*model.StockFreezeLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockFreezeLogDo) FindInBatches(result *model.StockFreezeLogSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockFreezeLogDo) Attrs(attrs ...field.AssignExpr) IStockFreezeLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockFreezeLogDo) Assign(attrs ...field.AssignExpr) IStockFreezeLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockFreezeLogDo) Joins(fields ...field.RelationField) IStockFreezeLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockFreezeLogDo) Preload(fields ...field.RelationField) IStockFreezeLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockFreezeLogDo) FirstOrInit() (*model.StockFreezeLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockFreezeLog), nil
	}
}

func (s stockFreezeLogDo) FirstOrCreate() (*model.StockFreezeLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockFreezeLog), nil
	}
}

func (s stockFreezeLogDo) FindByPage(offset int, limit int) (result model.StockFreezeLogSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockFreezeLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockFreezeLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockFreezeLogDo) Delete(models ...*model.StockFreezeLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockFreezeLogDo) withDO(do gen.Dao) *stockFreezeLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
