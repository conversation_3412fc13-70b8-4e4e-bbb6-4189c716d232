// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockSyncRetry(db *gorm.DB, opts ...gen.DOOption) stockSyncRetry {
	_stockSyncRetry := stockSyncRetry{}

	_stockSyncRetry.stockSyncRetryDo.UseDB(db, opts...)
	_stockSyncRetry.stockSyncRetryDo.UseModel(&model.StockSyncRetry{})

	tableName := _stockSyncRetry.stockSyncRetryDo.TableName()
	_stockSyncRetry.ALL = field.NewAsterisk(tableName)
	_stockSyncRetry.ID = field.NewInt32(tableName, "id")
	_stockSyncRetry.StrategyID = field.NewInt32(tableName, "strategy_id")
	_stockSyncRetry.Stock = field.NewInt32(tableName, "stock")
	_stockSyncRetry.Sku = field.NewString(tableName, "sku")
	_stockSyncRetry.ShopID = field.NewInt32(tableName, "shop_id")
	_stockSyncRetry.ProductID = field.NewString(tableName, "product_id")
	_stockSyncRetry.SkuID = field.NewString(tableName, "sku_id")
	_stockSyncRetry.CreatedTime = field.NewTime(tableName, "created_time")

	_stockSyncRetry.fillFieldMap()

	return _stockSyncRetry
}

type stockSyncRetry struct {
	stockSyncRetryDo stockSyncRetryDo

	ALL         field.Asterisk
	ID          field.Int32
	StrategyID  field.Int32
	Stock       field.Int32
	Sku         field.String
	ShopID      field.Int32
	ProductID   field.String
	SkuID       field.String
	CreatedTime field.Time

	fieldMap map[string]field.Expr
}

func (s stockSyncRetry) Table(newTableName string) *stockSyncRetry {
	s.stockSyncRetryDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockSyncRetry) As(alias string) *stockSyncRetry {
	s.stockSyncRetryDo.DO = *(s.stockSyncRetryDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockSyncRetry) updateTableName(table string) *stockSyncRetry {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.StrategyID = field.NewInt32(table, "strategy_id")
	s.Stock = field.NewInt32(table, "stock")
	s.Sku = field.NewString(table, "sku")
	s.ShopID = field.NewInt32(table, "shop_id")
	s.ProductID = field.NewString(table, "product_id")
	s.SkuID = field.NewString(table, "sku_id")
	s.CreatedTime = field.NewTime(table, "created_time")

	s.fillFieldMap()

	return s
}

func (s *stockSyncRetry) WithContext(ctx context.Context) IStockSyncRetryDo {
	return s.stockSyncRetryDo.WithContext(ctx)
}

func (s stockSyncRetry) TableName() string { return s.stockSyncRetryDo.TableName() }

func (s stockSyncRetry) Alias() string { return s.stockSyncRetryDo.Alias() }

func (s stockSyncRetry) Columns(cols ...field.Expr) gen.Columns {
	return s.stockSyncRetryDo.Columns(cols...)
}

func (s *stockSyncRetry) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockSyncRetry) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["strategy_id"] = s.StrategyID
	s.fieldMap["stock"] = s.Stock
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["shop_id"] = s.ShopID
	s.fieldMap["product_id"] = s.ProductID
	s.fieldMap["sku_id"] = s.SkuID
	s.fieldMap["created_time"] = s.CreatedTime
}

func (s stockSyncRetry) clone(db *gorm.DB) stockSyncRetry {
	s.stockSyncRetryDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockSyncRetry) replaceDB(db *gorm.DB) stockSyncRetry {
	s.stockSyncRetryDo.ReplaceDB(db)
	return s
}

type stockSyncRetryDo struct{ gen.DO }

type IStockSyncRetryDo interface {
	gen.SubQuery
	Debug() IStockSyncRetryDo
	WithContext(ctx context.Context) IStockSyncRetryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockSyncRetryDo
	WriteDB() IStockSyncRetryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockSyncRetryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockSyncRetryDo
	Not(conds ...gen.Condition) IStockSyncRetryDo
	Or(conds ...gen.Condition) IStockSyncRetryDo
	Select(conds ...field.Expr) IStockSyncRetryDo
	Where(conds ...gen.Condition) IStockSyncRetryDo
	Order(conds ...field.Expr) IStockSyncRetryDo
	Distinct(cols ...field.Expr) IStockSyncRetryDo
	Omit(cols ...field.Expr) IStockSyncRetryDo
	Join(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo
	Group(cols ...field.Expr) IStockSyncRetryDo
	Having(conds ...gen.Condition) IStockSyncRetryDo
	Limit(limit int) IStockSyncRetryDo
	Offset(offset int) IStockSyncRetryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockSyncRetryDo
	Unscoped() IStockSyncRetryDo
	Create(values ...*model.StockSyncRetry) error
	CreateInBatches(values model.StockSyncRetrySlice, batchSize int) error
	Save(values ...*model.StockSyncRetry) error
	First() (*model.StockSyncRetry, error)
	Take() (*model.StockSyncRetry, error)
	Last() (*model.StockSyncRetry, error)
	Find() (model.StockSyncRetrySlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockSyncRetrySlice, err error)
	FindInBatches(result *model.StockSyncRetrySlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockSyncRetry) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockSyncRetryDo
	Assign(attrs ...field.AssignExpr) IStockSyncRetryDo
	Joins(fields ...field.RelationField) IStockSyncRetryDo
	Preload(fields ...field.RelationField) IStockSyncRetryDo
	FirstOrInit() (*model.StockSyncRetry, error)
	FirstOrCreate() (*model.StockSyncRetry, error)
	FindByPage(offset int, limit int) (result model.StockSyncRetrySlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockSyncRetryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockSyncRetryDo) Debug() IStockSyncRetryDo {
	return s.withDO(s.DO.Debug())
}

func (s stockSyncRetryDo) WithContext(ctx context.Context) IStockSyncRetryDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockSyncRetryDo) ReadDB() IStockSyncRetryDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockSyncRetryDo) WriteDB() IStockSyncRetryDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockSyncRetryDo) Session(config *gorm.Session) IStockSyncRetryDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockSyncRetryDo) Clauses(conds ...clause.Expression) IStockSyncRetryDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockSyncRetryDo) Returning(value interface{}, columns ...string) IStockSyncRetryDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockSyncRetryDo) Not(conds ...gen.Condition) IStockSyncRetryDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockSyncRetryDo) Or(conds ...gen.Condition) IStockSyncRetryDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockSyncRetryDo) Select(conds ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockSyncRetryDo) Where(conds ...gen.Condition) IStockSyncRetryDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockSyncRetryDo) Order(conds ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockSyncRetryDo) Distinct(cols ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockSyncRetryDo) Omit(cols ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockSyncRetryDo) Join(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockSyncRetryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockSyncRetryDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockSyncRetryDo) Group(cols ...field.Expr) IStockSyncRetryDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockSyncRetryDo) Having(conds ...gen.Condition) IStockSyncRetryDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockSyncRetryDo) Limit(limit int) IStockSyncRetryDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockSyncRetryDo) Offset(offset int) IStockSyncRetryDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockSyncRetryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockSyncRetryDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockSyncRetryDo) Unscoped() IStockSyncRetryDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockSyncRetryDo) Create(values ...*model.StockSyncRetry) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockSyncRetryDo) CreateInBatches(values model.StockSyncRetrySlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockSyncRetryDo) Save(values ...*model.StockSyncRetry) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockSyncRetryDo) First() (*model.StockSyncRetry, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockSyncRetry), nil
	}
}

func (s stockSyncRetryDo) Take() (*model.StockSyncRetry, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockSyncRetry), nil
	}
}

func (s stockSyncRetryDo) Last() (*model.StockSyncRetry, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockSyncRetry), nil
	}
}

func (s stockSyncRetryDo) Find() (model.StockSyncRetrySlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockSyncRetrySlice{}, err
	}
	if slice, ok := result.([]*model.StockSyncRetry); ok {
		return model.StockSyncRetrySlice(slice), err
	}
	return model.StockSyncRetrySlice{}, err

}

func (s stockSyncRetryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockSyncRetrySlice, err error) {
	buf := make([]*model.StockSyncRetry, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockSyncRetryDo) FindInBatches(result *model.StockSyncRetrySlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockSyncRetryDo) Attrs(attrs ...field.AssignExpr) IStockSyncRetryDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockSyncRetryDo) Assign(attrs ...field.AssignExpr) IStockSyncRetryDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockSyncRetryDo) Joins(fields ...field.RelationField) IStockSyncRetryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockSyncRetryDo) Preload(fields ...field.RelationField) IStockSyncRetryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockSyncRetryDo) FirstOrInit() (*model.StockSyncRetry, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockSyncRetry), nil
	}
}

func (s stockSyncRetryDo) FirstOrCreate() (*model.StockSyncRetry, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockSyncRetry), nil
	}
}

func (s stockSyncRetryDo) FindByPage(offset int, limit int) (result model.StockSyncRetrySlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockSyncRetryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockSyncRetryDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockSyncRetryDo) Delete(models ...*model.StockSyncRetry) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockSyncRetryDo) withDO(do gen.Dao) *stockSyncRetryDo {
	s.DO = *do.(*gen.DO)
	return s
}
