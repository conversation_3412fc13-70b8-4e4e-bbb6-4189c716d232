// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockStrategyActive(db *gorm.DB, opts ...gen.DOOption) stockStrategyActive {
	_stockStrategyActive := stockStrategyActive{}

	_stockStrategyActive.stockStrategyActiveDo.UseDB(db, opts...)
	_stockStrategyActive.stockStrategyActiveDo.UseModel(&model.StockStrategyActive{})

	tableName := _stockStrategyActive.stockStrategyActiveDo.TableName()
	_stockStrategyActive.ALL = field.NewAsterisk(tableName)
	_stockStrategyActive.ID = field.NewInt32(tableName, "id")
	_stockStrategyActive.Sku = field.NewString(tableName, "sku")
	_stockStrategyActive.ShopID = field.NewInt32(tableName, "shop_id")
	_stockStrategyActive.StrategyID = field.NewInt32(tableName, "strategy_id")
	_stockStrategyActive.StrategyLevel = field.NewInt64(tableName, "strategy_level")
	_stockStrategyActive.UpdateTime = field.NewTime(tableName, "update_time")

	_stockStrategyActive.fillFieldMap()

	return _stockStrategyActive
}

type stockStrategyActive struct {
	stockStrategyActiveDo stockStrategyActiveDo

	ALL           field.Asterisk
	ID            field.Int32
	Sku           field.String
	ShopID        field.Int32
	StrategyID    field.Int32
	StrategyLevel field.Int64
	UpdateTime    field.Time

	fieldMap map[string]field.Expr
}

func (s stockStrategyActive) Table(newTableName string) *stockStrategyActive {
	s.stockStrategyActiveDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockStrategyActive) As(alias string) *stockStrategyActive {
	s.stockStrategyActiveDo.DO = *(s.stockStrategyActiveDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockStrategyActive) updateTableName(table string) *stockStrategyActive {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Sku = field.NewString(table, "sku")
	s.ShopID = field.NewInt32(table, "shop_id")
	s.StrategyID = field.NewInt32(table, "strategy_id")
	s.StrategyLevel = field.NewInt64(table, "strategy_level")
	s.UpdateTime = field.NewTime(table, "update_time")

	s.fillFieldMap()

	return s
}

func (s *stockStrategyActive) WithContext(ctx context.Context) IStockStrategyActiveDo {
	return s.stockStrategyActiveDo.WithContext(ctx)
}

func (s stockStrategyActive) TableName() string { return s.stockStrategyActiveDo.TableName() }

func (s stockStrategyActive) Alias() string { return s.stockStrategyActiveDo.Alias() }

func (s stockStrategyActive) Columns(cols ...field.Expr) gen.Columns {
	return s.stockStrategyActiveDo.Columns(cols...)
}

func (s *stockStrategyActive) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockStrategyActive) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["shop_id"] = s.ShopID
	s.fieldMap["strategy_id"] = s.StrategyID
	s.fieldMap["strategy_level"] = s.StrategyLevel
	s.fieldMap["update_time"] = s.UpdateTime
}

func (s stockStrategyActive) clone(db *gorm.DB) stockStrategyActive {
	s.stockStrategyActiveDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockStrategyActive) replaceDB(db *gorm.DB) stockStrategyActive {
	s.stockStrategyActiveDo.ReplaceDB(db)
	return s
}

type stockStrategyActiveDo struct{ gen.DO }

type IStockStrategyActiveDo interface {
	gen.SubQuery
	Debug() IStockStrategyActiveDo
	WithContext(ctx context.Context) IStockStrategyActiveDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockStrategyActiveDo
	WriteDB() IStockStrategyActiveDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockStrategyActiveDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockStrategyActiveDo
	Not(conds ...gen.Condition) IStockStrategyActiveDo
	Or(conds ...gen.Condition) IStockStrategyActiveDo
	Select(conds ...field.Expr) IStockStrategyActiveDo
	Where(conds ...gen.Condition) IStockStrategyActiveDo
	Order(conds ...field.Expr) IStockStrategyActiveDo
	Distinct(cols ...field.Expr) IStockStrategyActiveDo
	Omit(cols ...field.Expr) IStockStrategyActiveDo
	Join(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo
	Group(cols ...field.Expr) IStockStrategyActiveDo
	Having(conds ...gen.Condition) IStockStrategyActiveDo
	Limit(limit int) IStockStrategyActiveDo
	Offset(offset int) IStockStrategyActiveDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyActiveDo
	Unscoped() IStockStrategyActiveDo
	Create(values ...*model.StockStrategyActive) error
	CreateInBatches(values model.StockStrategyActiveSlice, batchSize int) error
	Save(values ...*model.StockStrategyActive) error
	First() (*model.StockStrategyActive, error)
	Take() (*model.StockStrategyActive, error)
	Last() (*model.StockStrategyActive, error)
	Find() (model.StockStrategyActiveSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategyActiveSlice, err error)
	FindInBatches(result *model.StockStrategyActiveSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockStrategyActive) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockStrategyActiveDo
	Assign(attrs ...field.AssignExpr) IStockStrategyActiveDo
	Joins(fields ...field.RelationField) IStockStrategyActiveDo
	Preload(fields ...field.RelationField) IStockStrategyActiveDo
	FirstOrInit() (*model.StockStrategyActive, error)
	FirstOrCreate() (*model.StockStrategyActive, error)
	FindByPage(offset int, limit int) (result model.StockStrategyActiveSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockStrategyActiveDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockStrategyActiveDo) Debug() IStockStrategyActiveDo {
	return s.withDO(s.DO.Debug())
}

func (s stockStrategyActiveDo) WithContext(ctx context.Context) IStockStrategyActiveDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockStrategyActiveDo) ReadDB() IStockStrategyActiveDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockStrategyActiveDo) WriteDB() IStockStrategyActiveDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockStrategyActiveDo) Session(config *gorm.Session) IStockStrategyActiveDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockStrategyActiveDo) Clauses(conds ...clause.Expression) IStockStrategyActiveDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockStrategyActiveDo) Returning(value interface{}, columns ...string) IStockStrategyActiveDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockStrategyActiveDo) Not(conds ...gen.Condition) IStockStrategyActiveDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockStrategyActiveDo) Or(conds ...gen.Condition) IStockStrategyActiveDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockStrategyActiveDo) Select(conds ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockStrategyActiveDo) Where(conds ...gen.Condition) IStockStrategyActiveDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockStrategyActiveDo) Order(conds ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockStrategyActiveDo) Distinct(cols ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockStrategyActiveDo) Omit(cols ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockStrategyActiveDo) Join(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockStrategyActiveDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockStrategyActiveDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockStrategyActiveDo) Group(cols ...field.Expr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockStrategyActiveDo) Having(conds ...gen.Condition) IStockStrategyActiveDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockStrategyActiveDo) Limit(limit int) IStockStrategyActiveDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockStrategyActiveDo) Offset(offset int) IStockStrategyActiveDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockStrategyActiveDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyActiveDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockStrategyActiveDo) Unscoped() IStockStrategyActiveDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockStrategyActiveDo) Create(values ...*model.StockStrategyActive) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockStrategyActiveDo) CreateInBatches(values model.StockStrategyActiveSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockStrategyActiveDo) Save(values ...*model.StockStrategyActive) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockStrategyActiveDo) First() (*model.StockStrategyActive, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyActive), nil
	}
}

func (s stockStrategyActiveDo) Take() (*model.StockStrategyActive, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyActive), nil
	}
}

func (s stockStrategyActiveDo) Last() (*model.StockStrategyActive, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyActive), nil
	}
}

func (s stockStrategyActiveDo) Find() (model.StockStrategyActiveSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockStrategyActiveSlice{}, err
	}
	if slice, ok := result.([]*model.StockStrategyActive); ok {
		return model.StockStrategyActiveSlice(slice), err
	}
	return model.StockStrategyActiveSlice{}, err

}

func (s stockStrategyActiveDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategyActiveSlice, err error) {
	buf := make([]*model.StockStrategyActive, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockStrategyActiveDo) FindInBatches(result *model.StockStrategyActiveSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockStrategyActiveDo) Attrs(attrs ...field.AssignExpr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockStrategyActiveDo) Assign(attrs ...field.AssignExpr) IStockStrategyActiveDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockStrategyActiveDo) Joins(fields ...field.RelationField) IStockStrategyActiveDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockStrategyActiveDo) Preload(fields ...field.RelationField) IStockStrategyActiveDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockStrategyActiveDo) FirstOrInit() (*model.StockStrategyActive, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyActive), nil
	}
}

func (s stockStrategyActiveDo) FirstOrCreate() (*model.StockStrategyActive, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyActive), nil
	}
}

func (s stockStrategyActiveDo) FindByPage(offset int, limit int) (result model.StockStrategyActiveSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockStrategyActiveDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockStrategyActiveDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockStrategyActiveDo) Delete(models ...*model.StockStrategyActive) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockStrategyActiveDo) withDO(do gen.Dao) *stockStrategyActiveDo {
	s.DO = *do.(*gen.DO)
	return s
}
