// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockStrategySku(db *gorm.DB, opts ...gen.DOOption) stockStrategySku {
	_stockStrategySku := stockStrategySku{}

	_stockStrategySku.stockStrategySkuDo.UseDB(db, opts...)
	_stockStrategySku.stockStrategySkuDo.UseModel(&model.StockStrategySku{})

	tableName := _stockStrategySku.stockStrategySkuDo.TableName()
	_stockStrategySku.ALL = field.NewAsterisk(tableName)
	_stockStrategySku.ID = field.NewInt32(tableName, "id")
	_stockStrategySku.StrategyID = field.NewInt32(tableName, "strategy_id")
	_stockStrategySku.Sku = field.NewString(tableName, "sku")

	_stockStrategySku.fillFieldMap()

	return _stockStrategySku
}

type stockStrategySku struct {
	stockStrategySkuDo stockStrategySkuDo

	ALL        field.Asterisk
	ID         field.Int32
	StrategyID field.Int32
	Sku        field.String

	fieldMap map[string]field.Expr
}

func (s stockStrategySku) Table(newTableName string) *stockStrategySku {
	s.stockStrategySkuDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockStrategySku) As(alias string) *stockStrategySku {
	s.stockStrategySkuDo.DO = *(s.stockStrategySkuDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockStrategySku) updateTableName(table string) *stockStrategySku {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.StrategyID = field.NewInt32(table, "strategy_id")
	s.Sku = field.NewString(table, "sku")

	s.fillFieldMap()

	return s
}

func (s *stockStrategySku) WithContext(ctx context.Context) IStockStrategySkuDo {
	return s.stockStrategySkuDo.WithContext(ctx)
}

func (s stockStrategySku) TableName() string { return s.stockStrategySkuDo.TableName() }

func (s stockStrategySku) Alias() string { return s.stockStrategySkuDo.Alias() }

func (s stockStrategySku) Columns(cols ...field.Expr) gen.Columns {
	return s.stockStrategySkuDo.Columns(cols...)
}

func (s *stockStrategySku) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockStrategySku) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 3)
	s.fieldMap["id"] = s.ID
	s.fieldMap["strategy_id"] = s.StrategyID
	s.fieldMap["sku"] = s.Sku
}

func (s stockStrategySku) clone(db *gorm.DB) stockStrategySku {
	s.stockStrategySkuDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockStrategySku) replaceDB(db *gorm.DB) stockStrategySku {
	s.stockStrategySkuDo.ReplaceDB(db)
	return s
}

type stockStrategySkuDo struct{ gen.DO }

type IStockStrategySkuDo interface {
	gen.SubQuery
	Debug() IStockStrategySkuDo
	WithContext(ctx context.Context) IStockStrategySkuDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockStrategySkuDo
	WriteDB() IStockStrategySkuDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockStrategySkuDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockStrategySkuDo
	Not(conds ...gen.Condition) IStockStrategySkuDo
	Or(conds ...gen.Condition) IStockStrategySkuDo
	Select(conds ...field.Expr) IStockStrategySkuDo
	Where(conds ...gen.Condition) IStockStrategySkuDo
	Order(conds ...field.Expr) IStockStrategySkuDo
	Distinct(cols ...field.Expr) IStockStrategySkuDo
	Omit(cols ...field.Expr) IStockStrategySkuDo
	Join(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo
	Group(cols ...field.Expr) IStockStrategySkuDo
	Having(conds ...gen.Condition) IStockStrategySkuDo
	Limit(limit int) IStockStrategySkuDo
	Offset(offset int) IStockStrategySkuDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategySkuDo
	Unscoped() IStockStrategySkuDo
	Create(values ...*model.StockStrategySku) error
	CreateInBatches(values model.StockStrategySkuSlice, batchSize int) error
	Save(values ...*model.StockStrategySku) error
	First() (*model.StockStrategySku, error)
	Take() (*model.StockStrategySku, error)
	Last() (*model.StockStrategySku, error)
	Find() (model.StockStrategySkuSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategySkuSlice, err error)
	FindInBatches(result *model.StockStrategySkuSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockStrategySku) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockStrategySkuDo
	Assign(attrs ...field.AssignExpr) IStockStrategySkuDo
	Joins(fields ...field.RelationField) IStockStrategySkuDo
	Preload(fields ...field.RelationField) IStockStrategySkuDo
	FirstOrInit() (*model.StockStrategySku, error)
	FirstOrCreate() (*model.StockStrategySku, error)
	FindByPage(offset int, limit int) (result model.StockStrategySkuSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockStrategySkuDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockStrategySkuDo) Debug() IStockStrategySkuDo {
	return s.withDO(s.DO.Debug())
}

func (s stockStrategySkuDo) WithContext(ctx context.Context) IStockStrategySkuDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockStrategySkuDo) ReadDB() IStockStrategySkuDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockStrategySkuDo) WriteDB() IStockStrategySkuDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockStrategySkuDo) Session(config *gorm.Session) IStockStrategySkuDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockStrategySkuDo) Clauses(conds ...clause.Expression) IStockStrategySkuDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockStrategySkuDo) Returning(value interface{}, columns ...string) IStockStrategySkuDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockStrategySkuDo) Not(conds ...gen.Condition) IStockStrategySkuDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockStrategySkuDo) Or(conds ...gen.Condition) IStockStrategySkuDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockStrategySkuDo) Select(conds ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockStrategySkuDo) Where(conds ...gen.Condition) IStockStrategySkuDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockStrategySkuDo) Order(conds ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockStrategySkuDo) Distinct(cols ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockStrategySkuDo) Omit(cols ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockStrategySkuDo) Join(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockStrategySkuDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockStrategySkuDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockStrategySkuDo) Group(cols ...field.Expr) IStockStrategySkuDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockStrategySkuDo) Having(conds ...gen.Condition) IStockStrategySkuDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockStrategySkuDo) Limit(limit int) IStockStrategySkuDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockStrategySkuDo) Offset(offset int) IStockStrategySkuDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockStrategySkuDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategySkuDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockStrategySkuDo) Unscoped() IStockStrategySkuDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockStrategySkuDo) Create(values ...*model.StockStrategySku) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockStrategySkuDo) CreateInBatches(values model.StockStrategySkuSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockStrategySkuDo) Save(values ...*model.StockStrategySku) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockStrategySkuDo) First() (*model.StockStrategySku, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategySku), nil
	}
}

func (s stockStrategySkuDo) Take() (*model.StockStrategySku, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategySku), nil
	}
}

func (s stockStrategySkuDo) Last() (*model.StockStrategySku, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategySku), nil
	}
}

func (s stockStrategySkuDo) Find() (model.StockStrategySkuSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockStrategySkuSlice{}, err
	}
	if slice, ok := result.([]*model.StockStrategySku); ok {
		return model.StockStrategySkuSlice(slice), err
	}
	return model.StockStrategySkuSlice{}, err

}

func (s stockStrategySkuDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategySkuSlice, err error) {
	buf := make([]*model.StockStrategySku, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockStrategySkuDo) FindInBatches(result *model.StockStrategySkuSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockStrategySkuDo) Attrs(attrs ...field.AssignExpr) IStockStrategySkuDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockStrategySkuDo) Assign(attrs ...field.AssignExpr) IStockStrategySkuDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockStrategySkuDo) Joins(fields ...field.RelationField) IStockStrategySkuDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockStrategySkuDo) Preload(fields ...field.RelationField) IStockStrategySkuDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockStrategySkuDo) FirstOrInit() (*model.StockStrategySku, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategySku), nil
	}
}

func (s stockStrategySkuDo) FirstOrCreate() (*model.StockStrategySku, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategySku), nil
	}
}

func (s stockStrategySkuDo) FindByPage(offset int, limit int) (result model.StockStrategySkuSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockStrategySkuDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockStrategySkuDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockStrategySkuDo) Delete(models ...*model.StockStrategySku) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockStrategySkuDo) withDO(do gen.Dao) *stockStrategySkuDo {
	s.DO = *do.(*gen.DO)
	return s
}
