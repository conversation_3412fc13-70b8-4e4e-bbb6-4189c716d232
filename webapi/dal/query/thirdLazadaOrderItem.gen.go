// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdLazadaOrderItem(db *gorm.DB, opts ...gen.DOOption) thirdLazadaOrderItem {
	_thirdLazadaOrderItem := thirdLazadaOrderItem{}

	_thirdLazadaOrderItem.thirdLazadaOrderItemDo.UseDB(db, opts...)
	_thirdLazadaOrderItem.thirdLazadaOrderItemDo.UseModel(&model.ThirdLazadaOrderItem{})

	tableName := _thirdLazadaOrderItem.thirdLazadaOrderItemDo.TableName()
	_thirdLazadaOrderItem.ALL = field.NewAsterisk(tableName)
	_thirdLazadaOrderItem.OrderItemID = field.NewInt64(tableName, "order_item_id")
	_thirdLazadaOrderItem.VoucherSeller = field.NewString(tableName, "voucher_seller")
	_thirdLazadaOrderItem.OrderType = field.NewString(tableName, "order_type")
	_thirdLazadaOrderItem.StagePayStatus = field.NewString(tableName, "stage_pay_status")
	_thirdLazadaOrderItem.WarehouseCode = field.NewString(tableName, "warehouse_code")
	_thirdLazadaOrderItem.VoucherSellerLpi = field.NewString(tableName, "voucher_seller_lpi")
	_thirdLazadaOrderItem.VoucherPlatformLpi = field.NewString(tableName, "voucher_platform_lpi")
	_thirdLazadaOrderItem.BuyerID = field.NewString(tableName, "buyer_id")
	_thirdLazadaOrderItem.ShippingFeeOriginal = field.NewString(tableName, "shipping_fee_original")
	_thirdLazadaOrderItem.ShippingFeeDiscountSeller = field.NewString(tableName, "shipping_fee_discount_seller")
	_thirdLazadaOrderItem.ShippingFeeDiscountPlatform = field.NewString(tableName, "shipping_fee_discount_platform")
	_thirdLazadaOrderItem.VoucherCodeSeller = field.NewString(tableName, "voucher_code_seller")
	_thirdLazadaOrderItem.VoucherCodePlatform = field.NewString(tableName, "voucher_code_platform")
	_thirdLazadaOrderItem.DeliveryOptionSof = field.NewString(tableName, "delivery_option_sof")
	_thirdLazadaOrderItem.IsFbl = field.NewString(tableName, "is_fbl")
	_thirdLazadaOrderItem.IsReroute = field.NewString(tableName, "is_reroute")
	_thirdLazadaOrderItem.Reason = field.NewString(tableName, "reason")
	_thirdLazadaOrderItem.DigitalDeliveryInfo = field.NewString(tableName, "digital_delivery_info")
	_thirdLazadaOrderItem.PromisedShippingTime = field.NewString(tableName, "promised_shipping_time")
	_thirdLazadaOrderItem.OrderID = field.NewString(tableName, "order_id")
	_thirdLazadaOrderItem.VoucherAmount = field.NewString(tableName, "voucher_amount")
	_thirdLazadaOrderItem.ReturnStatus = field.NewString(tableName, "return_status")
	_thirdLazadaOrderItem.ShippingType = field.NewString(tableName, "shipping_type")
	_thirdLazadaOrderItem.ShipmentProvider = field.NewString(tableName, "shipment_provider")
	_thirdLazadaOrderItem.Variation = field.NewString(tableName, "variation")
	_thirdLazadaOrderItem.CreatedAt = field.NewTime(tableName, "created_at")
	_thirdLazadaOrderItem.InvoiceNumber = field.NewString(tableName, "invoice_number")
	_thirdLazadaOrderItem.ShippingAmount = field.NewString(tableName, "shipping_amount")
	_thirdLazadaOrderItem.Currency = field.NewString(tableName, "currency")
	_thirdLazadaOrderItem.OrderFlag = field.NewString(tableName, "order_flag")
	_thirdLazadaOrderItem.ShopID = field.NewString(tableName, "shop_id")
	_thirdLazadaOrderItem.SLATimeStamp = field.NewString(tableName, "sla_time_stamp")
	_thirdLazadaOrderItem.Sku = field.NewString(tableName, "sku")
	_thirdLazadaOrderItem.VoucherCode = field.NewString(tableName, "voucher_code")
	_thirdLazadaOrderItem.WalletCredits = field.NewString(tableName, "wallet_credits")
	_thirdLazadaOrderItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_thirdLazadaOrderItem.IsDigital = field.NewInt32(tableName, "is_digital")
	_thirdLazadaOrderItem.TrackingCodePre = field.NewString(tableName, "tracking_code_pre")
	_thirdLazadaOrderItem.PackageID = field.NewString(tableName, "package_id")
	_thirdLazadaOrderItem.TrackingCode = field.NewString(tableName, "tracking_code")
	_thirdLazadaOrderItem.ShippingServiceCost = field.NewFloat64(tableName, "shipping_service_cost")
	_thirdLazadaOrderItem.ExtraAttributes = field.NewString(tableName, "extra_attributes")
	_thirdLazadaOrderItem.PaidPrice = field.NewString(tableName, "paid_price")
	_thirdLazadaOrderItem.ShippingProviderType = field.NewString(tableName, "shipping_provider_type")
	_thirdLazadaOrderItem.ProductDetailURL = field.NewString(tableName, "product_detail_url")
	_thirdLazadaOrderItem.ShopSku = field.NewString(tableName, "shop_sku")
	_thirdLazadaOrderItem.ReasonDetail = field.NewString(tableName, "reason_detail")
	_thirdLazadaOrderItem.PurchaseOrderID = field.NewString(tableName, "purchase_order_id")
	_thirdLazadaOrderItem.SkuID = field.NewString(tableName, "sku_id")
	_thirdLazadaOrderItem.ProductID = field.NewString(tableName, "product_id")
	_thirdLazadaOrderItem.FulfillmentSLA = field.NewString(tableName, "fulfillment_sla")
	_thirdLazadaOrderItem.PriorityFulfillmentTag = field.NewString(tableName, "priority_fulfillment_tag")
	_thirdLazadaOrderItem.GiftWrapping = field.NewString(tableName, "gift_wrapping")
	_thirdLazadaOrderItem.ShowGiftwrappingTag = field.NewBool(tableName, "show_giftwrapping_tag")
	_thirdLazadaOrderItem.Personalization = field.NewString(tableName, "personalization")
	_thirdLazadaOrderItem.ShowPersonalizationTag = field.NewBool(tableName, "show_personalization_tag")
	_thirdLazadaOrderItem.PaymentTime = field.NewInt64(tableName, "payment_time")
	_thirdLazadaOrderItem.PickUpStoreInfo = field.NewString(tableName, "pick_up_store_info")
	_thirdLazadaOrderItem.PurchaseOrderNumber = field.NewString(tableName, "purchase_order_number")
	_thirdLazadaOrderItem.Name = field.NewString(tableName, "name")
	_thirdLazadaOrderItem.ProductMainImage = field.NewString(tableName, "product_main_image")
	_thirdLazadaOrderItem.ItemPrice = field.NewString(tableName, "item_price")
	_thirdLazadaOrderItem.TaxAmount = field.NewString(tableName, "tax_amount")
	_thirdLazadaOrderItem.Status = field.NewString(tableName, "status")
	_thirdLazadaOrderItem.CancelReturnInitiator = field.NewString(tableName, "cancel_return_initiator")
	_thirdLazadaOrderItem.VoucherPlatform = field.NewString(tableName, "voucher_platform")

	_thirdLazadaOrderItem.fillFieldMap()

	return _thirdLazadaOrderItem
}

type thirdLazadaOrderItem struct {
	thirdLazadaOrderItemDo thirdLazadaOrderItemDo

	ALL                         field.Asterisk
	OrderItemID                 field.Int64
	VoucherSeller               field.String
	OrderType                   field.String
	StagePayStatus              field.String
	WarehouseCode               field.String
	VoucherSellerLpi            field.String
	VoucherPlatformLpi          field.String
	BuyerID                     field.String
	ShippingFeeOriginal         field.String
	ShippingFeeDiscountSeller   field.String
	ShippingFeeDiscountPlatform field.String
	VoucherCodeSeller           field.String
	VoucherCodePlatform         field.String
	DeliveryOptionSof           field.String
	IsFbl                       field.String
	IsReroute                   field.String
	Reason                      field.String
	DigitalDeliveryInfo         field.String
	PromisedShippingTime        field.String
	OrderID                     field.String
	VoucherAmount               field.String
	ReturnStatus                field.String
	ShippingType                field.String
	ShipmentProvider            field.String
	Variation                   field.String
	CreatedAt                   field.Time
	InvoiceNumber               field.String
	ShippingAmount              field.String
	Currency                    field.String
	OrderFlag                   field.String
	ShopID                      field.String
	SLATimeStamp                field.String
	Sku                         field.String
	VoucherCode                 field.String
	WalletCredits               field.String
	UpdatedAt                   field.Time
	IsDigital                   field.Int32
	TrackingCodePre             field.String
	PackageID                   field.String
	TrackingCode                field.String
	ShippingServiceCost         field.Float64
	ExtraAttributes             field.String
	PaidPrice                   field.String
	ShippingProviderType        field.String
	ProductDetailURL            field.String
	ShopSku                     field.String
	ReasonDetail                field.String
	PurchaseOrderID             field.String
	SkuID                       field.String
	ProductID                   field.String
	FulfillmentSLA              field.String
	PriorityFulfillmentTag      field.String
	GiftWrapping                field.String
	ShowGiftwrappingTag         field.Bool
	Personalization             field.String
	ShowPersonalizationTag      field.Bool
	PaymentTime                 field.Int64
	PickUpStoreInfo             field.String
	PurchaseOrderNumber         field.String
	Name                        field.String
	ProductMainImage            field.String
	ItemPrice                   field.String
	TaxAmount                   field.String
	Status                      field.String
	CancelReturnInitiator       field.String
	VoucherPlatform             field.String

	fieldMap map[string]field.Expr
}

func (t thirdLazadaOrderItem) Table(newTableName string) *thirdLazadaOrderItem {
	t.thirdLazadaOrderItemDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdLazadaOrderItem) As(alias string) *thirdLazadaOrderItem {
	t.thirdLazadaOrderItemDo.DO = *(t.thirdLazadaOrderItemDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdLazadaOrderItem) updateTableName(table string) *thirdLazadaOrderItem {
	t.ALL = field.NewAsterisk(table)
	t.OrderItemID = field.NewInt64(table, "order_item_id")
	t.VoucherSeller = field.NewString(table, "voucher_seller")
	t.OrderType = field.NewString(table, "order_type")
	t.StagePayStatus = field.NewString(table, "stage_pay_status")
	t.WarehouseCode = field.NewString(table, "warehouse_code")
	t.VoucherSellerLpi = field.NewString(table, "voucher_seller_lpi")
	t.VoucherPlatformLpi = field.NewString(table, "voucher_platform_lpi")
	t.BuyerID = field.NewString(table, "buyer_id")
	t.ShippingFeeOriginal = field.NewString(table, "shipping_fee_original")
	t.ShippingFeeDiscountSeller = field.NewString(table, "shipping_fee_discount_seller")
	t.ShippingFeeDiscountPlatform = field.NewString(table, "shipping_fee_discount_platform")
	t.VoucherCodeSeller = field.NewString(table, "voucher_code_seller")
	t.VoucherCodePlatform = field.NewString(table, "voucher_code_platform")
	t.DeliveryOptionSof = field.NewString(table, "delivery_option_sof")
	t.IsFbl = field.NewString(table, "is_fbl")
	t.IsReroute = field.NewString(table, "is_reroute")
	t.Reason = field.NewString(table, "reason")
	t.DigitalDeliveryInfo = field.NewString(table, "digital_delivery_info")
	t.PromisedShippingTime = field.NewString(table, "promised_shipping_time")
	t.OrderID = field.NewString(table, "order_id")
	t.VoucherAmount = field.NewString(table, "voucher_amount")
	t.ReturnStatus = field.NewString(table, "return_status")
	t.ShippingType = field.NewString(table, "shipping_type")
	t.ShipmentProvider = field.NewString(table, "shipment_provider")
	t.Variation = field.NewString(table, "variation")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.InvoiceNumber = field.NewString(table, "invoice_number")
	t.ShippingAmount = field.NewString(table, "shipping_amount")
	t.Currency = field.NewString(table, "currency")
	t.OrderFlag = field.NewString(table, "order_flag")
	t.ShopID = field.NewString(table, "shop_id")
	t.SLATimeStamp = field.NewString(table, "sla_time_stamp")
	t.Sku = field.NewString(table, "sku")
	t.VoucherCode = field.NewString(table, "voucher_code")
	t.WalletCredits = field.NewString(table, "wallet_credits")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsDigital = field.NewInt32(table, "is_digital")
	t.TrackingCodePre = field.NewString(table, "tracking_code_pre")
	t.PackageID = field.NewString(table, "package_id")
	t.TrackingCode = field.NewString(table, "tracking_code")
	t.ShippingServiceCost = field.NewFloat64(table, "shipping_service_cost")
	t.ExtraAttributes = field.NewString(table, "extra_attributes")
	t.PaidPrice = field.NewString(table, "paid_price")
	t.ShippingProviderType = field.NewString(table, "shipping_provider_type")
	t.ProductDetailURL = field.NewString(table, "product_detail_url")
	t.ShopSku = field.NewString(table, "shop_sku")
	t.ReasonDetail = field.NewString(table, "reason_detail")
	t.PurchaseOrderID = field.NewString(table, "purchase_order_id")
	t.SkuID = field.NewString(table, "sku_id")
	t.ProductID = field.NewString(table, "product_id")
	t.FulfillmentSLA = field.NewString(table, "fulfillment_sla")
	t.PriorityFulfillmentTag = field.NewString(table, "priority_fulfillment_tag")
	t.GiftWrapping = field.NewString(table, "gift_wrapping")
	t.ShowGiftwrappingTag = field.NewBool(table, "show_giftwrapping_tag")
	t.Personalization = field.NewString(table, "personalization")
	t.ShowPersonalizationTag = field.NewBool(table, "show_personalization_tag")
	t.PaymentTime = field.NewInt64(table, "payment_time")
	t.PickUpStoreInfo = field.NewString(table, "pick_up_store_info")
	t.PurchaseOrderNumber = field.NewString(table, "purchase_order_number")
	t.Name = field.NewString(table, "name")
	t.ProductMainImage = field.NewString(table, "product_main_image")
	t.ItemPrice = field.NewString(table, "item_price")
	t.TaxAmount = field.NewString(table, "tax_amount")
	t.Status = field.NewString(table, "status")
	t.CancelReturnInitiator = field.NewString(table, "cancel_return_initiator")
	t.VoucherPlatform = field.NewString(table, "voucher_platform")

	t.fillFieldMap()

	return t
}

func (t *thirdLazadaOrderItem) WithContext(ctx context.Context) IThirdLazadaOrderItemDo {
	return t.thirdLazadaOrderItemDo.WithContext(ctx)
}

func (t thirdLazadaOrderItem) TableName() string { return t.thirdLazadaOrderItemDo.TableName() }

func (t thirdLazadaOrderItem) Alias() string { return t.thirdLazadaOrderItemDo.Alias() }

func (t thirdLazadaOrderItem) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdLazadaOrderItemDo.Columns(cols...)
}

func (t *thirdLazadaOrderItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdLazadaOrderItem) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 66)
	t.fieldMap["order_item_id"] = t.OrderItemID
	t.fieldMap["voucher_seller"] = t.VoucherSeller
	t.fieldMap["order_type"] = t.OrderType
	t.fieldMap["stage_pay_status"] = t.StagePayStatus
	t.fieldMap["warehouse_code"] = t.WarehouseCode
	t.fieldMap["voucher_seller_lpi"] = t.VoucherSellerLpi
	t.fieldMap["voucher_platform_lpi"] = t.VoucherPlatformLpi
	t.fieldMap["buyer_id"] = t.BuyerID
	t.fieldMap["shipping_fee_original"] = t.ShippingFeeOriginal
	t.fieldMap["shipping_fee_discount_seller"] = t.ShippingFeeDiscountSeller
	t.fieldMap["shipping_fee_discount_platform"] = t.ShippingFeeDiscountPlatform
	t.fieldMap["voucher_code_seller"] = t.VoucherCodeSeller
	t.fieldMap["voucher_code_platform"] = t.VoucherCodePlatform
	t.fieldMap["delivery_option_sof"] = t.DeliveryOptionSof
	t.fieldMap["is_fbl"] = t.IsFbl
	t.fieldMap["is_reroute"] = t.IsReroute
	t.fieldMap["reason"] = t.Reason
	t.fieldMap["digital_delivery_info"] = t.DigitalDeliveryInfo
	t.fieldMap["promised_shipping_time"] = t.PromisedShippingTime
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["voucher_amount"] = t.VoucherAmount
	t.fieldMap["return_status"] = t.ReturnStatus
	t.fieldMap["shipping_type"] = t.ShippingType
	t.fieldMap["shipment_provider"] = t.ShipmentProvider
	t.fieldMap["variation"] = t.Variation
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["invoice_number"] = t.InvoiceNumber
	t.fieldMap["shipping_amount"] = t.ShippingAmount
	t.fieldMap["currency"] = t.Currency
	t.fieldMap["order_flag"] = t.OrderFlag
	t.fieldMap["shop_id"] = t.ShopID
	t.fieldMap["sla_time_stamp"] = t.SLATimeStamp
	t.fieldMap["sku"] = t.Sku
	t.fieldMap["voucher_code"] = t.VoucherCode
	t.fieldMap["wallet_credits"] = t.WalletCredits
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_digital"] = t.IsDigital
	t.fieldMap["tracking_code_pre"] = t.TrackingCodePre
	t.fieldMap["package_id"] = t.PackageID
	t.fieldMap["tracking_code"] = t.TrackingCode
	t.fieldMap["shipping_service_cost"] = t.ShippingServiceCost
	t.fieldMap["extra_attributes"] = t.ExtraAttributes
	t.fieldMap["paid_price"] = t.PaidPrice
	t.fieldMap["shipping_provider_type"] = t.ShippingProviderType
	t.fieldMap["product_detail_url"] = t.ProductDetailURL
	t.fieldMap["shop_sku"] = t.ShopSku
	t.fieldMap["reason_detail"] = t.ReasonDetail
	t.fieldMap["purchase_order_id"] = t.PurchaseOrderID
	t.fieldMap["sku_id"] = t.SkuID
	t.fieldMap["product_id"] = t.ProductID
	t.fieldMap["fulfillment_sla"] = t.FulfillmentSLA
	t.fieldMap["priority_fulfillment_tag"] = t.PriorityFulfillmentTag
	t.fieldMap["gift_wrapping"] = t.GiftWrapping
	t.fieldMap["show_giftwrapping_tag"] = t.ShowGiftwrappingTag
	t.fieldMap["personalization"] = t.Personalization
	t.fieldMap["show_personalization_tag"] = t.ShowPersonalizationTag
	t.fieldMap["payment_time"] = t.PaymentTime
	t.fieldMap["pick_up_store_info"] = t.PickUpStoreInfo
	t.fieldMap["purchase_order_number"] = t.PurchaseOrderNumber
	t.fieldMap["name"] = t.Name
	t.fieldMap["product_main_image"] = t.ProductMainImage
	t.fieldMap["item_price"] = t.ItemPrice
	t.fieldMap["tax_amount"] = t.TaxAmount
	t.fieldMap["status"] = t.Status
	t.fieldMap["cancel_return_initiator"] = t.CancelReturnInitiator
	t.fieldMap["voucher_platform"] = t.VoucherPlatform
}

func (t thirdLazadaOrderItem) clone(db *gorm.DB) thirdLazadaOrderItem {
	t.thirdLazadaOrderItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdLazadaOrderItem) replaceDB(db *gorm.DB) thirdLazadaOrderItem {
	t.thirdLazadaOrderItemDo.ReplaceDB(db)
	return t
}

type thirdLazadaOrderItemDo struct{ gen.DO }

type IThirdLazadaOrderItemDo interface {
	gen.SubQuery
	Debug() IThirdLazadaOrderItemDo
	WithContext(ctx context.Context) IThirdLazadaOrderItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdLazadaOrderItemDo
	WriteDB() IThirdLazadaOrderItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdLazadaOrderItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdLazadaOrderItemDo
	Not(conds ...gen.Condition) IThirdLazadaOrderItemDo
	Or(conds ...gen.Condition) IThirdLazadaOrderItemDo
	Select(conds ...field.Expr) IThirdLazadaOrderItemDo
	Where(conds ...gen.Condition) IThirdLazadaOrderItemDo
	Order(conds ...field.Expr) IThirdLazadaOrderItemDo
	Distinct(cols ...field.Expr) IThirdLazadaOrderItemDo
	Omit(cols ...field.Expr) IThirdLazadaOrderItemDo
	Join(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo
	Group(cols ...field.Expr) IThirdLazadaOrderItemDo
	Having(conds ...gen.Condition) IThirdLazadaOrderItemDo
	Limit(limit int) IThirdLazadaOrderItemDo
	Offset(offset int) IThirdLazadaOrderItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdLazadaOrderItemDo
	Unscoped() IThirdLazadaOrderItemDo
	Create(values ...*model.ThirdLazadaOrderItem) error
	CreateInBatches(values model.ThirdLazadaOrderItemSlice, batchSize int) error
	Save(values ...*model.ThirdLazadaOrderItem) error
	First() (*model.ThirdLazadaOrderItem, error)
	Take() (*model.ThirdLazadaOrderItem, error)
	Last() (*model.ThirdLazadaOrderItem, error)
	Find() (model.ThirdLazadaOrderItemSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdLazadaOrderItemSlice, err error)
	FindInBatches(result *model.ThirdLazadaOrderItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdLazadaOrderItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdLazadaOrderItemDo
	Assign(attrs ...field.AssignExpr) IThirdLazadaOrderItemDo
	Joins(fields ...field.RelationField) IThirdLazadaOrderItemDo
	Preload(fields ...field.RelationField) IThirdLazadaOrderItemDo
	FirstOrInit() (*model.ThirdLazadaOrderItem, error)
	FirstOrCreate() (*model.ThirdLazadaOrderItem, error)
	FindByPage(offset int, limit int) (result model.ThirdLazadaOrderItemSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdLazadaOrderItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdLazadaOrderItemDo) Debug() IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdLazadaOrderItemDo) WithContext(ctx context.Context) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdLazadaOrderItemDo) ReadDB() IThirdLazadaOrderItemDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdLazadaOrderItemDo) WriteDB() IThirdLazadaOrderItemDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdLazadaOrderItemDo) Session(config *gorm.Session) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdLazadaOrderItemDo) Clauses(conds ...clause.Expression) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdLazadaOrderItemDo) Returning(value interface{}, columns ...string) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdLazadaOrderItemDo) Not(conds ...gen.Condition) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdLazadaOrderItemDo) Or(conds ...gen.Condition) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdLazadaOrderItemDo) Select(conds ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdLazadaOrderItemDo) Where(conds ...gen.Condition) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdLazadaOrderItemDo) Order(conds ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdLazadaOrderItemDo) Distinct(cols ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdLazadaOrderItemDo) Omit(cols ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdLazadaOrderItemDo) Join(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdLazadaOrderItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdLazadaOrderItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdLazadaOrderItemDo) Group(cols ...field.Expr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdLazadaOrderItemDo) Having(conds ...gen.Condition) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdLazadaOrderItemDo) Limit(limit int) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdLazadaOrderItemDo) Offset(offset int) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdLazadaOrderItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdLazadaOrderItemDo) Unscoped() IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdLazadaOrderItemDo) Create(values ...*model.ThirdLazadaOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdLazadaOrderItemDo) CreateInBatches(values model.ThirdLazadaOrderItemSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdLazadaOrderItemDo) Save(values ...*model.ThirdLazadaOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdLazadaOrderItemDo) First() (*model.ThirdLazadaOrderItem, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrderItem), nil
	}
}

func (t thirdLazadaOrderItemDo) Take() (*model.ThirdLazadaOrderItem, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrderItem), nil
	}
}

func (t thirdLazadaOrderItemDo) Last() (*model.ThirdLazadaOrderItem, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrderItem), nil
	}
}

func (t thirdLazadaOrderItemDo) Find() (model.ThirdLazadaOrderItemSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdLazadaOrderItemSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdLazadaOrderItem); ok {
		return model.ThirdLazadaOrderItemSlice(slice), err
	}
	return model.ThirdLazadaOrderItemSlice{}, err

}

func (t thirdLazadaOrderItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdLazadaOrderItemSlice, err error) {
	buf := make([]*model.ThirdLazadaOrderItem, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdLazadaOrderItemDo) FindInBatches(result *model.ThirdLazadaOrderItemSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdLazadaOrderItemDo) Attrs(attrs ...field.AssignExpr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdLazadaOrderItemDo) Assign(attrs ...field.AssignExpr) IThirdLazadaOrderItemDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdLazadaOrderItemDo) Joins(fields ...field.RelationField) IThirdLazadaOrderItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdLazadaOrderItemDo) Preload(fields ...field.RelationField) IThirdLazadaOrderItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdLazadaOrderItemDo) FirstOrInit() (*model.ThirdLazadaOrderItem, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrderItem), nil
	}
}

func (t thirdLazadaOrderItemDo) FirstOrCreate() (*model.ThirdLazadaOrderItem, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrderItem), nil
	}
}

func (t thirdLazadaOrderItemDo) FindByPage(offset int, limit int) (result model.ThirdLazadaOrderItemSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdLazadaOrderItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdLazadaOrderItemDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdLazadaOrderItemDo) Delete(models ...*model.ThirdLazadaOrderItem) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdLazadaOrderItemDo) withDO(do gen.Dao) *thirdLazadaOrderItemDo {
	t.DO = *do.(*gen.DO)
	return t
}
