// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseOut(db *gorm.DB, opts ...gen.DOOption) warehouseOut {
	_warehouseOut := warehouseOut{}

	_warehouseOut.warehouseOutDo.UseDB(db, opts...)
	_warehouseOut.warehouseOutDo.UseModel(&model.WarehouseOut{})

	tableName := _warehouseOut.warehouseOutDo.TableName()
	_warehouseOut.ALL = field.NewAsterisk(tableName)
	_warehouseOut.ID = field.NewInt32(tableName, "id")
	_warehouseOut.OrderNo = field.NewString(tableName, "order_no")
	_warehouseOut.SourceNo = field.NewString(tableName, "source_no")
	_warehouseOut.Type = field.NewString(tableName, "type")
	_warehouseOut.CreatedTime = field.NewTime(tableName, "created_time")
	_warehouseOut.SyncTime = field.NewTime(tableName, "sync_time")
	_warehouseOut.State = field.NewString(tableName, "state")
	_warehouseOut.CreatedUser = field.NewString(tableName, "created_user")
	_warehouseOut.Remark = field.NewString(tableName, "remark")
	_warehouseOut.PdfURL = field.NewString(tableName, "pdf_url")
	_warehouseOut.ExpressNo = field.NewString(tableName, "express_no")
	_warehouseOut.LogisticsCode = field.NewString(tableName, "logistics_code")
	_warehouseOut.Reason = field.NewString(tableName, "reason")
	_warehouseOut.WarehouseState = field.NewString(tableName, "warehouse_state")
	_warehouseOut.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_warehouseOut.fillFieldMap()

	return _warehouseOut
}

type warehouseOut struct {
	warehouseOutDo warehouseOutDo

	ALL            field.Asterisk
	ID             field.Int32
	OrderNo        field.String
	SourceNo       field.String
	Type           field.String
	CreatedTime    field.Time
	SyncTime       field.Time
	State          field.String
	CreatedUser    field.String
	Remark         field.String
	PdfURL         field.String
	ExpressNo      field.String
	LogisticsCode  field.String
	Reason         field.String
	WarehouseState field.String
	WarehouseCode  field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (w warehouseOut) Table(newTableName string) *warehouseOut {
	w.warehouseOutDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseOut) As(alias string) *warehouseOut {
	w.warehouseOutDo.DO = *(w.warehouseOutDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseOut) updateTableName(table string) *warehouseOut {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.OrderNo = field.NewString(table, "order_no")
	w.SourceNo = field.NewString(table, "source_no")
	w.Type = field.NewString(table, "type")
	w.CreatedTime = field.NewTime(table, "created_time")
	w.SyncTime = field.NewTime(table, "sync_time")
	w.State = field.NewString(table, "state")
	w.CreatedUser = field.NewString(table, "created_user")
	w.Remark = field.NewString(table, "remark")
	w.PdfURL = field.NewString(table, "pdf_url")
	w.ExpressNo = field.NewString(table, "express_no")
	w.LogisticsCode = field.NewString(table, "logistics_code")
	w.Reason = field.NewString(table, "reason")
	w.WarehouseState = field.NewString(table, "warehouse_state")
	w.WarehouseCode = field.NewString(table, "warehouse_code")

	w.fillFieldMap()

	return w
}

func (w *warehouseOut) WithContext(ctx context.Context) IWarehouseOutDo {
	return w.warehouseOutDo.WithContext(ctx)
}

func (w warehouseOut) TableName() string { return w.warehouseOutDo.TableName() }

func (w warehouseOut) Alias() string { return w.warehouseOutDo.Alias() }

func (w warehouseOut) Columns(cols ...field.Expr) gen.Columns {
	return w.warehouseOutDo.Columns(cols...)
}

func (w *warehouseOut) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseOut) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 15)
	w.fieldMap["id"] = w.ID
	w.fieldMap["order_no"] = w.OrderNo
	w.fieldMap["source_no"] = w.SourceNo
	w.fieldMap["type"] = w.Type
	w.fieldMap["created_time"] = w.CreatedTime
	w.fieldMap["sync_time"] = w.SyncTime
	w.fieldMap["state"] = w.State
	w.fieldMap["created_user"] = w.CreatedUser
	w.fieldMap["remark"] = w.Remark
	w.fieldMap["pdf_url"] = w.PdfURL
	w.fieldMap["express_no"] = w.ExpressNo
	w.fieldMap["logistics_code"] = w.LogisticsCode
	w.fieldMap["reason"] = w.Reason
	w.fieldMap["warehouse_state"] = w.WarehouseState
	w.fieldMap["warehouse_code"] = w.WarehouseCode
}

func (w warehouseOut) clone(db *gorm.DB) warehouseOut {
	w.warehouseOutDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseOut) replaceDB(db *gorm.DB) warehouseOut {
	w.warehouseOutDo.ReplaceDB(db)
	return w
}

type warehouseOutDo struct{ gen.DO }

type IWarehouseOutDo interface {
	gen.SubQuery
	Debug() IWarehouseOutDo
	WithContext(ctx context.Context) IWarehouseOutDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseOutDo
	WriteDB() IWarehouseOutDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseOutDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseOutDo
	Not(conds ...gen.Condition) IWarehouseOutDo
	Or(conds ...gen.Condition) IWarehouseOutDo
	Select(conds ...field.Expr) IWarehouseOutDo
	Where(conds ...gen.Condition) IWarehouseOutDo
	Order(conds ...field.Expr) IWarehouseOutDo
	Distinct(cols ...field.Expr) IWarehouseOutDo
	Omit(cols ...field.Expr) IWarehouseOutDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseOutDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutDo
	Group(cols ...field.Expr) IWarehouseOutDo
	Having(conds ...gen.Condition) IWarehouseOutDo
	Limit(limit int) IWarehouseOutDo
	Offset(offset int) IWarehouseOutDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutDo
	Unscoped() IWarehouseOutDo
	Create(values ...*model.WarehouseOut) error
	CreateInBatches(values model.WarehouseOutSlice, batchSize int) error
	Save(values ...*model.WarehouseOut) error
	First() (*model.WarehouseOut, error)
	Take() (*model.WarehouseOut, error)
	Last() (*model.WarehouseOut, error)
	Find() (model.WarehouseOutSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutSlice, err error)
	FindInBatches(result *model.WarehouseOutSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseOut) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseOutDo
	Assign(attrs ...field.AssignExpr) IWarehouseOutDo
	Joins(fields ...field.RelationField) IWarehouseOutDo
	Preload(fields ...field.RelationField) IWarehouseOutDo
	FirstOrInit() (*model.WarehouseOut, error)
	FirstOrCreate() (*model.WarehouseOut, error)
	FindByPage(offset int, limit int) (result model.WarehouseOutSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseOutDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseOutDo) Debug() IWarehouseOutDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseOutDo) WithContext(ctx context.Context) IWarehouseOutDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseOutDo) ReadDB() IWarehouseOutDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseOutDo) WriteDB() IWarehouseOutDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseOutDo) Session(config *gorm.Session) IWarehouseOutDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseOutDo) Clauses(conds ...clause.Expression) IWarehouseOutDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseOutDo) Returning(value interface{}, columns ...string) IWarehouseOutDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseOutDo) Not(conds ...gen.Condition) IWarehouseOutDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseOutDo) Or(conds ...gen.Condition) IWarehouseOutDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseOutDo) Select(conds ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseOutDo) Where(conds ...gen.Condition) IWarehouseOutDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseOutDo) Order(conds ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseOutDo) Distinct(cols ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseOutDo) Omit(cols ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseOutDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseOutDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseOutDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseOutDo) Group(cols ...field.Expr) IWarehouseOutDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseOutDo) Having(conds ...gen.Condition) IWarehouseOutDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseOutDo) Limit(limit int) IWarehouseOutDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseOutDo) Offset(offset int) IWarehouseOutDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseOutDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseOutDo) Unscoped() IWarehouseOutDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseOutDo) Create(values ...*model.WarehouseOut) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseOutDo) CreateInBatches(values model.WarehouseOutSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseOutDo) Save(values ...*model.WarehouseOut) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseOutDo) First() (*model.WarehouseOut, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOut), nil
	}
}

func (w warehouseOutDo) Take() (*model.WarehouseOut, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOut), nil
	}
}

func (w warehouseOutDo) Last() (*model.WarehouseOut, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOut), nil
	}
}

func (w warehouseOutDo) Find() (model.WarehouseOutSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseOutSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseOut); ok {
		return model.WarehouseOutSlice(slice), err
	}
	return model.WarehouseOutSlice{}, err

}

func (w warehouseOutDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutSlice, err error) {
	buf := make([]*model.WarehouseOut, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseOutDo) FindInBatches(result *model.WarehouseOutSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseOutDo) Attrs(attrs ...field.AssignExpr) IWarehouseOutDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseOutDo) Assign(attrs ...field.AssignExpr) IWarehouseOutDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseOutDo) Joins(fields ...field.RelationField) IWarehouseOutDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseOutDo) Preload(fields ...field.RelationField) IWarehouseOutDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseOutDo) FirstOrInit() (*model.WarehouseOut, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOut), nil
	}
}

func (w warehouseOutDo) FirstOrCreate() (*model.WarehouseOut, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOut), nil
	}
}

func (w warehouseOutDo) FindByPage(offset int, limit int) (result model.WarehouseOutSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseOutDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseOutDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseOutDo) Delete(models ...*model.WarehouseOut) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseOutDo) withDO(do gen.Dao) *warehouseOutDo {
	w.DO = *do.(*gen.DO)
	return w
}
