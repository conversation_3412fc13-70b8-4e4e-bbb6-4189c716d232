// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdShoplineOrder(db *gorm.DB, opts ...gen.DOOption) thirdShoplineOrder {
	_thirdShoplineOrder := thirdShoplineOrder{}

	_thirdShoplineOrder.thirdShoplineOrderDo.UseDB(db, opts...)
	_thirdShoplineOrder.thirdShoplineOrderDo.UseModel(&model.ThirdShoplineOrder{})

	tableName := _thirdShoplineOrder.thirdShoplineOrderDo.TableName()
	_thirdShoplineOrder.ALL = field.NewAsterisk(tableName)
	_thirdShoplineOrder.ID = field.NewString(tableName, "id")
	_thirdShoplineOrder.AdjustPrice = field.NewString(tableName, "adjust_price")
	_thirdShoplineOrder.AdjustPriceSet = field.NewString(tableName, "adjust_price_set")
	_thirdShoplineOrder.BillingAddress = field.NewString(tableName, "billing_address")
	_thirdShoplineOrder.BrowserIP = field.NewString(tableName, "browser_ip")
	_thirdShoplineOrder.BuyerNote = field.NewString(tableName, "buyer_note")
	_thirdShoplineOrder.CancelReason = field.NewString(tableName, "cancel_reason")
	_thirdShoplineOrder.CancelledAt = field.NewString(tableName, "cancelled_at")
	_thirdShoplineOrder.CartToken = field.NewString(tableName, "cart_token")
	_thirdShoplineOrder.CheckoutID = field.NewString(tableName, "checkout_id")
	_thirdShoplineOrder.CheckoutToken = field.NewString(tableName, "checkout_token")
	_thirdShoplineOrder.ClientDetails = field.NewString(tableName, "client_details")
	_thirdShoplineOrder.ContractSeq = field.NewString(tableName, "contract_seq")
	_thirdShoplineOrder.CreatedAt = field.NewString(tableName, "created_at")
	_thirdShoplineOrder.Currency = field.NewString(tableName, "currency")
	_thirdShoplineOrder.CurrentSubtotalPrice = field.NewString(tableName, "current_subtotal_price")
	_thirdShoplineOrder.CurrentSubtotalPriceSet = field.NewString(tableName, "current_subtotal_price_set")
	_thirdShoplineOrder.CurrentTotalDiscounts = field.NewString(tableName, "current_total_discounts")
	_thirdShoplineOrder.CurrentTotalDiscountsSet = field.NewString(tableName, "current_total_discounts_set")
	_thirdShoplineOrder.CurrentTotalDutiesSet = field.NewString(tableName, "current_total_duties_set")
	_thirdShoplineOrder.CurrentTotalPrice = field.NewString(tableName, "current_total_price")
	_thirdShoplineOrder.CurrentTotalPriceSet = field.NewString(tableName, "current_total_price_set")
	_thirdShoplineOrder.CurrentTotalTax = field.NewString(tableName, "current_total_tax")
	_thirdShoplineOrder.CurrentTotalTaxSet = field.NewString(tableName, "current_total_tax_set")
	_thirdShoplineOrder.Customer = field.NewString(tableName, "customer")
	_thirdShoplineOrder.CustomerLocale = field.NewString(tableName, "customer_locale")
	_thirdShoplineOrder.DeductMemberPointAmount = field.NewString(tableName, "deduct_member_point_amount")
	_thirdShoplineOrder.DeductMemberPointAmountSet = field.NewString(tableName, "deduct_member_point_amount_set")
	_thirdShoplineOrder.DiscountApplications = field.NewString(tableName, "discount_applications")
	_thirdShoplineOrder.DiscountCodes = field.NewString(tableName, "discount_codes")
	_thirdShoplineOrder.Email = field.NewString(tableName, "email")
	_thirdShoplineOrder.FinancialStatus = field.NewString(tableName, "financial_status")
	_thirdShoplineOrder.FulfillmentStatus = field.NewString(tableName, "fulfillment_status")
	_thirdShoplineOrder.Fulfillments = field.NewString(tableName, "fulfillments")
	_thirdShoplineOrder.HiddenOrder = field.NewBool(tableName, "hidden_order")
	_thirdShoplineOrder.LandingSite = field.NewString(tableName, "landing_site")
	_thirdShoplineOrder.LineItems = field.NewString(tableName, "line_items")
	_thirdShoplineOrder.Locations = field.NewString(tableName, "locations")
	_thirdShoplineOrder.Name = field.NewString(tableName, "name")
	_thirdShoplineOrder.Note = field.NewString(tableName, "note")
	_thirdShoplineOrder.NoteAttributes = field.NewString(tableName, "note_attributes")
	_thirdShoplineOrder.OrderAt = field.NewString(tableName, "order_at")
	_thirdShoplineOrder.OrderSource = field.NewString(tableName, "order_source")
	_thirdShoplineOrder.OrderStatusURL = field.NewString(tableName, "order_status_url")
	_thirdShoplineOrder.PaymentDetails = field.NewString(tableName, "payment_details")
	_thirdShoplineOrder.PaymentGatewayNames = field.NewString(tableName, "payment_gateway_names")
	_thirdShoplineOrder.Phone = field.NewString(tableName, "phone")
	_thirdShoplineOrder.PosLocationID = field.NewString(tableName, "pos_location_id")
	_thirdShoplineOrder.PresentmentCurrency = field.NewString(tableName, "presentment_currency")
	_thirdShoplineOrder.ProcessedAt = field.NewString(tableName, "processed_at")
	_thirdShoplineOrder.ProcessedUserID = field.NewString(tableName, "processed_user_id")
	_thirdShoplineOrder.ReferringSite = field.NewString(tableName, "referring_site")
	_thirdShoplineOrder.Refunds = field.NewString(tableName, "refunds")
	_thirdShoplineOrder.Service = field.NewString(tableName, "service")
	_thirdShoplineOrder.ShippingAddress = field.NewString(tableName, "shipping_address")
	_thirdShoplineOrder.ShippingLines = field.NewString(tableName, "shipping_lines")
	_thirdShoplineOrder.SourceIdentifier = field.NewString(tableName, "source_identifier")
	_thirdShoplineOrder.SourceName = field.NewString(tableName, "source_name")
	_thirdShoplineOrder.SourceURL = field.NewString(tableName, "source_url")
	_thirdShoplineOrder.Status = field.NewString(tableName, "status")
	_thirdShoplineOrder.StoreID = field.NewString(tableName, "store_id")
	_thirdShoplineOrder.SubtotalPrice = field.NewString(tableName, "subtotal_price")
	_thirdShoplineOrder.SubtotalPriceSet = field.NewString(tableName, "subtotal_price_set")
	_thirdShoplineOrder.Tags = field.NewString(tableName, "tags")
	_thirdShoplineOrder.TaxLines = field.NewString(tableName, "tax_lines")
	_thirdShoplineOrder.TaxNumber = field.NewString(tableName, "tax_number")
	_thirdShoplineOrder.TaxType = field.NewString(tableName, "tax_type")
	_thirdShoplineOrder.TaxesIncluded = field.NewBool(tableName, "taxes_included")
	_thirdShoplineOrder.TotalDiscounts = field.NewString(tableName, "total_discounts")
	_thirdShoplineOrder.TotalDiscountsSet = field.NewString(tableName, "total_discounts_set")
	_thirdShoplineOrder.TotalLineItemsPrice = field.NewString(tableName, "total_line_items_price")
	_thirdShoplineOrder.TotalLineItemsPriceSet = field.NewString(tableName, "total_line_items_price_set")
	_thirdShoplineOrder.TotalOutstanding = field.NewString(tableName, "total_outstanding")
	_thirdShoplineOrder.TotalShippingPriceSet = field.NewString(tableName, "total_shipping_price_set")
	_thirdShoplineOrder.TotalTax = field.NewString(tableName, "total_tax")
	_thirdShoplineOrder.TotalTaxSet = field.NewString(tableName, "total_tax_set")
	_thirdShoplineOrder.TotalTipReceived = field.NewString(tableName, "total_tip_received")
	_thirdShoplineOrder.TotalTipReceivedSet = field.NewString(tableName, "total_tip_received_set")
	_thirdShoplineOrder.TotalWeight = field.NewFloat64(tableName, "total_weight")
	_thirdShoplineOrder.UpdatedAt = field.NewString(tableName, "updated_at")
	_thirdShoplineOrder.UserID = field.NewString(tableName, "user_id")
	_thirdShoplineOrder.UtmParameters = field.NewString(tableName, "utm_parameters")

	_thirdShoplineOrder.fillFieldMap()

	return _thirdShoplineOrder
}

type thirdShoplineOrder struct {
	thirdShoplineOrderDo thirdShoplineOrderDo

	ALL                        field.Asterisk
	ID                         field.String
	AdjustPrice                field.String
	AdjustPriceSet             field.String
	BillingAddress             field.String
	BrowserIP                  field.String
	BuyerNote                  field.String
	CancelReason               field.String
	CancelledAt                field.String
	CartToken                  field.String
	CheckoutID                 field.String
	CheckoutToken              field.String
	ClientDetails              field.String
	ContractSeq                field.String
	CreatedAt                  field.String
	Currency                   field.String
	CurrentSubtotalPrice       field.String
	CurrentSubtotalPriceSet    field.String
	CurrentTotalDiscounts      field.String
	CurrentTotalDiscountsSet   field.String
	CurrentTotalDutiesSet      field.String
	CurrentTotalPrice          field.String
	CurrentTotalPriceSet       field.String
	CurrentTotalTax            field.String
	CurrentTotalTaxSet         field.String
	Customer                   field.String
	CustomerLocale             field.String
	DeductMemberPointAmount    field.String
	DeductMemberPointAmountSet field.String
	DiscountApplications       field.String
	DiscountCodes              field.String
	Email                      field.String
	FinancialStatus            field.String
	FulfillmentStatus          field.String
	Fulfillments               field.String
	HiddenOrder                field.Bool
	LandingSite                field.String
	LineItems                  field.String
	Locations                  field.String
	Name                       field.String
	Note                       field.String
	NoteAttributes             field.String
	OrderAt                    field.String
	OrderSource                field.String
	OrderStatusURL             field.String
	PaymentDetails             field.String
	PaymentGatewayNames        field.String
	Phone                      field.String
	PosLocationID              field.String
	PresentmentCurrency        field.String
	ProcessedAt                field.String
	ProcessedUserID            field.String
	ReferringSite              field.String
	Refunds                    field.String
	Service                    field.String
	ShippingAddress            field.String
	ShippingLines              field.String
	SourceIdentifier           field.String
	SourceName                 field.String
	SourceURL                  field.String
	Status                     field.String
	StoreID                    field.String
	SubtotalPrice              field.String
	SubtotalPriceSet           field.String
	Tags                       field.String
	TaxLines                   field.String
	TaxNumber                  field.String
	TaxType                    field.String
	TaxesIncluded              field.Bool
	TotalDiscounts             field.String
	TotalDiscountsSet          field.String
	TotalLineItemsPrice        field.String
	TotalLineItemsPriceSet     field.String
	TotalOutstanding           field.String
	TotalShippingPriceSet      field.String
	TotalTax                   field.String
	TotalTaxSet                field.String
	TotalTipReceived           field.String
	TotalTipReceivedSet        field.String
	TotalWeight                field.Float64
	UpdatedAt                  field.String
	UserID                     field.String
	UtmParameters              field.String

	fieldMap map[string]field.Expr
}

func (t thirdShoplineOrder) Table(newTableName string) *thirdShoplineOrder {
	t.thirdShoplineOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdShoplineOrder) As(alias string) *thirdShoplineOrder {
	t.thirdShoplineOrderDo.DO = *(t.thirdShoplineOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdShoplineOrder) updateTableName(table string) *thirdShoplineOrder {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.AdjustPrice = field.NewString(table, "adjust_price")
	t.AdjustPriceSet = field.NewString(table, "adjust_price_set")
	t.BillingAddress = field.NewString(table, "billing_address")
	t.BrowserIP = field.NewString(table, "browser_ip")
	t.BuyerNote = field.NewString(table, "buyer_note")
	t.CancelReason = field.NewString(table, "cancel_reason")
	t.CancelledAt = field.NewString(table, "cancelled_at")
	t.CartToken = field.NewString(table, "cart_token")
	t.CheckoutID = field.NewString(table, "checkout_id")
	t.CheckoutToken = field.NewString(table, "checkout_token")
	t.ClientDetails = field.NewString(table, "client_details")
	t.ContractSeq = field.NewString(table, "contract_seq")
	t.CreatedAt = field.NewString(table, "created_at")
	t.Currency = field.NewString(table, "currency")
	t.CurrentSubtotalPrice = field.NewString(table, "current_subtotal_price")
	t.CurrentSubtotalPriceSet = field.NewString(table, "current_subtotal_price_set")
	t.CurrentTotalDiscounts = field.NewString(table, "current_total_discounts")
	t.CurrentTotalDiscountsSet = field.NewString(table, "current_total_discounts_set")
	t.CurrentTotalDutiesSet = field.NewString(table, "current_total_duties_set")
	t.CurrentTotalPrice = field.NewString(table, "current_total_price")
	t.CurrentTotalPriceSet = field.NewString(table, "current_total_price_set")
	t.CurrentTotalTax = field.NewString(table, "current_total_tax")
	t.CurrentTotalTaxSet = field.NewString(table, "current_total_tax_set")
	t.Customer = field.NewString(table, "customer")
	t.CustomerLocale = field.NewString(table, "customer_locale")
	t.DeductMemberPointAmount = field.NewString(table, "deduct_member_point_amount")
	t.DeductMemberPointAmountSet = field.NewString(table, "deduct_member_point_amount_set")
	t.DiscountApplications = field.NewString(table, "discount_applications")
	t.DiscountCodes = field.NewString(table, "discount_codes")
	t.Email = field.NewString(table, "email")
	t.FinancialStatus = field.NewString(table, "financial_status")
	t.FulfillmentStatus = field.NewString(table, "fulfillment_status")
	t.Fulfillments = field.NewString(table, "fulfillments")
	t.HiddenOrder = field.NewBool(table, "hidden_order")
	t.LandingSite = field.NewString(table, "landing_site")
	t.LineItems = field.NewString(table, "line_items")
	t.Locations = field.NewString(table, "locations")
	t.Name = field.NewString(table, "name")
	t.Note = field.NewString(table, "note")
	t.NoteAttributes = field.NewString(table, "note_attributes")
	t.OrderAt = field.NewString(table, "order_at")
	t.OrderSource = field.NewString(table, "order_source")
	t.OrderStatusURL = field.NewString(table, "order_status_url")
	t.PaymentDetails = field.NewString(table, "payment_details")
	t.PaymentGatewayNames = field.NewString(table, "payment_gateway_names")
	t.Phone = field.NewString(table, "phone")
	t.PosLocationID = field.NewString(table, "pos_location_id")
	t.PresentmentCurrency = field.NewString(table, "presentment_currency")
	t.ProcessedAt = field.NewString(table, "processed_at")
	t.ProcessedUserID = field.NewString(table, "processed_user_id")
	t.ReferringSite = field.NewString(table, "referring_site")
	t.Refunds = field.NewString(table, "refunds")
	t.Service = field.NewString(table, "service")
	t.ShippingAddress = field.NewString(table, "shipping_address")
	t.ShippingLines = field.NewString(table, "shipping_lines")
	t.SourceIdentifier = field.NewString(table, "source_identifier")
	t.SourceName = field.NewString(table, "source_name")
	t.SourceURL = field.NewString(table, "source_url")
	t.Status = field.NewString(table, "status")
	t.StoreID = field.NewString(table, "store_id")
	t.SubtotalPrice = field.NewString(table, "subtotal_price")
	t.SubtotalPriceSet = field.NewString(table, "subtotal_price_set")
	t.Tags = field.NewString(table, "tags")
	t.TaxLines = field.NewString(table, "tax_lines")
	t.TaxNumber = field.NewString(table, "tax_number")
	t.TaxType = field.NewString(table, "tax_type")
	t.TaxesIncluded = field.NewBool(table, "taxes_included")
	t.TotalDiscounts = field.NewString(table, "total_discounts")
	t.TotalDiscountsSet = field.NewString(table, "total_discounts_set")
	t.TotalLineItemsPrice = field.NewString(table, "total_line_items_price")
	t.TotalLineItemsPriceSet = field.NewString(table, "total_line_items_price_set")
	t.TotalOutstanding = field.NewString(table, "total_outstanding")
	t.TotalShippingPriceSet = field.NewString(table, "total_shipping_price_set")
	t.TotalTax = field.NewString(table, "total_tax")
	t.TotalTaxSet = field.NewString(table, "total_tax_set")
	t.TotalTipReceived = field.NewString(table, "total_tip_received")
	t.TotalTipReceivedSet = field.NewString(table, "total_tip_received_set")
	t.TotalWeight = field.NewFloat64(table, "total_weight")
	t.UpdatedAt = field.NewString(table, "updated_at")
	t.UserID = field.NewString(table, "user_id")
	t.UtmParameters = field.NewString(table, "utm_parameters")

	t.fillFieldMap()

	return t
}

func (t *thirdShoplineOrder) WithContext(ctx context.Context) IThirdShoplineOrderDo {
	return t.thirdShoplineOrderDo.WithContext(ctx)
}

func (t thirdShoplineOrder) TableName() string { return t.thirdShoplineOrderDo.TableName() }

func (t thirdShoplineOrder) Alias() string { return t.thirdShoplineOrderDo.Alias() }

func (t thirdShoplineOrder) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdShoplineOrderDo.Columns(cols...)
}

func (t *thirdShoplineOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdShoplineOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 82)
	t.fieldMap["id"] = t.ID
	t.fieldMap["adjust_price"] = t.AdjustPrice
	t.fieldMap["adjust_price_set"] = t.AdjustPriceSet
	t.fieldMap["billing_address"] = t.BillingAddress
	t.fieldMap["browser_ip"] = t.BrowserIP
	t.fieldMap["buyer_note"] = t.BuyerNote
	t.fieldMap["cancel_reason"] = t.CancelReason
	t.fieldMap["cancelled_at"] = t.CancelledAt
	t.fieldMap["cart_token"] = t.CartToken
	t.fieldMap["checkout_id"] = t.CheckoutID
	t.fieldMap["checkout_token"] = t.CheckoutToken
	t.fieldMap["client_details"] = t.ClientDetails
	t.fieldMap["contract_seq"] = t.ContractSeq
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["currency"] = t.Currency
	t.fieldMap["current_subtotal_price"] = t.CurrentSubtotalPrice
	t.fieldMap["current_subtotal_price_set"] = t.CurrentSubtotalPriceSet
	t.fieldMap["current_total_discounts"] = t.CurrentTotalDiscounts
	t.fieldMap["current_total_discounts_set"] = t.CurrentTotalDiscountsSet
	t.fieldMap["current_total_duties_set"] = t.CurrentTotalDutiesSet
	t.fieldMap["current_total_price"] = t.CurrentTotalPrice
	t.fieldMap["current_total_price_set"] = t.CurrentTotalPriceSet
	t.fieldMap["current_total_tax"] = t.CurrentTotalTax
	t.fieldMap["current_total_tax_set"] = t.CurrentTotalTaxSet
	t.fieldMap["customer"] = t.Customer
	t.fieldMap["customer_locale"] = t.CustomerLocale
	t.fieldMap["deduct_member_point_amount"] = t.DeductMemberPointAmount
	t.fieldMap["deduct_member_point_amount_set"] = t.DeductMemberPointAmountSet
	t.fieldMap["discount_applications"] = t.DiscountApplications
	t.fieldMap["discount_codes"] = t.DiscountCodes
	t.fieldMap["email"] = t.Email
	t.fieldMap["financial_status"] = t.FinancialStatus
	t.fieldMap["fulfillment_status"] = t.FulfillmentStatus
	t.fieldMap["fulfillments"] = t.Fulfillments
	t.fieldMap["hidden_order"] = t.HiddenOrder
	t.fieldMap["landing_site"] = t.LandingSite
	t.fieldMap["line_items"] = t.LineItems
	t.fieldMap["locations"] = t.Locations
	t.fieldMap["name"] = t.Name
	t.fieldMap["note"] = t.Note
	t.fieldMap["note_attributes"] = t.NoteAttributes
	t.fieldMap["order_at"] = t.OrderAt
	t.fieldMap["order_source"] = t.OrderSource
	t.fieldMap["order_status_url"] = t.OrderStatusURL
	t.fieldMap["payment_details"] = t.PaymentDetails
	t.fieldMap["payment_gateway_names"] = t.PaymentGatewayNames
	t.fieldMap["phone"] = t.Phone
	t.fieldMap["pos_location_id"] = t.PosLocationID
	t.fieldMap["presentment_currency"] = t.PresentmentCurrency
	t.fieldMap["processed_at"] = t.ProcessedAt
	t.fieldMap["processed_user_id"] = t.ProcessedUserID
	t.fieldMap["referring_site"] = t.ReferringSite
	t.fieldMap["refunds"] = t.Refunds
	t.fieldMap["service"] = t.Service
	t.fieldMap["shipping_address"] = t.ShippingAddress
	t.fieldMap["shipping_lines"] = t.ShippingLines
	t.fieldMap["source_identifier"] = t.SourceIdentifier
	t.fieldMap["source_name"] = t.SourceName
	t.fieldMap["source_url"] = t.SourceURL
	t.fieldMap["status"] = t.Status
	t.fieldMap["store_id"] = t.StoreID
	t.fieldMap["subtotal_price"] = t.SubtotalPrice
	t.fieldMap["subtotal_price_set"] = t.SubtotalPriceSet
	t.fieldMap["tags"] = t.Tags
	t.fieldMap["tax_lines"] = t.TaxLines
	t.fieldMap["tax_number"] = t.TaxNumber
	t.fieldMap["tax_type"] = t.TaxType
	t.fieldMap["taxes_included"] = t.TaxesIncluded
	t.fieldMap["total_discounts"] = t.TotalDiscounts
	t.fieldMap["total_discounts_set"] = t.TotalDiscountsSet
	t.fieldMap["total_line_items_price"] = t.TotalLineItemsPrice
	t.fieldMap["total_line_items_price_set"] = t.TotalLineItemsPriceSet
	t.fieldMap["total_outstanding"] = t.TotalOutstanding
	t.fieldMap["total_shipping_price_set"] = t.TotalShippingPriceSet
	t.fieldMap["total_tax"] = t.TotalTax
	t.fieldMap["total_tax_set"] = t.TotalTaxSet
	t.fieldMap["total_tip_received"] = t.TotalTipReceived
	t.fieldMap["total_tip_received_set"] = t.TotalTipReceivedSet
	t.fieldMap["total_weight"] = t.TotalWeight
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["utm_parameters"] = t.UtmParameters
}

func (t thirdShoplineOrder) clone(db *gorm.DB) thirdShoplineOrder {
	t.thirdShoplineOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdShoplineOrder) replaceDB(db *gorm.DB) thirdShoplineOrder {
	t.thirdShoplineOrderDo.ReplaceDB(db)
	return t
}

type thirdShoplineOrderDo struct{ gen.DO }

type IThirdShoplineOrderDo interface {
	gen.SubQuery
	Debug() IThirdShoplineOrderDo
	WithContext(ctx context.Context) IThirdShoplineOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdShoplineOrderDo
	WriteDB() IThirdShoplineOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdShoplineOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdShoplineOrderDo
	Not(conds ...gen.Condition) IThirdShoplineOrderDo
	Or(conds ...gen.Condition) IThirdShoplineOrderDo
	Select(conds ...field.Expr) IThirdShoplineOrderDo
	Where(conds ...gen.Condition) IThirdShoplineOrderDo
	Order(conds ...field.Expr) IThirdShoplineOrderDo
	Distinct(cols ...field.Expr) IThirdShoplineOrderDo
	Omit(cols ...field.Expr) IThirdShoplineOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo
	Group(cols ...field.Expr) IThirdShoplineOrderDo
	Having(conds ...gen.Condition) IThirdShoplineOrderDo
	Limit(limit int) IThirdShoplineOrderDo
	Offset(offset int) IThirdShoplineOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShoplineOrderDo
	Unscoped() IThirdShoplineOrderDo
	Create(values ...*model.ThirdShoplineOrder) error
	CreateInBatches(values model.ThirdShoplineOrderSlice, batchSize int) error
	Save(values ...*model.ThirdShoplineOrder) error
	First() (*model.ThirdShoplineOrder, error)
	Take() (*model.ThirdShoplineOrder, error)
	Last() (*model.ThirdShoplineOrder, error)
	Find() (model.ThirdShoplineOrderSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShoplineOrderSlice, err error)
	FindInBatches(result *model.ThirdShoplineOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdShoplineOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdShoplineOrderDo
	Assign(attrs ...field.AssignExpr) IThirdShoplineOrderDo
	Joins(fields ...field.RelationField) IThirdShoplineOrderDo
	Preload(fields ...field.RelationField) IThirdShoplineOrderDo
	FirstOrInit() (*model.ThirdShoplineOrder, error)
	FirstOrCreate() (*model.ThirdShoplineOrder, error)
	FindByPage(offset int, limit int) (result model.ThirdShoplineOrderSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdShoplineOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdShoplineOrderDo) Debug() IThirdShoplineOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdShoplineOrderDo) WithContext(ctx context.Context) IThirdShoplineOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdShoplineOrderDo) ReadDB() IThirdShoplineOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdShoplineOrderDo) WriteDB() IThirdShoplineOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdShoplineOrderDo) Session(config *gorm.Session) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdShoplineOrderDo) Clauses(conds ...clause.Expression) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdShoplineOrderDo) Returning(value interface{}, columns ...string) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdShoplineOrderDo) Not(conds ...gen.Condition) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdShoplineOrderDo) Or(conds ...gen.Condition) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdShoplineOrderDo) Select(conds ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdShoplineOrderDo) Where(conds ...gen.Condition) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdShoplineOrderDo) Order(conds ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdShoplineOrderDo) Distinct(cols ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdShoplineOrderDo) Omit(cols ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdShoplineOrderDo) Join(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdShoplineOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdShoplineOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdShoplineOrderDo) Group(cols ...field.Expr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdShoplineOrderDo) Having(conds ...gen.Condition) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdShoplineOrderDo) Limit(limit int) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdShoplineOrderDo) Offset(offset int) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdShoplineOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdShoplineOrderDo) Unscoped() IThirdShoplineOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdShoplineOrderDo) Create(values ...*model.ThirdShoplineOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdShoplineOrderDo) CreateInBatches(values model.ThirdShoplineOrderSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdShoplineOrderDo) Save(values ...*model.ThirdShoplineOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdShoplineOrderDo) First() (*model.ThirdShoplineOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineOrder), nil
	}
}

func (t thirdShoplineOrderDo) Take() (*model.ThirdShoplineOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineOrder), nil
	}
}

func (t thirdShoplineOrderDo) Last() (*model.ThirdShoplineOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineOrder), nil
	}
}

func (t thirdShoplineOrderDo) Find() (model.ThirdShoplineOrderSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdShoplineOrderSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdShoplineOrder); ok {
		return model.ThirdShoplineOrderSlice(slice), err
	}
	return model.ThirdShoplineOrderSlice{}, err

}

func (t thirdShoplineOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdShoplineOrderSlice, err error) {
	buf := make([]*model.ThirdShoplineOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdShoplineOrderDo) FindInBatches(result *model.ThirdShoplineOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdShoplineOrderDo) Attrs(attrs ...field.AssignExpr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdShoplineOrderDo) Assign(attrs ...field.AssignExpr) IThirdShoplineOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdShoplineOrderDo) Joins(fields ...field.RelationField) IThirdShoplineOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdShoplineOrderDo) Preload(fields ...field.RelationField) IThirdShoplineOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdShoplineOrderDo) FirstOrInit() (*model.ThirdShoplineOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineOrder), nil
	}
}

func (t thirdShoplineOrderDo) FirstOrCreate() (*model.ThirdShoplineOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdShoplineOrder), nil
	}
}

func (t thirdShoplineOrderDo) FindByPage(offset int, limit int) (result model.ThirdShoplineOrderSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdShoplineOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdShoplineOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdShoplineOrderDo) Delete(models ...*model.ThirdShoplineOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdShoplineOrderDo) withDO(do gen.Dao) *thirdShoplineOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
