// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseIn(db *gorm.DB, opts ...gen.DOOption) warehouseIn {
	_warehouseIn := warehouseIn{}

	_warehouseIn.warehouseInDo.UseDB(db, opts...)
	_warehouseIn.warehouseInDo.UseModel(&model.WarehouseIn{})

	tableName := _warehouseIn.warehouseInDo.TableName()
	_warehouseIn.ALL = field.NewAsterisk(tableName)
	_warehouseIn.ID = field.NewInt32(tableName, "id")
	_warehouseIn.SourceNo = field.NewString(tableName, "source_no")
	_warehouseIn.OrderNo = field.NewString(tableName, "order_no")
	_warehouseIn.Type = field.NewString(tableName, "type")
	_warehouseIn.CreatedTime = field.NewTime(tableName, "created_time")
	_warehouseIn.SyncTime = field.NewTime(tableName, "sync_time")
	_warehouseIn.State = field.NewString(tableName, "state")
	_warehouseIn.CreatedUser = field.NewString(tableName, "created_user")
	_warehouseIn.Remark = field.NewString(tableName, "remark")
	_warehouseIn.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_warehouseIn.fillFieldMap()

	return _warehouseIn
}

type warehouseIn struct {
	warehouseInDo warehouseInDo

	ALL           field.Asterisk
	ID            field.Int32
	SourceNo      field.String
	OrderNo       field.String
	Type          field.String
	CreatedTime   field.Time
	SyncTime      field.Time
	State         field.String
	CreatedUser   field.String
	Remark        field.String
	WarehouseCode field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (w warehouseIn) Table(newTableName string) *warehouseIn {
	w.warehouseInDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseIn) As(alias string) *warehouseIn {
	w.warehouseInDo.DO = *(w.warehouseInDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseIn) updateTableName(table string) *warehouseIn {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.SourceNo = field.NewString(table, "source_no")
	w.OrderNo = field.NewString(table, "order_no")
	w.Type = field.NewString(table, "type")
	w.CreatedTime = field.NewTime(table, "created_time")
	w.SyncTime = field.NewTime(table, "sync_time")
	w.State = field.NewString(table, "state")
	w.CreatedUser = field.NewString(table, "created_user")
	w.Remark = field.NewString(table, "remark")
	w.WarehouseCode = field.NewString(table, "warehouse_code")

	w.fillFieldMap()

	return w
}

func (w *warehouseIn) WithContext(ctx context.Context) IWarehouseInDo {
	return w.warehouseInDo.WithContext(ctx)
}

func (w warehouseIn) TableName() string { return w.warehouseInDo.TableName() }

func (w warehouseIn) Alias() string { return w.warehouseInDo.Alias() }

func (w warehouseIn) Columns(cols ...field.Expr) gen.Columns { return w.warehouseInDo.Columns(cols...) }

func (w *warehouseIn) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseIn) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 10)
	w.fieldMap["id"] = w.ID
	w.fieldMap["source_no"] = w.SourceNo
	w.fieldMap["order_no"] = w.OrderNo
	w.fieldMap["type"] = w.Type
	w.fieldMap["created_time"] = w.CreatedTime
	w.fieldMap["sync_time"] = w.SyncTime
	w.fieldMap["state"] = w.State
	w.fieldMap["created_user"] = w.CreatedUser
	w.fieldMap["remark"] = w.Remark
	w.fieldMap["warehouse_code"] = w.WarehouseCode
}

func (w warehouseIn) clone(db *gorm.DB) warehouseIn {
	w.warehouseInDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseIn) replaceDB(db *gorm.DB) warehouseIn {
	w.warehouseInDo.ReplaceDB(db)
	return w
}

type warehouseInDo struct{ gen.DO }

type IWarehouseInDo interface {
	gen.SubQuery
	Debug() IWarehouseInDo
	WithContext(ctx context.Context) IWarehouseInDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseInDo
	WriteDB() IWarehouseInDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseInDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseInDo
	Not(conds ...gen.Condition) IWarehouseInDo
	Or(conds ...gen.Condition) IWarehouseInDo
	Select(conds ...field.Expr) IWarehouseInDo
	Where(conds ...gen.Condition) IWarehouseInDo
	Order(conds ...field.Expr) IWarehouseInDo
	Distinct(cols ...field.Expr) IWarehouseInDo
	Omit(cols ...field.Expr) IWarehouseInDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseInDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseInDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseInDo
	Group(cols ...field.Expr) IWarehouseInDo
	Having(conds ...gen.Condition) IWarehouseInDo
	Limit(limit int) IWarehouseInDo
	Offset(offset int) IWarehouseInDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseInDo
	Unscoped() IWarehouseInDo
	Create(values ...*model.WarehouseIn) error
	CreateInBatches(values model.WarehouseInSlice, batchSize int) error
	Save(values ...*model.WarehouseIn) error
	First() (*model.WarehouseIn, error)
	Take() (*model.WarehouseIn, error)
	Last() (*model.WarehouseIn, error)
	Find() (model.WarehouseInSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseInSlice, err error)
	FindInBatches(result *model.WarehouseInSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseIn) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseInDo
	Assign(attrs ...field.AssignExpr) IWarehouseInDo
	Joins(fields ...field.RelationField) IWarehouseInDo
	Preload(fields ...field.RelationField) IWarehouseInDo
	FirstOrInit() (*model.WarehouseIn, error)
	FirstOrCreate() (*model.WarehouseIn, error)
	FindByPage(offset int, limit int) (result model.WarehouseInSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseInDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseInDo) Debug() IWarehouseInDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseInDo) WithContext(ctx context.Context) IWarehouseInDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseInDo) ReadDB() IWarehouseInDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseInDo) WriteDB() IWarehouseInDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseInDo) Session(config *gorm.Session) IWarehouseInDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseInDo) Clauses(conds ...clause.Expression) IWarehouseInDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseInDo) Returning(value interface{}, columns ...string) IWarehouseInDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseInDo) Not(conds ...gen.Condition) IWarehouseInDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseInDo) Or(conds ...gen.Condition) IWarehouseInDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseInDo) Select(conds ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseInDo) Where(conds ...gen.Condition) IWarehouseInDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseInDo) Order(conds ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseInDo) Distinct(cols ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseInDo) Omit(cols ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseInDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseInDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseInDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseInDo) Group(cols ...field.Expr) IWarehouseInDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseInDo) Having(conds ...gen.Condition) IWarehouseInDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseInDo) Limit(limit int) IWarehouseInDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseInDo) Offset(offset int) IWarehouseInDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseInDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseInDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseInDo) Unscoped() IWarehouseInDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseInDo) Create(values ...*model.WarehouseIn) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseInDo) CreateInBatches(values model.WarehouseInSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseInDo) Save(values ...*model.WarehouseIn) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseInDo) First() (*model.WarehouseIn, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseIn), nil
	}
}

func (w warehouseInDo) Take() (*model.WarehouseIn, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseIn), nil
	}
}

func (w warehouseInDo) Last() (*model.WarehouseIn, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseIn), nil
	}
}

func (w warehouseInDo) Find() (model.WarehouseInSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseInSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseIn); ok {
		return model.WarehouseInSlice(slice), err
	}
	return model.WarehouseInSlice{}, err

}

func (w warehouseInDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseInSlice, err error) {
	buf := make([]*model.WarehouseIn, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseInDo) FindInBatches(result *model.WarehouseInSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseInDo) Attrs(attrs ...field.AssignExpr) IWarehouseInDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseInDo) Assign(attrs ...field.AssignExpr) IWarehouseInDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseInDo) Joins(fields ...field.RelationField) IWarehouseInDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseInDo) Preload(fields ...field.RelationField) IWarehouseInDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseInDo) FirstOrInit() (*model.WarehouseIn, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseIn), nil
	}
}

func (w warehouseInDo) FirstOrCreate() (*model.WarehouseIn, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseIn), nil
	}
}

func (w warehouseInDo) FindByPage(offset int, limit int) (result model.WarehouseInSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseInDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseInDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseInDo) Delete(models ...*model.WarehouseIn) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseInDo) withDO(do gen.Dao) *warehouseInDo {
	w.DO = *do.(*gen.DO)
	return w
}
