// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouseOutReceiveAddress(db *gorm.DB, opts ...gen.DOOption) warehouseOutReceiveAddress {
	_warehouseOutReceiveAddress := warehouseOutReceiveAddress{}

	_warehouseOutReceiveAddress.warehouseOutReceiveAddressDo.UseDB(db, opts...)
	_warehouseOutReceiveAddress.warehouseOutReceiveAddressDo.UseModel(&model.WarehouseOutReceiveAddress{})

	tableName := _warehouseOutReceiveAddress.warehouseOutReceiveAddressDo.TableName()
	_warehouseOutReceiveAddress.ALL = field.NewAsterisk(tableName)
	_warehouseOutReceiveAddress.ID = field.NewInt32(tableName, "id")
	_warehouseOutReceiveAddress.Company = field.NewString(tableName, "company")
	_warehouseOutReceiveAddress.Name = field.NewString(tableName, "name")
	_warehouseOutReceiveAddress.Mobile = field.NewString(tableName, "mobile")
	_warehouseOutReceiveAddress.Province = field.NewString(tableName, "province")
	_warehouseOutReceiveAddress.City = field.NewString(tableName, "city")
	_warehouseOutReceiveAddress.Area = field.NewString(tableName, "area")
	_warehouseOutReceiveAddress.Town = field.NewString(tableName, "town")
	_warehouseOutReceiveAddress.Detail = field.NewString(tableName, "detail")

	_warehouseOutReceiveAddress.fillFieldMap()

	return _warehouseOutReceiveAddress
}

type warehouseOutReceiveAddress struct {
	warehouseOutReceiveAddressDo warehouseOutReceiveAddressDo

	ALL      field.Asterisk
	ID       field.Int32
	Company  field.String
	Name     field.String
	Mobile   field.String
	Province field.String
	City     field.String
	Area     field.String
	Town     field.String
	Detail   field.String

	fieldMap map[string]field.Expr
}

func (w warehouseOutReceiveAddress) Table(newTableName string) *warehouseOutReceiveAddress {
	w.warehouseOutReceiveAddressDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouseOutReceiveAddress) As(alias string) *warehouseOutReceiveAddress {
	w.warehouseOutReceiveAddressDo.DO = *(w.warehouseOutReceiveAddressDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouseOutReceiveAddress) updateTableName(table string) *warehouseOutReceiveAddress {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt32(table, "id")
	w.Company = field.NewString(table, "company")
	w.Name = field.NewString(table, "name")
	w.Mobile = field.NewString(table, "mobile")
	w.Province = field.NewString(table, "province")
	w.City = field.NewString(table, "city")
	w.Area = field.NewString(table, "area")
	w.Town = field.NewString(table, "town")
	w.Detail = field.NewString(table, "detail")

	w.fillFieldMap()

	return w
}

func (w *warehouseOutReceiveAddress) WithContext(ctx context.Context) IWarehouseOutReceiveAddressDo {
	return w.warehouseOutReceiveAddressDo.WithContext(ctx)
}

func (w warehouseOutReceiveAddress) TableName() string {
	return w.warehouseOutReceiveAddressDo.TableName()
}

func (w warehouseOutReceiveAddress) Alias() string { return w.warehouseOutReceiveAddressDo.Alias() }

func (w warehouseOutReceiveAddress) Columns(cols ...field.Expr) gen.Columns {
	return w.warehouseOutReceiveAddressDo.Columns(cols...)
}

func (w *warehouseOutReceiveAddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouseOutReceiveAddress) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 9)
	w.fieldMap["id"] = w.ID
	w.fieldMap["company"] = w.Company
	w.fieldMap["name"] = w.Name
	w.fieldMap["mobile"] = w.Mobile
	w.fieldMap["province"] = w.Province
	w.fieldMap["city"] = w.City
	w.fieldMap["area"] = w.Area
	w.fieldMap["town"] = w.Town
	w.fieldMap["detail"] = w.Detail
}

func (w warehouseOutReceiveAddress) clone(db *gorm.DB) warehouseOutReceiveAddress {
	w.warehouseOutReceiveAddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouseOutReceiveAddress) replaceDB(db *gorm.DB) warehouseOutReceiveAddress {
	w.warehouseOutReceiveAddressDo.ReplaceDB(db)
	return w
}

type warehouseOutReceiveAddressDo struct{ gen.DO }

type IWarehouseOutReceiveAddressDo interface {
	gen.SubQuery
	Debug() IWarehouseOutReceiveAddressDo
	WithContext(ctx context.Context) IWarehouseOutReceiveAddressDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseOutReceiveAddressDo
	WriteDB() IWarehouseOutReceiveAddressDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseOutReceiveAddressDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseOutReceiveAddressDo
	Not(conds ...gen.Condition) IWarehouseOutReceiveAddressDo
	Or(conds ...gen.Condition) IWarehouseOutReceiveAddressDo
	Select(conds ...field.Expr) IWarehouseOutReceiveAddressDo
	Where(conds ...gen.Condition) IWarehouseOutReceiveAddressDo
	Order(conds ...field.Expr) IWarehouseOutReceiveAddressDo
	Distinct(cols ...field.Expr) IWarehouseOutReceiveAddressDo
	Omit(cols ...field.Expr) IWarehouseOutReceiveAddressDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo
	Group(cols ...field.Expr) IWarehouseOutReceiveAddressDo
	Having(conds ...gen.Condition) IWarehouseOutReceiveAddressDo
	Limit(limit int) IWarehouseOutReceiveAddressDo
	Offset(offset int) IWarehouseOutReceiveAddressDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutReceiveAddressDo
	Unscoped() IWarehouseOutReceiveAddressDo
	Create(values ...*model.WarehouseOutReceiveAddress) error
	CreateInBatches(values model.WarehouseOutReceiveAddressSlice, batchSize int) error
	Save(values ...*model.WarehouseOutReceiveAddress) error
	First() (*model.WarehouseOutReceiveAddress, error)
	Take() (*model.WarehouseOutReceiveAddress, error)
	Last() (*model.WarehouseOutReceiveAddress, error)
	Find() (model.WarehouseOutReceiveAddressSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutReceiveAddressSlice, err error)
	FindInBatches(result *model.WarehouseOutReceiveAddressSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.WarehouseOutReceiveAddress) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseOutReceiveAddressDo
	Assign(attrs ...field.AssignExpr) IWarehouseOutReceiveAddressDo
	Joins(fields ...field.RelationField) IWarehouseOutReceiveAddressDo
	Preload(fields ...field.RelationField) IWarehouseOutReceiveAddressDo
	FirstOrInit() (*model.WarehouseOutReceiveAddress, error)
	FirstOrCreate() (*model.WarehouseOutReceiveAddress, error)
	FindByPage(offset int, limit int) (result model.WarehouseOutReceiveAddressSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseOutReceiveAddressDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseOutReceiveAddressDo) Debug() IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseOutReceiveAddressDo) WithContext(ctx context.Context) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseOutReceiveAddressDo) ReadDB() IWarehouseOutReceiveAddressDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseOutReceiveAddressDo) WriteDB() IWarehouseOutReceiveAddressDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseOutReceiveAddressDo) Session(config *gorm.Session) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseOutReceiveAddressDo) Clauses(conds ...clause.Expression) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseOutReceiveAddressDo) Returning(value interface{}, columns ...string) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseOutReceiveAddressDo) Not(conds ...gen.Condition) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseOutReceiveAddressDo) Or(conds ...gen.Condition) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseOutReceiveAddressDo) Select(conds ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseOutReceiveAddressDo) Where(conds ...gen.Condition) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseOutReceiveAddressDo) Order(conds ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseOutReceiveAddressDo) Distinct(cols ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseOutReceiveAddressDo) Omit(cols ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseOutReceiveAddressDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseOutReceiveAddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseOutReceiveAddressDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseOutReceiveAddressDo) Group(cols ...field.Expr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseOutReceiveAddressDo) Having(conds ...gen.Condition) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseOutReceiveAddressDo) Limit(limit int) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseOutReceiveAddressDo) Offset(offset int) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseOutReceiveAddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseOutReceiveAddressDo) Unscoped() IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseOutReceiveAddressDo) Create(values ...*model.WarehouseOutReceiveAddress) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseOutReceiveAddressDo) CreateInBatches(values model.WarehouseOutReceiveAddressSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseOutReceiveAddressDo) Save(values ...*model.WarehouseOutReceiveAddress) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseOutReceiveAddressDo) First() (*model.WarehouseOutReceiveAddress, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutReceiveAddress), nil
	}
}

func (w warehouseOutReceiveAddressDo) Take() (*model.WarehouseOutReceiveAddress, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutReceiveAddress), nil
	}
}

func (w warehouseOutReceiveAddressDo) Last() (*model.WarehouseOutReceiveAddress, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutReceiveAddress), nil
	}
}

func (w warehouseOutReceiveAddressDo) Find() (model.WarehouseOutReceiveAddressSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseOutReceiveAddressSlice{}, err
	}
	if slice, ok := result.([]*model.WarehouseOutReceiveAddress); ok {
		return model.WarehouseOutReceiveAddressSlice(slice), err
	}
	return model.WarehouseOutReceiveAddressSlice{}, err

}

func (w warehouseOutReceiveAddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseOutReceiveAddressSlice, err error) {
	buf := make([]*model.WarehouseOutReceiveAddress, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseOutReceiveAddressDo) FindInBatches(result *model.WarehouseOutReceiveAddressSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseOutReceiveAddressDo) Attrs(attrs ...field.AssignExpr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseOutReceiveAddressDo) Assign(attrs ...field.AssignExpr) IWarehouseOutReceiveAddressDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseOutReceiveAddressDo) Joins(fields ...field.RelationField) IWarehouseOutReceiveAddressDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseOutReceiveAddressDo) Preload(fields ...field.RelationField) IWarehouseOutReceiveAddressDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseOutReceiveAddressDo) FirstOrInit() (*model.WarehouseOutReceiveAddress, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutReceiveAddress), nil
	}
}

func (w warehouseOutReceiveAddressDo) FirstOrCreate() (*model.WarehouseOutReceiveAddress, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.WarehouseOutReceiveAddress), nil
	}
}

func (w warehouseOutReceiveAddressDo) FindByPage(offset int, limit int) (result model.WarehouseOutReceiveAddressSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseOutReceiveAddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseOutReceiveAddressDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseOutReceiveAddressDo) Delete(models ...*model.WarehouseOutReceiveAddress) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseOutReceiveAddressDo) withDO(do gen.Dao) *warehouseOutReceiveAddressDo {
	w.DO = *do.(*gen.DO)
	return w
}
