// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockStrategy(db *gorm.DB, opts ...gen.DOOption) stockStrategy {
	_stockStrategy := stockStrategy{}

	_stockStrategy.stockStrategyDo.UseDB(db, opts...)
	_stockStrategy.stockStrategyDo.UseModel(&model.StockStrategy{})

	tableName := _stockStrategy.stockStrategyDo.TableName()
	_stockStrategy.ALL = field.NewAsterisk(tableName)
	_stockStrategy.ID = field.NewInt32(tableName, "id")
	_stockStrategy.Name = field.NewString(tableName, "name")
	_stockStrategy.ShopType = field.NewString(tableName, "shop_type")
	_stockStrategy.CalcType = field.NewString(tableName, "calc_type")
	_stockStrategy.DimensionType = field.NewString(tableName, "dimension_type")
	_stockStrategy.Proportion = field.NewInt32(tableName, "proportion")
	_stockStrategy.Num = field.NewInt32(tableName, "num")
	_stockStrategy.WorkType = field.NewString(tableName, "work_type")
	_stockStrategy.WorkTime = field.NewTime(tableName, "work_time")
	_stockStrategy.SyncTime = field.NewTime(tableName, "sync_time")
	_stockStrategy.CreatedTime = field.NewTime(tableName, "created_time")
	_stockStrategy.State = field.NewString(tableName, "state")
	_stockStrategy.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_stockStrategy.fillFieldMap()

	return _stockStrategy
}

type stockStrategy struct {
	stockStrategyDo stockStrategyDo

	ALL           field.Asterisk
	ID            field.Int32
	Name          field.String
	ShopType      field.String
	CalcType      field.String
	DimensionType field.String
	Proportion    field.Int32
	Num           field.Int32
	WorkType      field.String
	WorkTime      field.Time
	SyncTime      field.Time
	CreatedTime   field.Time
	State         field.String
	WarehouseCode field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (s stockStrategy) Table(newTableName string) *stockStrategy {
	s.stockStrategyDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockStrategy) As(alias string) *stockStrategy {
	s.stockStrategyDo.DO = *(s.stockStrategyDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockStrategy) updateTableName(table string) *stockStrategy {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Name = field.NewString(table, "name")
	s.ShopType = field.NewString(table, "shop_type")
	s.CalcType = field.NewString(table, "calc_type")
	s.DimensionType = field.NewString(table, "dimension_type")
	s.Proportion = field.NewInt32(table, "proportion")
	s.Num = field.NewInt32(table, "num")
	s.WorkType = field.NewString(table, "work_type")
	s.WorkTime = field.NewTime(table, "work_time")
	s.SyncTime = field.NewTime(table, "sync_time")
	s.CreatedTime = field.NewTime(table, "created_time")
	s.State = field.NewString(table, "state")
	s.WarehouseCode = field.NewString(table, "warehouse_code")

	s.fillFieldMap()

	return s
}

func (s *stockStrategy) WithContext(ctx context.Context) IStockStrategyDo {
	return s.stockStrategyDo.WithContext(ctx)
}

func (s stockStrategy) TableName() string { return s.stockStrategyDo.TableName() }

func (s stockStrategy) Alias() string { return s.stockStrategyDo.Alias() }

func (s stockStrategy) Columns(cols ...field.Expr) gen.Columns {
	return s.stockStrategyDo.Columns(cols...)
}

func (s *stockStrategy) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockStrategy) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["shop_type"] = s.ShopType
	s.fieldMap["calc_type"] = s.CalcType
	s.fieldMap["dimension_type"] = s.DimensionType
	s.fieldMap["proportion"] = s.Proportion
	s.fieldMap["num"] = s.Num
	s.fieldMap["work_type"] = s.WorkType
	s.fieldMap["work_time"] = s.WorkTime
	s.fieldMap["sync_time"] = s.SyncTime
	s.fieldMap["created_time"] = s.CreatedTime
	s.fieldMap["state"] = s.State
	s.fieldMap["warehouse_code"] = s.WarehouseCode
}

func (s stockStrategy) clone(db *gorm.DB) stockStrategy {
	s.stockStrategyDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockStrategy) replaceDB(db *gorm.DB) stockStrategy {
	s.stockStrategyDo.ReplaceDB(db)
	return s
}

type stockStrategyDo struct{ gen.DO }

type IStockStrategyDo interface {
	gen.SubQuery
	Debug() IStockStrategyDo
	WithContext(ctx context.Context) IStockStrategyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockStrategyDo
	WriteDB() IStockStrategyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockStrategyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockStrategyDo
	Not(conds ...gen.Condition) IStockStrategyDo
	Or(conds ...gen.Condition) IStockStrategyDo
	Select(conds ...field.Expr) IStockStrategyDo
	Where(conds ...gen.Condition) IStockStrategyDo
	Order(conds ...field.Expr) IStockStrategyDo
	Distinct(cols ...field.Expr) IStockStrategyDo
	Omit(cols ...field.Expr) IStockStrategyDo
	Join(table schema.Tabler, on ...field.Expr) IStockStrategyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyDo
	Group(cols ...field.Expr) IStockStrategyDo
	Having(conds ...gen.Condition) IStockStrategyDo
	Limit(limit int) IStockStrategyDo
	Offset(offset int) IStockStrategyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyDo
	Unscoped() IStockStrategyDo
	Create(values ...*model.StockStrategy) error
	CreateInBatches(values model.StockStrategySlice, batchSize int) error
	Save(values ...*model.StockStrategy) error
	First() (*model.StockStrategy, error)
	Take() (*model.StockStrategy, error)
	Last() (*model.StockStrategy, error)
	Find() (model.StockStrategySlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategySlice, err error)
	FindInBatches(result *model.StockStrategySlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockStrategy) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockStrategyDo
	Assign(attrs ...field.AssignExpr) IStockStrategyDo
	Joins(fields ...field.RelationField) IStockStrategyDo
	Preload(fields ...field.RelationField) IStockStrategyDo
	FirstOrInit() (*model.StockStrategy, error)
	FirstOrCreate() (*model.StockStrategy, error)
	FindByPage(offset int, limit int) (result model.StockStrategySlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockStrategyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockStrategyDo) Debug() IStockStrategyDo {
	return s.withDO(s.DO.Debug())
}

func (s stockStrategyDo) WithContext(ctx context.Context) IStockStrategyDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockStrategyDo) ReadDB() IStockStrategyDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockStrategyDo) WriteDB() IStockStrategyDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockStrategyDo) Session(config *gorm.Session) IStockStrategyDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockStrategyDo) Clauses(conds ...clause.Expression) IStockStrategyDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockStrategyDo) Returning(value interface{}, columns ...string) IStockStrategyDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockStrategyDo) Not(conds ...gen.Condition) IStockStrategyDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockStrategyDo) Or(conds ...gen.Condition) IStockStrategyDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockStrategyDo) Select(conds ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockStrategyDo) Where(conds ...gen.Condition) IStockStrategyDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockStrategyDo) Order(conds ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockStrategyDo) Distinct(cols ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockStrategyDo) Omit(cols ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockStrategyDo) Join(table schema.Tabler, on ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockStrategyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockStrategyDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockStrategyDo) Group(cols ...field.Expr) IStockStrategyDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockStrategyDo) Having(conds ...gen.Condition) IStockStrategyDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockStrategyDo) Limit(limit int) IStockStrategyDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockStrategyDo) Offset(offset int) IStockStrategyDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockStrategyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockStrategyDo) Unscoped() IStockStrategyDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockStrategyDo) Create(values ...*model.StockStrategy) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockStrategyDo) CreateInBatches(values model.StockStrategySlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockStrategyDo) Save(values ...*model.StockStrategy) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockStrategyDo) First() (*model.StockStrategy, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategy), nil
	}
}

func (s stockStrategyDo) Take() (*model.StockStrategy, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategy), nil
	}
}

func (s stockStrategyDo) Last() (*model.StockStrategy, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategy), nil
	}
}

func (s stockStrategyDo) Find() (model.StockStrategySlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockStrategySlice{}, err
	}
	if slice, ok := result.([]*model.StockStrategy); ok {
		return model.StockStrategySlice(slice), err
	}
	return model.StockStrategySlice{}, err

}

func (s stockStrategyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategySlice, err error) {
	buf := make([]*model.StockStrategy, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockStrategyDo) FindInBatches(result *model.StockStrategySlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockStrategyDo) Attrs(attrs ...field.AssignExpr) IStockStrategyDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockStrategyDo) Assign(attrs ...field.AssignExpr) IStockStrategyDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockStrategyDo) Joins(fields ...field.RelationField) IStockStrategyDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockStrategyDo) Preload(fields ...field.RelationField) IStockStrategyDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockStrategyDo) FirstOrInit() (*model.StockStrategy, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategy), nil
	}
}

func (s stockStrategyDo) FirstOrCreate() (*model.StockStrategy, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategy), nil
	}
}

func (s stockStrategyDo) FindByPage(offset int, limit int) (result model.StockStrategySlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockStrategyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockStrategyDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockStrategyDo) Delete(models ...*model.StockStrategy) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockStrategyDo) withDO(do gen.Dao) *stockStrategyDo {
	s.DO = *do.(*gen.DO)
	return s
}
