// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdTokopediaOrder(db *gorm.DB, opts ...gen.DOOption) thirdTokopediaOrder {
	_thirdTokopediaOrder := thirdTokopediaOrder{}

	_thirdTokopediaOrder.thirdTokopediaOrderDo.UseDB(db, opts...)
	_thirdTokopediaOrder.thirdTokopediaOrderDo.UseModel(&model.ThirdTokopediaOrder{})

	tableName := _thirdTokopediaOrder.thirdTokopediaOrderDo.TableName()
	_thirdTokopediaOrder.ALL = field.NewAsterisk(tableName)
	_thirdTokopediaOrder.OrderID = field.NewInt64(tableName, "order_id")
	_thirdTokopediaOrder.BuyerID = field.NewInt64(tableName, "buyer_id")
	_thirdTokopediaOrder.SellerID = field.NewInt64(tableName, "seller_id")
	_thirdTokopediaOrder.PaymentID = field.NewInt64(tableName, "payment_id")
	_thirdTokopediaOrder.IsAffiliate = field.NewBool(tableName, "is_affiliate")
	_thirdTokopediaOrder.IsFulfillment = field.NewBool(tableName, "is_fulfillment")
	_thirdTokopediaOrder.OrderWarehouse = field.NewString(tableName, "order_warehouse")
	_thirdTokopediaOrder.OrderStatus = field.NewInt32(tableName, "order_status")
	_thirdTokopediaOrder.InvoiceNumber = field.NewString(tableName, "invoice_number")
	_thirdTokopediaOrder.InvoicePdf = field.NewString(tableName, "invoice_pdf")
	_thirdTokopediaOrder.InvoiceURL = field.NewString(tableName, "invoice_url")
	_thirdTokopediaOrder.OpenAmt = field.NewInt32(tableName, "open_amt")
	_thirdTokopediaOrder.LpAmt = field.NewInt32(tableName, "lp_amt")
	_thirdTokopediaOrder.CashbackAmt = field.NewInt32(tableName, "cashback_amt")
	_thirdTokopediaOrder.Info = field.NewString(tableName, "info")
	_thirdTokopediaOrder.Comment = field.NewString(tableName, "comment")
	_thirdTokopediaOrder.ItemPrice = field.NewInt32(tableName, "item_price")
	_thirdTokopediaOrder.BuyerInfo = field.NewString(tableName, "buyer_info")
	_thirdTokopediaOrder.ShopInfo = field.NewString(tableName, "shop_info")
	_thirdTokopediaOrder.ShipmentFulfillment = field.NewString(tableName, "shipment_fulfillment")
	_thirdTokopediaOrder.Preorder = field.NewString(tableName, "preorder")
	_thirdTokopediaOrder.OrderInfo = field.NewString(tableName, "order_info")
	_thirdTokopediaOrder.OriginInfo = field.NewString(tableName, "origin_info")
	_thirdTokopediaOrder.PaymentInfo = field.NewString(tableName, "payment_info")
	_thirdTokopediaOrder.InsuranceInfo = field.NewString(tableName, "insurance_info")
	_thirdTokopediaOrder.CreateTime = field.NewString(tableName, "create_time")
	_thirdTokopediaOrder.ShippingDate = field.NewString(tableName, "shipping_date")
	_thirdTokopediaOrder.UpdateTime = field.NewString(tableName, "update_time")
	_thirdTokopediaOrder.PaymentDate = field.NewString(tableName, "payment_date")
	_thirdTokopediaOrder.DeliveredDate = field.NewString(tableName, "delivered_date")
	_thirdTokopediaOrder.EstShippingDate = field.NewString(tableName, "est_shipping_date")
	_thirdTokopediaOrder.EstDeliveryDate = field.NewString(tableName, "est_delivery_date")
	_thirdTokopediaOrder.PromoOrderDetail = field.NewString(tableName, "promo_order_detail")
	_thirdTokopediaOrder.PofInfo = field.NewString(tableName, "pof_info")

	_thirdTokopediaOrder.fillFieldMap()

	return _thirdTokopediaOrder
}

type thirdTokopediaOrder struct {
	thirdTokopediaOrderDo thirdTokopediaOrderDo

	ALL                 field.Asterisk
	OrderID             field.Int64
	BuyerID             field.Int64
	SellerID            field.Int64
	PaymentID           field.Int64
	IsAffiliate         field.Bool
	IsFulfillment       field.Bool
	OrderWarehouse      field.String
	OrderStatus         field.Int32
	InvoiceNumber       field.String
	InvoicePdf          field.String
	InvoiceURL          field.String
	OpenAmt             field.Int32
	LpAmt               field.Int32
	CashbackAmt         field.Int32
	Info                field.String
	Comment             field.String
	ItemPrice           field.Int32
	BuyerInfo           field.String
	ShopInfo            field.String
	ShipmentFulfillment field.String
	Preorder            field.String
	OrderInfo           field.String
	OriginInfo          field.String
	PaymentInfo         field.String
	InsuranceInfo       field.String
	CreateTime          field.String
	ShippingDate        field.String
	UpdateTime          field.String
	PaymentDate         field.String
	DeliveredDate       field.String
	EstShippingDate     field.String
	EstDeliveryDate     field.String
	PromoOrderDetail    field.String
	PofInfo             field.String

	fieldMap map[string]field.Expr
}

func (t thirdTokopediaOrder) Table(newTableName string) *thirdTokopediaOrder {
	t.thirdTokopediaOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdTokopediaOrder) As(alias string) *thirdTokopediaOrder {
	t.thirdTokopediaOrderDo.DO = *(t.thirdTokopediaOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdTokopediaOrder) updateTableName(table string) *thirdTokopediaOrder {
	t.ALL = field.NewAsterisk(table)
	t.OrderID = field.NewInt64(table, "order_id")
	t.BuyerID = field.NewInt64(table, "buyer_id")
	t.SellerID = field.NewInt64(table, "seller_id")
	t.PaymentID = field.NewInt64(table, "payment_id")
	t.IsAffiliate = field.NewBool(table, "is_affiliate")
	t.IsFulfillment = field.NewBool(table, "is_fulfillment")
	t.OrderWarehouse = field.NewString(table, "order_warehouse")
	t.OrderStatus = field.NewInt32(table, "order_status")
	t.InvoiceNumber = field.NewString(table, "invoice_number")
	t.InvoicePdf = field.NewString(table, "invoice_pdf")
	t.InvoiceURL = field.NewString(table, "invoice_url")
	t.OpenAmt = field.NewInt32(table, "open_amt")
	t.LpAmt = field.NewInt32(table, "lp_amt")
	t.CashbackAmt = field.NewInt32(table, "cashback_amt")
	t.Info = field.NewString(table, "info")
	t.Comment = field.NewString(table, "comment")
	t.ItemPrice = field.NewInt32(table, "item_price")
	t.BuyerInfo = field.NewString(table, "buyer_info")
	t.ShopInfo = field.NewString(table, "shop_info")
	t.ShipmentFulfillment = field.NewString(table, "shipment_fulfillment")
	t.Preorder = field.NewString(table, "preorder")
	t.OrderInfo = field.NewString(table, "order_info")
	t.OriginInfo = field.NewString(table, "origin_info")
	t.PaymentInfo = field.NewString(table, "payment_info")
	t.InsuranceInfo = field.NewString(table, "insurance_info")
	t.CreateTime = field.NewString(table, "create_time")
	t.ShippingDate = field.NewString(table, "shipping_date")
	t.UpdateTime = field.NewString(table, "update_time")
	t.PaymentDate = field.NewString(table, "payment_date")
	t.DeliveredDate = field.NewString(table, "delivered_date")
	t.EstShippingDate = field.NewString(table, "est_shipping_date")
	t.EstDeliveryDate = field.NewString(table, "est_delivery_date")
	t.PromoOrderDetail = field.NewString(table, "promo_order_detail")
	t.PofInfo = field.NewString(table, "pof_info")

	t.fillFieldMap()

	return t
}

func (t *thirdTokopediaOrder) WithContext(ctx context.Context) IThirdTokopediaOrderDo {
	return t.thirdTokopediaOrderDo.WithContext(ctx)
}

func (t thirdTokopediaOrder) TableName() string { return t.thirdTokopediaOrderDo.TableName() }

func (t thirdTokopediaOrder) Alias() string { return t.thirdTokopediaOrderDo.Alias() }

func (t thirdTokopediaOrder) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdTokopediaOrderDo.Columns(cols...)
}

func (t *thirdTokopediaOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdTokopediaOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 34)
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["buyer_id"] = t.BuyerID
	t.fieldMap["seller_id"] = t.SellerID
	t.fieldMap["payment_id"] = t.PaymentID
	t.fieldMap["is_affiliate"] = t.IsAffiliate
	t.fieldMap["is_fulfillment"] = t.IsFulfillment
	t.fieldMap["order_warehouse"] = t.OrderWarehouse
	t.fieldMap["order_status"] = t.OrderStatus
	t.fieldMap["invoice_number"] = t.InvoiceNumber
	t.fieldMap["invoice_pdf"] = t.InvoicePdf
	t.fieldMap["invoice_url"] = t.InvoiceURL
	t.fieldMap["open_amt"] = t.OpenAmt
	t.fieldMap["lp_amt"] = t.LpAmt
	t.fieldMap["cashback_amt"] = t.CashbackAmt
	t.fieldMap["info"] = t.Info
	t.fieldMap["comment"] = t.Comment
	t.fieldMap["item_price"] = t.ItemPrice
	t.fieldMap["buyer_info"] = t.BuyerInfo
	t.fieldMap["shop_info"] = t.ShopInfo
	t.fieldMap["shipment_fulfillment"] = t.ShipmentFulfillment
	t.fieldMap["preorder"] = t.Preorder
	t.fieldMap["order_info"] = t.OrderInfo
	t.fieldMap["origin_info"] = t.OriginInfo
	t.fieldMap["payment_info"] = t.PaymentInfo
	t.fieldMap["insurance_info"] = t.InsuranceInfo
	t.fieldMap["create_time"] = t.CreateTime
	t.fieldMap["shipping_date"] = t.ShippingDate
	t.fieldMap["update_time"] = t.UpdateTime
	t.fieldMap["payment_date"] = t.PaymentDate
	t.fieldMap["delivered_date"] = t.DeliveredDate
	t.fieldMap["est_shipping_date"] = t.EstShippingDate
	t.fieldMap["est_delivery_date"] = t.EstDeliveryDate
	t.fieldMap["promo_order_detail"] = t.PromoOrderDetail
	t.fieldMap["pof_info"] = t.PofInfo
}

func (t thirdTokopediaOrder) clone(db *gorm.DB) thirdTokopediaOrder {
	t.thirdTokopediaOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdTokopediaOrder) replaceDB(db *gorm.DB) thirdTokopediaOrder {
	t.thirdTokopediaOrderDo.ReplaceDB(db)
	return t
}

type thirdTokopediaOrderDo struct{ gen.DO }

type IThirdTokopediaOrderDo interface {
	gen.SubQuery
	Debug() IThirdTokopediaOrderDo
	WithContext(ctx context.Context) IThirdTokopediaOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdTokopediaOrderDo
	WriteDB() IThirdTokopediaOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdTokopediaOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdTokopediaOrderDo
	Not(conds ...gen.Condition) IThirdTokopediaOrderDo
	Or(conds ...gen.Condition) IThirdTokopediaOrderDo
	Select(conds ...field.Expr) IThirdTokopediaOrderDo
	Where(conds ...gen.Condition) IThirdTokopediaOrderDo
	Order(conds ...field.Expr) IThirdTokopediaOrderDo
	Distinct(cols ...field.Expr) IThirdTokopediaOrderDo
	Omit(cols ...field.Expr) IThirdTokopediaOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo
	Group(cols ...field.Expr) IThirdTokopediaOrderDo
	Having(conds ...gen.Condition) IThirdTokopediaOrderDo
	Limit(limit int) IThirdTokopediaOrderDo
	Offset(offset int) IThirdTokopediaOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdTokopediaOrderDo
	Unscoped() IThirdTokopediaOrderDo
	Create(values ...*model.ThirdTokopediaOrder) error
	CreateInBatches(values model.ThirdTokopediaOrderSlice, batchSize int) error
	Save(values ...*model.ThirdTokopediaOrder) error
	First() (*model.ThirdTokopediaOrder, error)
	Take() (*model.ThirdTokopediaOrder, error)
	Last() (*model.ThirdTokopediaOrder, error)
	Find() (model.ThirdTokopediaOrderSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdTokopediaOrderSlice, err error)
	FindInBatches(result *model.ThirdTokopediaOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdTokopediaOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdTokopediaOrderDo
	Assign(attrs ...field.AssignExpr) IThirdTokopediaOrderDo
	Joins(fields ...field.RelationField) IThirdTokopediaOrderDo
	Preload(fields ...field.RelationField) IThirdTokopediaOrderDo
	FirstOrInit() (*model.ThirdTokopediaOrder, error)
	FirstOrCreate() (*model.ThirdTokopediaOrder, error)
	FindByPage(offset int, limit int) (result model.ThirdTokopediaOrderSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdTokopediaOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdTokopediaOrderDo) Debug() IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdTokopediaOrderDo) WithContext(ctx context.Context) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdTokopediaOrderDo) ReadDB() IThirdTokopediaOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdTokopediaOrderDo) WriteDB() IThirdTokopediaOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdTokopediaOrderDo) Session(config *gorm.Session) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdTokopediaOrderDo) Clauses(conds ...clause.Expression) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdTokopediaOrderDo) Returning(value interface{}, columns ...string) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdTokopediaOrderDo) Not(conds ...gen.Condition) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdTokopediaOrderDo) Or(conds ...gen.Condition) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdTokopediaOrderDo) Select(conds ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdTokopediaOrderDo) Where(conds ...gen.Condition) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdTokopediaOrderDo) Order(conds ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdTokopediaOrderDo) Distinct(cols ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdTokopediaOrderDo) Omit(cols ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdTokopediaOrderDo) Join(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdTokopediaOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdTokopediaOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdTokopediaOrderDo) Group(cols ...field.Expr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdTokopediaOrderDo) Having(conds ...gen.Condition) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdTokopediaOrderDo) Limit(limit int) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdTokopediaOrderDo) Offset(offset int) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdTokopediaOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdTokopediaOrderDo) Unscoped() IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdTokopediaOrderDo) Create(values ...*model.ThirdTokopediaOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdTokopediaOrderDo) CreateInBatches(values model.ThirdTokopediaOrderSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdTokopediaOrderDo) Save(values ...*model.ThirdTokopediaOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdTokopediaOrderDo) First() (*model.ThirdTokopediaOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTokopediaOrder), nil
	}
}

func (t thirdTokopediaOrderDo) Take() (*model.ThirdTokopediaOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTokopediaOrder), nil
	}
}

func (t thirdTokopediaOrderDo) Last() (*model.ThirdTokopediaOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTokopediaOrder), nil
	}
}

func (t thirdTokopediaOrderDo) Find() (model.ThirdTokopediaOrderSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdTokopediaOrderSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdTokopediaOrder); ok {
		return model.ThirdTokopediaOrderSlice(slice), err
	}
	return model.ThirdTokopediaOrderSlice{}, err

}

func (t thirdTokopediaOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdTokopediaOrderSlice, err error) {
	buf := make([]*model.ThirdTokopediaOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdTokopediaOrderDo) FindInBatches(result *model.ThirdTokopediaOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdTokopediaOrderDo) Attrs(attrs ...field.AssignExpr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdTokopediaOrderDo) Assign(attrs ...field.AssignExpr) IThirdTokopediaOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdTokopediaOrderDo) Joins(fields ...field.RelationField) IThirdTokopediaOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdTokopediaOrderDo) Preload(fields ...field.RelationField) IThirdTokopediaOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdTokopediaOrderDo) FirstOrInit() (*model.ThirdTokopediaOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTokopediaOrder), nil
	}
}

func (t thirdTokopediaOrderDo) FirstOrCreate() (*model.ThirdTokopediaOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTokopediaOrder), nil
	}
}

func (t thirdTokopediaOrderDo) FindByPage(offset int, limit int) (result model.ThirdTokopediaOrderSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdTokopediaOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdTokopediaOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdTokopediaOrderDo) Delete(models ...*model.ThirdTokopediaOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdTokopediaOrderDo) withDO(do gen.Dao) *thirdTokopediaOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
