// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdTiktokOrder(db *gorm.DB, opts ...gen.DOOption) thirdTiktokOrder {
	_thirdTiktokOrder := thirdTiktokOrder{}

	_thirdTiktokOrder.thirdTiktokOrderDo.UseDB(db, opts...)
	_thirdTiktokOrder.thirdTiktokOrderDo.UseModel(&model.ThirdTiktokOrder{})

	tableName := _thirdTiktokOrder.thirdTiktokOrderDo.TableName()
	_thirdTiktokOrder.ALL = field.NewAsterisk(tableName)
	_thirdTiktokOrder.ID = field.NewString(tableName, "id")
	_thirdTiktokOrder.Status = field.NewString(tableName, "status")
	_thirdTiktokOrder.ShippingProvider = field.NewString(tableName, "shipping_provider")
	_thirdTiktokOrder.ShippingProviderID = field.NewString(tableName, "shipping_provider_id")
	_thirdTiktokOrder.CreateTime = field.NewInt32(tableName, "create_time")
	_thirdTiktokOrder.PaidTime = field.NewInt32(tableName, "paid_time")
	_thirdTiktokOrder.BuyerMessage = field.NewString(tableName, "buyer_message")
	_thirdTiktokOrder.CancelReason = field.NewString(tableName, "cancel_reason")
	_thirdTiktokOrder.CancellationInitiator = field.NewString(tableName, "cancellation_initiator")
	_thirdTiktokOrder.TrackingNumber = field.NewString(tableName, "tracking_number")
	_thirdTiktokOrder.RtsTime = field.NewInt32(tableName, "rts_time")
	_thirdTiktokOrder.RtsSLATime = field.NewInt32(tableName, "rts_sla_time")
	_thirdTiktokOrder.TtsSLATime = field.NewInt32(tableName, "tts_sla_time")
	_thirdTiktokOrder.CancelOrderSLATime = field.NewInt32(tableName, "cancel_order_sla_time")
	_thirdTiktokOrder.UpdateTime = field.NewInt32(tableName, "update_time")
	_thirdTiktokOrder.HasUpdatedRecipientAddress = field.NewBool(tableName, "has_updated_recipient_address")
	_thirdTiktokOrder.IsCod = field.NewBool(tableName, "is_cod")
	_thirdTiktokOrder.IsSampleOrder = field.NewBool(tableName, "is_sample_order")
	_thirdTiktokOrder.NeedUploadInvoice = field.NewString(tableName, "need_upload_invoice")
	_thirdTiktokOrder.BuyerEmail = field.NewString(tableName, "buyer_email")
	_thirdTiktokOrder.Cpf = field.NewString(tableName, "cpf")
	_thirdTiktokOrder.IsOnHoldOrder = field.NewBool(tableName, "is_on_hold_order")
	_thirdTiktokOrder.IsBuyerRequestCancel = field.NewBool(tableName, "is_buyer_request_cancel")
	_thirdTiktokOrder.RequestCancelTime = field.NewInt32(tableName, "request_cancel_time")
	_thirdTiktokOrder.DeliveryOptionRequiredDeliveryTime = field.NewInt32(tableName, "delivery_option_required_delivery_time")
	_thirdTiktokOrder.ShippingDueTime = field.NewInt32(tableName, "shipping_due_time")
	_thirdTiktokOrder.CollectionDueTime = field.NewInt32(tableName, "collection_due_time")
	_thirdTiktokOrder.DeliveryDueTime = field.NewInt32(tableName, "delivery_due_time")
	_thirdTiktokOrder.CollectionTime = field.NewInt32(tableName, "collection_time")
	_thirdTiktokOrder.DeliveryTime = field.NewInt32(tableName, "delivery_time")
	_thirdTiktokOrder.CancelTime = field.NewInt32(tableName, "cancel_time")
	_thirdTiktokOrder.UserID = field.NewString(tableName, "user_id")
	_thirdTiktokOrder.SplitOrCombineTag = field.NewString(tableName, "split_or_combine_tag")
	_thirdTiktokOrder.FulfillmentType = field.NewString(tableName, "fulfillment_type")
	_thirdTiktokOrder.SellerNote = field.NewString(tableName, "seller_note")
	_thirdTiktokOrder.WarehouseID = field.NewString(tableName, "warehouse_id")
	_thirdTiktokOrder.PaymentMethodName = field.NewString(tableName, "payment_method_name")
	_thirdTiktokOrder.ShippingType = field.NewString(tableName, "shipping_type")
	_thirdTiktokOrder.DeliveryOptionName = field.NewString(tableName, "delivery_option_name")
	_thirdTiktokOrder.DeliveryOptionID = field.NewString(tableName, "delivery_option_id")
	_thirdTiktokOrder.DeliverySLATime = field.NewInt32(tableName, "delivery_sla_time")
	_thirdTiktokOrder.LineItems = field.NewString(tableName, "line_items")
	_thirdTiktokOrder.Packages = field.NewString(tableName, "packages")
	_thirdTiktokOrder.Payment = field.NewString(tableName, "payment")
	_thirdTiktokOrder.RecipientAddress = field.NewString(tableName, "recipient_address")

	_thirdTiktokOrder.fillFieldMap()

	return _thirdTiktokOrder
}

type thirdTiktokOrder struct {
	thirdTiktokOrderDo thirdTiktokOrderDo

	ALL                                field.Asterisk
	ID                                 field.String
	Status                             field.String
	ShippingProvider                   field.String
	ShippingProviderID                 field.String
	CreateTime                         field.Int32
	PaidTime                           field.Int32
	BuyerMessage                       field.String
	CancelReason                       field.String
	CancellationInitiator              field.String
	TrackingNumber                     field.String
	RtsTime                            field.Int32
	RtsSLATime                         field.Int32
	TtsSLATime                         field.Int32
	CancelOrderSLATime                 field.Int32
	UpdateTime                         field.Int32
	HasUpdatedRecipientAddress         field.Bool
	IsCod                              field.Bool
	IsSampleOrder                      field.Bool
	NeedUploadInvoice                  field.String
	BuyerEmail                         field.String
	Cpf                                field.String
	IsOnHoldOrder                      field.Bool
	IsBuyerRequestCancel               field.Bool
	RequestCancelTime                  field.Int32
	DeliveryOptionRequiredDeliveryTime field.Int32
	ShippingDueTime                    field.Int32
	CollectionDueTime                  field.Int32
	DeliveryDueTime                    field.Int32
	CollectionTime                     field.Int32
	DeliveryTime                       field.Int32
	CancelTime                         field.Int32
	UserID                             field.String
	SplitOrCombineTag                  field.String
	FulfillmentType                    field.String
	SellerNote                         field.String
	WarehouseID                        field.String
	PaymentMethodName                  field.String
	ShippingType                       field.String
	DeliveryOptionName                 field.String
	DeliveryOptionID                   field.String
	DeliverySLATime                    field.Int32
	LineItems                          field.String
	Packages                           field.String
	Payment                            field.String
	RecipientAddress                   field.String

	fieldMap map[string]field.Expr
}

func (t thirdTiktokOrder) Table(newTableName string) *thirdTiktokOrder {
	t.thirdTiktokOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdTiktokOrder) As(alias string) *thirdTiktokOrder {
	t.thirdTiktokOrderDo.DO = *(t.thirdTiktokOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdTiktokOrder) updateTableName(table string) *thirdTiktokOrder {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.Status = field.NewString(table, "status")
	t.ShippingProvider = field.NewString(table, "shipping_provider")
	t.ShippingProviderID = field.NewString(table, "shipping_provider_id")
	t.CreateTime = field.NewInt32(table, "create_time")
	t.PaidTime = field.NewInt32(table, "paid_time")
	t.BuyerMessage = field.NewString(table, "buyer_message")
	t.CancelReason = field.NewString(table, "cancel_reason")
	t.CancellationInitiator = field.NewString(table, "cancellation_initiator")
	t.TrackingNumber = field.NewString(table, "tracking_number")
	t.RtsTime = field.NewInt32(table, "rts_time")
	t.RtsSLATime = field.NewInt32(table, "rts_sla_time")
	t.TtsSLATime = field.NewInt32(table, "tts_sla_time")
	t.CancelOrderSLATime = field.NewInt32(table, "cancel_order_sla_time")
	t.UpdateTime = field.NewInt32(table, "update_time")
	t.HasUpdatedRecipientAddress = field.NewBool(table, "has_updated_recipient_address")
	t.IsCod = field.NewBool(table, "is_cod")
	t.IsSampleOrder = field.NewBool(table, "is_sample_order")
	t.NeedUploadInvoice = field.NewString(table, "need_upload_invoice")
	t.BuyerEmail = field.NewString(table, "buyer_email")
	t.Cpf = field.NewString(table, "cpf")
	t.IsOnHoldOrder = field.NewBool(table, "is_on_hold_order")
	t.IsBuyerRequestCancel = field.NewBool(table, "is_buyer_request_cancel")
	t.RequestCancelTime = field.NewInt32(table, "request_cancel_time")
	t.DeliveryOptionRequiredDeliveryTime = field.NewInt32(table, "delivery_option_required_delivery_time")
	t.ShippingDueTime = field.NewInt32(table, "shipping_due_time")
	t.CollectionDueTime = field.NewInt32(table, "collection_due_time")
	t.DeliveryDueTime = field.NewInt32(table, "delivery_due_time")
	t.CollectionTime = field.NewInt32(table, "collection_time")
	t.DeliveryTime = field.NewInt32(table, "delivery_time")
	t.CancelTime = field.NewInt32(table, "cancel_time")
	t.UserID = field.NewString(table, "user_id")
	t.SplitOrCombineTag = field.NewString(table, "split_or_combine_tag")
	t.FulfillmentType = field.NewString(table, "fulfillment_type")
	t.SellerNote = field.NewString(table, "seller_note")
	t.WarehouseID = field.NewString(table, "warehouse_id")
	t.PaymentMethodName = field.NewString(table, "payment_method_name")
	t.ShippingType = field.NewString(table, "shipping_type")
	t.DeliveryOptionName = field.NewString(table, "delivery_option_name")
	t.DeliveryOptionID = field.NewString(table, "delivery_option_id")
	t.DeliverySLATime = field.NewInt32(table, "delivery_sla_time")
	t.LineItems = field.NewString(table, "line_items")
	t.Packages = field.NewString(table, "packages")
	t.Payment = field.NewString(table, "payment")
	t.RecipientAddress = field.NewString(table, "recipient_address")

	t.fillFieldMap()

	return t
}

func (t *thirdTiktokOrder) WithContext(ctx context.Context) IThirdTiktokOrderDo {
	return t.thirdTiktokOrderDo.WithContext(ctx)
}

func (t thirdTiktokOrder) TableName() string { return t.thirdTiktokOrderDo.TableName() }

func (t thirdTiktokOrder) Alias() string { return t.thirdTiktokOrderDo.Alias() }

func (t thirdTiktokOrder) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdTiktokOrderDo.Columns(cols...)
}

func (t *thirdTiktokOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdTiktokOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 45)
	t.fieldMap["id"] = t.ID
	t.fieldMap["status"] = t.Status
	t.fieldMap["shipping_provider"] = t.ShippingProvider
	t.fieldMap["shipping_provider_id"] = t.ShippingProviderID
	t.fieldMap["create_time"] = t.CreateTime
	t.fieldMap["paid_time"] = t.PaidTime
	t.fieldMap["buyer_message"] = t.BuyerMessage
	t.fieldMap["cancel_reason"] = t.CancelReason
	t.fieldMap["cancellation_initiator"] = t.CancellationInitiator
	t.fieldMap["tracking_number"] = t.TrackingNumber
	t.fieldMap["rts_time"] = t.RtsTime
	t.fieldMap["rts_sla_time"] = t.RtsSLATime
	t.fieldMap["tts_sla_time"] = t.TtsSLATime
	t.fieldMap["cancel_order_sla_time"] = t.CancelOrderSLATime
	t.fieldMap["update_time"] = t.UpdateTime
	t.fieldMap["has_updated_recipient_address"] = t.HasUpdatedRecipientAddress
	t.fieldMap["is_cod"] = t.IsCod
	t.fieldMap["is_sample_order"] = t.IsSampleOrder
	t.fieldMap["need_upload_invoice"] = t.NeedUploadInvoice
	t.fieldMap["buyer_email"] = t.BuyerEmail
	t.fieldMap["cpf"] = t.Cpf
	t.fieldMap["is_on_hold_order"] = t.IsOnHoldOrder
	t.fieldMap["is_buyer_request_cancel"] = t.IsBuyerRequestCancel
	t.fieldMap["request_cancel_time"] = t.RequestCancelTime
	t.fieldMap["delivery_option_required_delivery_time"] = t.DeliveryOptionRequiredDeliveryTime
	t.fieldMap["shipping_due_time"] = t.ShippingDueTime
	t.fieldMap["collection_due_time"] = t.CollectionDueTime
	t.fieldMap["delivery_due_time"] = t.DeliveryDueTime
	t.fieldMap["collection_time"] = t.CollectionTime
	t.fieldMap["delivery_time"] = t.DeliveryTime
	t.fieldMap["cancel_time"] = t.CancelTime
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["split_or_combine_tag"] = t.SplitOrCombineTag
	t.fieldMap["fulfillment_type"] = t.FulfillmentType
	t.fieldMap["seller_note"] = t.SellerNote
	t.fieldMap["warehouse_id"] = t.WarehouseID
	t.fieldMap["payment_method_name"] = t.PaymentMethodName
	t.fieldMap["shipping_type"] = t.ShippingType
	t.fieldMap["delivery_option_name"] = t.DeliveryOptionName
	t.fieldMap["delivery_option_id"] = t.DeliveryOptionID
	t.fieldMap["delivery_sla_time"] = t.DeliverySLATime
	t.fieldMap["line_items"] = t.LineItems
	t.fieldMap["packages"] = t.Packages
	t.fieldMap["payment"] = t.Payment
	t.fieldMap["recipient_address"] = t.RecipientAddress
}

func (t thirdTiktokOrder) clone(db *gorm.DB) thirdTiktokOrder {
	t.thirdTiktokOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdTiktokOrder) replaceDB(db *gorm.DB) thirdTiktokOrder {
	t.thirdTiktokOrderDo.ReplaceDB(db)
	return t
}

type thirdTiktokOrderDo struct{ gen.DO }

type IThirdTiktokOrderDo interface {
	gen.SubQuery
	Debug() IThirdTiktokOrderDo
	WithContext(ctx context.Context) IThirdTiktokOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdTiktokOrderDo
	WriteDB() IThirdTiktokOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdTiktokOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdTiktokOrderDo
	Not(conds ...gen.Condition) IThirdTiktokOrderDo
	Or(conds ...gen.Condition) IThirdTiktokOrderDo
	Select(conds ...field.Expr) IThirdTiktokOrderDo
	Where(conds ...gen.Condition) IThirdTiktokOrderDo
	Order(conds ...field.Expr) IThirdTiktokOrderDo
	Distinct(cols ...field.Expr) IThirdTiktokOrderDo
	Omit(cols ...field.Expr) IThirdTiktokOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo
	Group(cols ...field.Expr) IThirdTiktokOrderDo
	Having(conds ...gen.Condition) IThirdTiktokOrderDo
	Limit(limit int) IThirdTiktokOrderDo
	Offset(offset int) IThirdTiktokOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdTiktokOrderDo
	Unscoped() IThirdTiktokOrderDo
	Create(values ...*model.ThirdTiktokOrder) error
	CreateInBatches(values model.ThirdTiktokOrderSlice, batchSize int) error
	Save(values ...*model.ThirdTiktokOrder) error
	First() (*model.ThirdTiktokOrder, error)
	Take() (*model.ThirdTiktokOrder, error)
	Last() (*model.ThirdTiktokOrder, error)
	Find() (model.ThirdTiktokOrderSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdTiktokOrderSlice, err error)
	FindInBatches(result *model.ThirdTiktokOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdTiktokOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdTiktokOrderDo
	Assign(attrs ...field.AssignExpr) IThirdTiktokOrderDo
	Joins(fields ...field.RelationField) IThirdTiktokOrderDo
	Preload(fields ...field.RelationField) IThirdTiktokOrderDo
	FirstOrInit() (*model.ThirdTiktokOrder, error)
	FirstOrCreate() (*model.ThirdTiktokOrder, error)
	FindByPage(offset int, limit int) (result model.ThirdTiktokOrderSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdTiktokOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdTiktokOrderDo) Debug() IThirdTiktokOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdTiktokOrderDo) WithContext(ctx context.Context) IThirdTiktokOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdTiktokOrderDo) ReadDB() IThirdTiktokOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdTiktokOrderDo) WriteDB() IThirdTiktokOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdTiktokOrderDo) Session(config *gorm.Session) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdTiktokOrderDo) Clauses(conds ...clause.Expression) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdTiktokOrderDo) Returning(value interface{}, columns ...string) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdTiktokOrderDo) Not(conds ...gen.Condition) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdTiktokOrderDo) Or(conds ...gen.Condition) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdTiktokOrderDo) Select(conds ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdTiktokOrderDo) Where(conds ...gen.Condition) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdTiktokOrderDo) Order(conds ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdTiktokOrderDo) Distinct(cols ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdTiktokOrderDo) Omit(cols ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdTiktokOrderDo) Join(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdTiktokOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdTiktokOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdTiktokOrderDo) Group(cols ...field.Expr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdTiktokOrderDo) Having(conds ...gen.Condition) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdTiktokOrderDo) Limit(limit int) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdTiktokOrderDo) Offset(offset int) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdTiktokOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdTiktokOrderDo) Unscoped() IThirdTiktokOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdTiktokOrderDo) Create(values ...*model.ThirdTiktokOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdTiktokOrderDo) CreateInBatches(values model.ThirdTiktokOrderSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdTiktokOrderDo) Save(values ...*model.ThirdTiktokOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdTiktokOrderDo) First() (*model.ThirdTiktokOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTiktokOrder), nil
	}
}

func (t thirdTiktokOrderDo) Take() (*model.ThirdTiktokOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTiktokOrder), nil
	}
}

func (t thirdTiktokOrderDo) Last() (*model.ThirdTiktokOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTiktokOrder), nil
	}
}

func (t thirdTiktokOrderDo) Find() (model.ThirdTiktokOrderSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdTiktokOrderSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdTiktokOrder); ok {
		return model.ThirdTiktokOrderSlice(slice), err
	}
	return model.ThirdTiktokOrderSlice{}, err

}

func (t thirdTiktokOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdTiktokOrderSlice, err error) {
	buf := make([]*model.ThirdTiktokOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdTiktokOrderDo) FindInBatches(result *model.ThirdTiktokOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdTiktokOrderDo) Attrs(attrs ...field.AssignExpr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdTiktokOrderDo) Assign(attrs ...field.AssignExpr) IThirdTiktokOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdTiktokOrderDo) Joins(fields ...field.RelationField) IThirdTiktokOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdTiktokOrderDo) Preload(fields ...field.RelationField) IThirdTiktokOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdTiktokOrderDo) FirstOrInit() (*model.ThirdTiktokOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTiktokOrder), nil
	}
}

func (t thirdTiktokOrderDo) FirstOrCreate() (*model.ThirdTiktokOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdTiktokOrder), nil
	}
}

func (t thirdTiktokOrderDo) FindByPage(offset int, limit int) (result model.ThirdTiktokOrderSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdTiktokOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdTiktokOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdTiktokOrderDo) Delete(models ...*model.ThirdTiktokOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdTiktokOrderDo) withDO(do gen.Dao) *thirdTiktokOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
