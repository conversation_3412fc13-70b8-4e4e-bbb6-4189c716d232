// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newTestOrderBase(db *gorm.DB, opts ...gen.DOOption) testOrderBase {
	_testOrderBase := testOrderBase{}

	_testOrderBase.testOrderBaseDo.UseDB(db, opts...)
	_testOrderBase.testOrderBaseDo.UseModel(&model.TestOrderBase{})

	tableName := _testOrderBase.testOrderBaseDo.TableName()
	_testOrderBase.ALL = field.NewAsterisk(tableName)
	_testOrderBase.ID = field.NewString(tableName, "id")
	_testOrderBase.ChannelOrderID = field.NewString(tableName, "channel_order_id")
	_testOrderBase.State = field.NewString(tableName, "state")
	_testOrderBase.DisplayState = field.NewString(tableName, "display_state")
	_testOrderBase.StorePlatform = field.NewString(tableName, "store_platform")
	_testOrderBase.ShopID = field.NewInt32(tableName, "shop_id")
	_testOrderBase.StoreState = field.NewString(tableName, "store_state")
	_testOrderBase.WarehouseID = field.NewString(tableName, "warehouse_id")
	_testOrderBase.ExpressID = field.NewString(tableName, "express_id")
	_testOrderBase.WarehouseState = field.NewString(tableName, "warehouse_state")
	_testOrderBase.OrderTime = field.NewTime(tableName, "order_time")
	_testOrderBase.PayTime = field.NewTime(tableName, "pay_time")
	_testOrderBase.PayWay = field.NewString(tableName, "pay_way")
	_testOrderBase.OutOfStockReason = field.NewString(tableName, "out_of_stock_reason")
	_testOrderBase.PreOrder = field.NewBool(tableName, "pre_order")
	_testOrderBase.DeliverFirst = field.NewBool(tableName, "deliver_first")
	_testOrderBase.Remark = field.NewBool(tableName, "remark")
	_testOrderBase.Hold = field.NewBool(tableName, "hold")
	_testOrderBase.Exception = field.NewBool(tableName, "exception")
	_testOrderBase.Oversold = field.NewBool(tableName, "oversold")
	_testOrderBase.Test008 = field.NewInt32(tableName, "test008")
	_testOrderBase.UpdateTime = field.NewTime(tableName, "update_time")

	_testOrderBase.fillFieldMap()

	return _testOrderBase
}

type testOrderBase struct {
	testOrderBaseDo testOrderBaseDo

	ALL              field.Asterisk
	ID               field.String
	ChannelOrderID   field.String
	State            field.String
	DisplayState     field.String
	StorePlatform    field.String
	ShopID           field.Int32
	StoreState       field.String
	WarehouseID      field.String
	ExpressID        field.String
	WarehouseState   field.String
	OrderTime        field.Time
	PayTime          field.Time
	PayWay           field.String
	OutOfStockReason field.String
	PreOrder         field.Bool
	DeliverFirst     field.Bool
	Remark           field.Bool
	Hold             field.Bool
	Exception        field.Bool
	Oversold         field.Bool
	Test008          field.Int32
	UpdateTime       field.Time

	fieldMap map[string]field.Expr
}

func (t testOrderBase) Table(newTableName string) *testOrderBase {
	t.testOrderBaseDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t testOrderBase) As(alias string) *testOrderBase {
	t.testOrderBaseDo.DO = *(t.testOrderBaseDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *testOrderBase) updateTableName(table string) *testOrderBase {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewString(table, "id")
	t.ChannelOrderID = field.NewString(table, "channel_order_id")
	t.State = field.NewString(table, "state")
	t.DisplayState = field.NewString(table, "display_state")
	t.StorePlatform = field.NewString(table, "store_platform")
	t.ShopID = field.NewInt32(table, "shop_id")
	t.StoreState = field.NewString(table, "store_state")
	t.WarehouseID = field.NewString(table, "warehouse_id")
	t.ExpressID = field.NewString(table, "express_id")
	t.WarehouseState = field.NewString(table, "warehouse_state")
	t.OrderTime = field.NewTime(table, "order_time")
	t.PayTime = field.NewTime(table, "pay_time")
	t.PayWay = field.NewString(table, "pay_way")
	t.OutOfStockReason = field.NewString(table, "out_of_stock_reason")
	t.PreOrder = field.NewBool(table, "pre_order")
	t.DeliverFirst = field.NewBool(table, "deliver_first")
	t.Remark = field.NewBool(table, "remark")
	t.Hold = field.NewBool(table, "hold")
	t.Exception = field.NewBool(table, "exception")
	t.Oversold = field.NewBool(table, "oversold")
	t.Test008 = field.NewInt32(table, "test008")
	t.UpdateTime = field.NewTime(table, "update_time")

	t.fillFieldMap()

	return t
}

func (t *testOrderBase) WithContext(ctx context.Context) ITestOrderBaseDo {
	return t.testOrderBaseDo.WithContext(ctx)
}

func (t testOrderBase) TableName() string { return t.testOrderBaseDo.TableName() }

func (t testOrderBase) Alias() string { return t.testOrderBaseDo.Alias() }

func (t testOrderBase) Columns(cols ...field.Expr) gen.Columns {
	return t.testOrderBaseDo.Columns(cols...)
}

func (t *testOrderBase) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *testOrderBase) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 22)
	t.fieldMap["id"] = t.ID
	t.fieldMap["channel_order_id"] = t.ChannelOrderID
	t.fieldMap["state"] = t.State
	t.fieldMap["display_state"] = t.DisplayState
	t.fieldMap["store_platform"] = t.StorePlatform
	t.fieldMap["shop_id"] = t.ShopID
	t.fieldMap["store_state"] = t.StoreState
	t.fieldMap["warehouse_id"] = t.WarehouseID
	t.fieldMap["express_id"] = t.ExpressID
	t.fieldMap["warehouse_state"] = t.WarehouseState
	t.fieldMap["order_time"] = t.OrderTime
	t.fieldMap["pay_time"] = t.PayTime
	t.fieldMap["pay_way"] = t.PayWay
	t.fieldMap["out_of_stock_reason"] = t.OutOfStockReason
	t.fieldMap["pre_order"] = t.PreOrder
	t.fieldMap["deliver_first"] = t.DeliverFirst
	t.fieldMap["remark"] = t.Remark
	t.fieldMap["hold"] = t.Hold
	t.fieldMap["exception"] = t.Exception
	t.fieldMap["oversold"] = t.Oversold
	t.fieldMap["test008"] = t.Test008
	t.fieldMap["update_time"] = t.UpdateTime
}

func (t testOrderBase) clone(db *gorm.DB) testOrderBase {
	t.testOrderBaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t testOrderBase) replaceDB(db *gorm.DB) testOrderBase {
	t.testOrderBaseDo.ReplaceDB(db)
	return t
}

type testOrderBaseDo struct{ gen.DO }

type ITestOrderBaseDo interface {
	gen.SubQuery
	Debug() ITestOrderBaseDo
	WithContext(ctx context.Context) ITestOrderBaseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITestOrderBaseDo
	WriteDB() ITestOrderBaseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITestOrderBaseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITestOrderBaseDo
	Not(conds ...gen.Condition) ITestOrderBaseDo
	Or(conds ...gen.Condition) ITestOrderBaseDo
	Select(conds ...field.Expr) ITestOrderBaseDo
	Where(conds ...gen.Condition) ITestOrderBaseDo
	Order(conds ...field.Expr) ITestOrderBaseDo
	Distinct(cols ...field.Expr) ITestOrderBaseDo
	Omit(cols ...field.Expr) ITestOrderBaseDo
	Join(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo
	Group(cols ...field.Expr) ITestOrderBaseDo
	Having(conds ...gen.Condition) ITestOrderBaseDo
	Limit(limit int) ITestOrderBaseDo
	Offset(offset int) ITestOrderBaseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITestOrderBaseDo
	Unscoped() ITestOrderBaseDo
	Create(values ...*model.TestOrderBase) error
	CreateInBatches(values model.TestOrderBaseSlice, batchSize int) error
	Save(values ...*model.TestOrderBase) error
	First() (*model.TestOrderBase, error)
	Take() (*model.TestOrderBase, error)
	Last() (*model.TestOrderBase, error)
	Find() (model.TestOrderBaseSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.TestOrderBaseSlice, err error)
	FindInBatches(result *model.TestOrderBaseSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TestOrderBase) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITestOrderBaseDo
	Assign(attrs ...field.AssignExpr) ITestOrderBaseDo
	Joins(fields ...field.RelationField) ITestOrderBaseDo
	Preload(fields ...field.RelationField) ITestOrderBaseDo
	FirstOrInit() (*model.TestOrderBase, error)
	FirstOrCreate() (*model.TestOrderBase, error)
	FindByPage(offset int, limit int) (result model.TestOrderBaseSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITestOrderBaseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t testOrderBaseDo) Debug() ITestOrderBaseDo {
	return t.withDO(t.DO.Debug())
}

func (t testOrderBaseDo) WithContext(ctx context.Context) ITestOrderBaseDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t testOrderBaseDo) ReadDB() ITestOrderBaseDo {
	return t.Clauses(dbresolver.Read)
}

func (t testOrderBaseDo) WriteDB() ITestOrderBaseDo {
	return t.Clauses(dbresolver.Write)
}

func (t testOrderBaseDo) Session(config *gorm.Session) ITestOrderBaseDo {
	return t.withDO(t.DO.Session(config))
}

func (t testOrderBaseDo) Clauses(conds ...clause.Expression) ITestOrderBaseDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t testOrderBaseDo) Returning(value interface{}, columns ...string) ITestOrderBaseDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t testOrderBaseDo) Not(conds ...gen.Condition) ITestOrderBaseDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t testOrderBaseDo) Or(conds ...gen.Condition) ITestOrderBaseDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t testOrderBaseDo) Select(conds ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t testOrderBaseDo) Where(conds ...gen.Condition) ITestOrderBaseDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t testOrderBaseDo) Order(conds ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t testOrderBaseDo) Distinct(cols ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t testOrderBaseDo) Omit(cols ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t testOrderBaseDo) Join(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t testOrderBaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t testOrderBaseDo) RightJoin(table schema.Tabler, on ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t testOrderBaseDo) Group(cols ...field.Expr) ITestOrderBaseDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t testOrderBaseDo) Having(conds ...gen.Condition) ITestOrderBaseDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t testOrderBaseDo) Limit(limit int) ITestOrderBaseDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t testOrderBaseDo) Offset(offset int) ITestOrderBaseDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t testOrderBaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITestOrderBaseDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t testOrderBaseDo) Unscoped() ITestOrderBaseDo {
	return t.withDO(t.DO.Unscoped())
}

func (t testOrderBaseDo) Create(values ...*model.TestOrderBase) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t testOrderBaseDo) CreateInBatches(values model.TestOrderBaseSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t testOrderBaseDo) Save(values ...*model.TestOrderBase) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t testOrderBaseDo) First() (*model.TestOrderBase, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TestOrderBase), nil
	}
}

func (t testOrderBaseDo) Take() (*model.TestOrderBase, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TestOrderBase), nil
	}
}

func (t testOrderBaseDo) Last() (*model.TestOrderBase, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TestOrderBase), nil
	}
}

func (t testOrderBaseDo) Find() (model.TestOrderBaseSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.TestOrderBaseSlice{}, err
	}
	if slice, ok := result.([]*model.TestOrderBase); ok {
		return model.TestOrderBaseSlice(slice), err
	}
	return model.TestOrderBaseSlice{}, err

}

func (t testOrderBaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.TestOrderBaseSlice, err error) {
	buf := make([]*model.TestOrderBase, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t testOrderBaseDo) FindInBatches(result *model.TestOrderBaseSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t testOrderBaseDo) Attrs(attrs ...field.AssignExpr) ITestOrderBaseDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t testOrderBaseDo) Assign(attrs ...field.AssignExpr) ITestOrderBaseDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t testOrderBaseDo) Joins(fields ...field.RelationField) ITestOrderBaseDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t testOrderBaseDo) Preload(fields ...field.RelationField) ITestOrderBaseDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t testOrderBaseDo) FirstOrInit() (*model.TestOrderBase, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TestOrderBase), nil
	}
}

func (t testOrderBaseDo) FirstOrCreate() (*model.TestOrderBase, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TestOrderBase), nil
	}
}

func (t testOrderBaseDo) FindByPage(offset int, limit int) (result model.TestOrderBaseSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t testOrderBaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t testOrderBaseDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t testOrderBaseDo) Delete(models ...*model.TestOrderBase) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *testOrderBaseDo) withDO(do gen.Dao) *testOrderBaseDo {
	t.DO = *do.(*gen.DO)
	return t
}
