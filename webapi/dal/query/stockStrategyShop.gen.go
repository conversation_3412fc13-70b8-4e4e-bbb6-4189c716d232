// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockStrategyShop(db *gorm.DB, opts ...gen.DOOption) stockStrategyShop {
	_stockStrategyShop := stockStrategyShop{}

	_stockStrategyShop.stockStrategyShopDo.UseDB(db, opts...)
	_stockStrategyShop.stockStrategyShopDo.UseModel(&model.StockStrategyShop{})

	tableName := _stockStrategyShop.stockStrategyShopDo.TableName()
	_stockStrategyShop.ALL = field.NewAsterisk(tableName)
	_stockStrategyShop.ID = field.NewInt32(tableName, "id")
	_stockStrategyShop.StrategyID = field.NewInt32(tableName, "strategy_id")
	_stockStrategyShop.ShopID = field.NewInt32(tableName, "shop_id")

	_stockStrategyShop.fillFieldMap()

	return _stockStrategyShop
}

type stockStrategyShop struct {
	stockStrategyShopDo stockStrategyShopDo

	ALL        field.Asterisk
	ID         field.Int32
	StrategyID field.Int32
	ShopID     field.Int32

	fieldMap map[string]field.Expr
}

func (s stockStrategyShop) Table(newTableName string) *stockStrategyShop {
	s.stockStrategyShopDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockStrategyShop) As(alias string) *stockStrategyShop {
	s.stockStrategyShopDo.DO = *(s.stockStrategyShopDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockStrategyShop) updateTableName(table string) *stockStrategyShop {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.StrategyID = field.NewInt32(table, "strategy_id")
	s.ShopID = field.NewInt32(table, "shop_id")

	s.fillFieldMap()

	return s
}

func (s *stockStrategyShop) WithContext(ctx context.Context) IStockStrategyShopDo {
	return s.stockStrategyShopDo.WithContext(ctx)
}

func (s stockStrategyShop) TableName() string { return s.stockStrategyShopDo.TableName() }

func (s stockStrategyShop) Alias() string { return s.stockStrategyShopDo.Alias() }

func (s stockStrategyShop) Columns(cols ...field.Expr) gen.Columns {
	return s.stockStrategyShopDo.Columns(cols...)
}

func (s *stockStrategyShop) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockStrategyShop) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 3)
	s.fieldMap["id"] = s.ID
	s.fieldMap["strategy_id"] = s.StrategyID
	s.fieldMap["shop_id"] = s.ShopID
}

func (s stockStrategyShop) clone(db *gorm.DB) stockStrategyShop {
	s.stockStrategyShopDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockStrategyShop) replaceDB(db *gorm.DB) stockStrategyShop {
	s.stockStrategyShopDo.ReplaceDB(db)
	return s
}

type stockStrategyShopDo struct{ gen.DO }

type IStockStrategyShopDo interface {
	gen.SubQuery
	Debug() IStockStrategyShopDo
	WithContext(ctx context.Context) IStockStrategyShopDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockStrategyShopDo
	WriteDB() IStockStrategyShopDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockStrategyShopDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockStrategyShopDo
	Not(conds ...gen.Condition) IStockStrategyShopDo
	Or(conds ...gen.Condition) IStockStrategyShopDo
	Select(conds ...field.Expr) IStockStrategyShopDo
	Where(conds ...gen.Condition) IStockStrategyShopDo
	Order(conds ...field.Expr) IStockStrategyShopDo
	Distinct(cols ...field.Expr) IStockStrategyShopDo
	Omit(cols ...field.Expr) IStockStrategyShopDo
	Join(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo
	Group(cols ...field.Expr) IStockStrategyShopDo
	Having(conds ...gen.Condition) IStockStrategyShopDo
	Limit(limit int) IStockStrategyShopDo
	Offset(offset int) IStockStrategyShopDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyShopDo
	Unscoped() IStockStrategyShopDo
	Create(values ...*model.StockStrategyShop) error
	CreateInBatches(values model.StockStrategyShopSlice, batchSize int) error
	Save(values ...*model.StockStrategyShop) error
	First() (*model.StockStrategyShop, error)
	Take() (*model.StockStrategyShop, error)
	Last() (*model.StockStrategyShop, error)
	Find() (model.StockStrategyShopSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategyShopSlice, err error)
	FindInBatches(result *model.StockStrategyShopSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockStrategyShop) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockStrategyShopDo
	Assign(attrs ...field.AssignExpr) IStockStrategyShopDo
	Joins(fields ...field.RelationField) IStockStrategyShopDo
	Preload(fields ...field.RelationField) IStockStrategyShopDo
	FirstOrInit() (*model.StockStrategyShop, error)
	FirstOrCreate() (*model.StockStrategyShop, error)
	FindByPage(offset int, limit int) (result model.StockStrategyShopSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockStrategyShopDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockStrategyShopDo) Debug() IStockStrategyShopDo {
	return s.withDO(s.DO.Debug())
}

func (s stockStrategyShopDo) WithContext(ctx context.Context) IStockStrategyShopDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockStrategyShopDo) ReadDB() IStockStrategyShopDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockStrategyShopDo) WriteDB() IStockStrategyShopDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockStrategyShopDo) Session(config *gorm.Session) IStockStrategyShopDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockStrategyShopDo) Clauses(conds ...clause.Expression) IStockStrategyShopDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockStrategyShopDo) Returning(value interface{}, columns ...string) IStockStrategyShopDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockStrategyShopDo) Not(conds ...gen.Condition) IStockStrategyShopDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockStrategyShopDo) Or(conds ...gen.Condition) IStockStrategyShopDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockStrategyShopDo) Select(conds ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockStrategyShopDo) Where(conds ...gen.Condition) IStockStrategyShopDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockStrategyShopDo) Order(conds ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockStrategyShopDo) Distinct(cols ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockStrategyShopDo) Omit(cols ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockStrategyShopDo) Join(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockStrategyShopDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockStrategyShopDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockStrategyShopDo) Group(cols ...field.Expr) IStockStrategyShopDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockStrategyShopDo) Having(conds ...gen.Condition) IStockStrategyShopDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockStrategyShopDo) Limit(limit int) IStockStrategyShopDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockStrategyShopDo) Offset(offset int) IStockStrategyShopDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockStrategyShopDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockStrategyShopDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockStrategyShopDo) Unscoped() IStockStrategyShopDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockStrategyShopDo) Create(values ...*model.StockStrategyShop) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockStrategyShopDo) CreateInBatches(values model.StockStrategyShopSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockStrategyShopDo) Save(values ...*model.StockStrategyShop) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockStrategyShopDo) First() (*model.StockStrategyShop, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyShop), nil
	}
}

func (s stockStrategyShopDo) Take() (*model.StockStrategyShop, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyShop), nil
	}
}

func (s stockStrategyShopDo) Last() (*model.StockStrategyShop, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyShop), nil
	}
}

func (s stockStrategyShopDo) Find() (model.StockStrategyShopSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockStrategyShopSlice{}, err
	}
	if slice, ok := result.([]*model.StockStrategyShop); ok {
		return model.StockStrategyShopSlice(slice), err
	}
	return model.StockStrategyShopSlice{}, err

}

func (s stockStrategyShopDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockStrategyShopSlice, err error) {
	buf := make([]*model.StockStrategyShop, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockStrategyShopDo) FindInBatches(result *model.StockStrategyShopSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockStrategyShopDo) Attrs(attrs ...field.AssignExpr) IStockStrategyShopDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockStrategyShopDo) Assign(attrs ...field.AssignExpr) IStockStrategyShopDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockStrategyShopDo) Joins(fields ...field.RelationField) IStockStrategyShopDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockStrategyShopDo) Preload(fields ...field.RelationField) IStockStrategyShopDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockStrategyShopDo) FirstOrInit() (*model.StockStrategyShop, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyShop), nil
	}
}

func (s stockStrategyShopDo) FirstOrCreate() (*model.StockStrategyShop, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockStrategyShop), nil
	}
}

func (s stockStrategyShopDo) FindByPage(offset int, limit int) (result model.StockStrategyShopSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockStrategyShopDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockStrategyShopDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockStrategyShopDo) Delete(models ...*model.StockStrategyShop) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockStrategyShopDo) withDO(do gen.Dao) *stockStrategyShopDo {
	s.DO = *do.(*gen.DO)
	return s
}
