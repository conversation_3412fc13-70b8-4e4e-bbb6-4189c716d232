// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newWarehouse(db *gorm.DB, opts ...gen.DOOption) warehouse {
	_warehouse := warehouse{}

	_warehouse.warehouseDo.UseDB(db, opts...)
	_warehouse.warehouseDo.UseModel(&model.Warehouse{})

	tableName := _warehouse.warehouseDo.TableName()
	_warehouse.ALL = field.NewAsterisk(tableName)
	_warehouse.ID = field.NewInt64(tableName, "id")
	_warehouse.WarehouseCode = field.NewString(tableName, "warehouse_code")
	_warehouse.Name = field.NewString(tableName, "name")
	_warehouse.ThirdCode = field.NewString(tableName, "third_code")
	_warehouse.ActiveFlag = field.NewBool(tableName, "active_flag")
	_warehouse.CreatedAt = field.NewTime(tableName, "created_at")
	_warehouse.CreatedBy = field.NewInt64(tableName, "created_by")
	_warehouse.UpdatedAt = field.NewTime(tableName, "updated_at")
	_warehouse.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_warehouse.FlagDeleted = field.NewInt32(tableName, "flag_deleted")
	_warehouse.DeletedBy = field.NewInt64(tableName, "deleted_by")
	_warehouse.DeletedAt = field.NewField(tableName, "deleted_at")
	_warehouse.DefaultFlag = field.NewInt32(tableName, "default_flag")
	_warehouse.PhysicalWarehouseCode = field.NewString(tableName, "physical_warehouse_code")
	_warehouse.LogicalWarehouseCode = field.NewString(tableName, "logical_warehouse_code")

	_warehouse.fillFieldMap()

	return _warehouse
}

// warehouse 仓库表
type warehouse struct {
	warehouseDo warehouseDo

	ALL                   field.Asterisk
	ID                    field.Int64  // 主键id
	WarehouseCode         field.String // 仓库编码
	Name                  field.String // 仓库名称
	ThirdCode             field.String // 第三方仓库编码
	ActiveFlag            field.Bool   // 是否启用；1-是；0-否
	CreatedAt             field.Time   // 创建时间
	CreatedBy             field.Int64  // 创建人
	UpdatedAt             field.Time   // 更新时间，更新时自动更新为当前时间
	UpdatedBy             field.Int64  // 更新人
	FlagDeleted           field.Int32  // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy             field.Int64  // 删除人
	DeletedAt             field.Field  // 删除时间
	DefaultFlag           field.Int32  // 是否当前地区默认仓库;1-是；0-否
	PhysicalWarehouseCode field.String // 实体仓编码
	LogicalWarehouseCode  field.String // 逻辑仓编码

	fieldMap map[string]field.Expr
}

func (w warehouse) Table(newTableName string) *warehouse {
	w.warehouseDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w warehouse) As(alias string) *warehouse {
	w.warehouseDo.DO = *(w.warehouseDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *warehouse) updateTableName(table string) *warehouse {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.WarehouseCode = field.NewString(table, "warehouse_code")
	w.Name = field.NewString(table, "name")
	w.ThirdCode = field.NewString(table, "third_code")
	w.ActiveFlag = field.NewBool(table, "active_flag")
	w.CreatedAt = field.NewTime(table, "created_at")
	w.CreatedBy = field.NewInt64(table, "created_by")
	w.UpdatedAt = field.NewTime(table, "updated_at")
	w.UpdatedBy = field.NewInt64(table, "updated_by")
	w.FlagDeleted = field.NewInt32(table, "flag_deleted")
	w.DeletedBy = field.NewInt64(table, "deleted_by")
	w.DeletedAt = field.NewField(table, "deleted_at")
	w.DefaultFlag = field.NewInt32(table, "default_flag")
	w.PhysicalWarehouseCode = field.NewString(table, "physical_warehouse_code")
	w.LogicalWarehouseCode = field.NewString(table, "logical_warehouse_code")

	w.fillFieldMap()

	return w
}

func (w *warehouse) WithContext(ctx context.Context) IWarehouseDo {
	return w.warehouseDo.WithContext(ctx)
}

func (w warehouse) TableName() string { return w.warehouseDo.TableName() }

func (w warehouse) Alias() string { return w.warehouseDo.Alias() }

func (w warehouse) Columns(cols ...field.Expr) gen.Columns { return w.warehouseDo.Columns(cols...) }

func (w *warehouse) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *warehouse) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 15)
	w.fieldMap["id"] = w.ID
	w.fieldMap["warehouse_code"] = w.WarehouseCode
	w.fieldMap["name"] = w.Name
	w.fieldMap["third_code"] = w.ThirdCode
	w.fieldMap["active_flag"] = w.ActiveFlag
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["created_by"] = w.CreatedBy
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["updated_by"] = w.UpdatedBy
	w.fieldMap["flag_deleted"] = w.FlagDeleted
	w.fieldMap["deleted_by"] = w.DeletedBy
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["default_flag"] = w.DefaultFlag
	w.fieldMap["physical_warehouse_code"] = w.PhysicalWarehouseCode
	w.fieldMap["logical_warehouse_code"] = w.LogicalWarehouseCode
}

func (w warehouse) clone(db *gorm.DB) warehouse {
	w.warehouseDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w warehouse) replaceDB(db *gorm.DB) warehouse {
	w.warehouseDo.ReplaceDB(db)
	return w
}

type warehouseDo struct{ gen.DO }

type IWarehouseDo interface {
	gen.SubQuery
	Debug() IWarehouseDo
	WithContext(ctx context.Context) IWarehouseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWarehouseDo
	WriteDB() IWarehouseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWarehouseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWarehouseDo
	Not(conds ...gen.Condition) IWarehouseDo
	Or(conds ...gen.Condition) IWarehouseDo
	Select(conds ...field.Expr) IWarehouseDo
	Where(conds ...gen.Condition) IWarehouseDo
	Order(conds ...field.Expr) IWarehouseDo
	Distinct(cols ...field.Expr) IWarehouseDo
	Omit(cols ...field.Expr) IWarehouseDo
	Join(table schema.Tabler, on ...field.Expr) IWarehouseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseDo
	Group(cols ...field.Expr) IWarehouseDo
	Having(conds ...gen.Condition) IWarehouseDo
	Limit(limit int) IWarehouseDo
	Offset(offset int) IWarehouseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseDo
	Unscoped() IWarehouseDo
	Create(values ...*model.Warehouse) error
	CreateInBatches(values model.WarehouseSlice, batchSize int) error
	Save(values ...*model.Warehouse) error
	First() (*model.Warehouse, error)
	Take() (*model.Warehouse, error)
	Last() (*model.Warehouse, error)
	Find() (model.WarehouseSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseSlice, err error)
	FindInBatches(result *model.WarehouseSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Warehouse) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWarehouseDo
	Assign(attrs ...field.AssignExpr) IWarehouseDo
	Joins(fields ...field.RelationField) IWarehouseDo
	Preload(fields ...field.RelationField) IWarehouseDo
	FirstOrInit() (*model.Warehouse, error)
	FirstOrCreate() (*model.Warehouse, error)
	FindByPage(offset int, limit int) (result model.WarehouseSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWarehouseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (w warehouseDo) Debug() IWarehouseDo {
	return w.withDO(w.DO.Debug())
}

func (w warehouseDo) WithContext(ctx context.Context) IWarehouseDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w warehouseDo) ReadDB() IWarehouseDo {
	return w.Clauses(dbresolver.Read)
}

func (w warehouseDo) WriteDB() IWarehouseDo {
	return w.Clauses(dbresolver.Write)
}

func (w warehouseDo) Session(config *gorm.Session) IWarehouseDo {
	return w.withDO(w.DO.Session(config))
}

func (w warehouseDo) Clauses(conds ...clause.Expression) IWarehouseDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w warehouseDo) Returning(value interface{}, columns ...string) IWarehouseDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w warehouseDo) Not(conds ...gen.Condition) IWarehouseDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w warehouseDo) Or(conds ...gen.Condition) IWarehouseDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w warehouseDo) Select(conds ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w warehouseDo) Where(conds ...gen.Condition) IWarehouseDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w warehouseDo) Order(conds ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w warehouseDo) Distinct(cols ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w warehouseDo) Omit(cols ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w warehouseDo) Join(table schema.Tabler, on ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w warehouseDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w warehouseDo) RightJoin(table schema.Tabler, on ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w warehouseDo) Group(cols ...field.Expr) IWarehouseDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w warehouseDo) Having(conds ...gen.Condition) IWarehouseDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w warehouseDo) Limit(limit int) IWarehouseDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w warehouseDo) Offset(offset int) IWarehouseDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w warehouseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWarehouseDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w warehouseDo) Unscoped() IWarehouseDo {
	return w.withDO(w.DO.Unscoped())
}

func (w warehouseDo) Create(values ...*model.Warehouse) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w warehouseDo) CreateInBatches(values model.WarehouseSlice, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w warehouseDo) Save(values ...*model.Warehouse) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w warehouseDo) First() (*model.Warehouse, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warehouse), nil
	}
}

func (w warehouseDo) Take() (*model.Warehouse, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warehouse), nil
	}
}

func (w warehouseDo) Last() (*model.Warehouse, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warehouse), nil
	}
}

func (w warehouseDo) Find() (model.WarehouseSlice, error) {
	result, err := w.DO.Find()
	if err != nil {
		return model.WarehouseSlice{}, err
	}
	if slice, ok := result.([]*model.Warehouse); ok {
		return model.WarehouseSlice(slice), err
	}
	return model.WarehouseSlice{}, err

}

func (w warehouseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.WarehouseSlice, err error) {
	buf := make([]*model.Warehouse, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w warehouseDo) FindInBatches(result *model.WarehouseSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w warehouseDo) Attrs(attrs ...field.AssignExpr) IWarehouseDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w warehouseDo) Assign(attrs ...field.AssignExpr) IWarehouseDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w warehouseDo) Joins(fields ...field.RelationField) IWarehouseDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w warehouseDo) Preload(fields ...field.RelationField) IWarehouseDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w warehouseDo) FirstOrInit() (*model.Warehouse, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warehouse), nil
	}
}

func (w warehouseDo) FirstOrCreate() (*model.Warehouse, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Warehouse), nil
	}
}

func (w warehouseDo) FindByPage(offset int, limit int) (result model.WarehouseSlice, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w warehouseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w warehouseDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w warehouseDo) Delete(models ...*model.Warehouse) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *warehouseDo) withDO(do gen.Dao) *warehouseDo {
	w.DO = *do.(*gen.DO)
	return w
}
