// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockLog(db *gorm.DB, opts ...gen.DOOption) stockLog {
	_stockLog := stockLog{}

	_stockLog.stockLogDo.UseDB(db, opts...)
	_stockLog.stockLogDo.UseModel(&model.StockLog{})

	tableName := _stockLog.stockLogDo.TableName()
	_stockLog.ALL = field.NewAsterisk(tableName)
	_stockLog.ID = field.NewInt32(tableName, "id")
	_stockLog.Sku = field.NewString(tableName, "sku")
	_stockLog.Num = field.NewInt32(tableName, "num")
	_stockLog.QuantityAfterChange = field.NewInt32(tableName, "quantity_after_change")
	_stockLog.CreatedTime = field.NewTime(tableName, "created_time")
	_stockLog.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_stockLog.fillFieldMap()

	return _stockLog
}

type stockLog struct {
	stockLogDo stockLogDo

	ALL                 field.Asterisk
	ID                  field.Int32
	Sku                 field.String
	Num                 field.Int32
	QuantityAfterChange field.Int32
	CreatedTime         field.Time
	WarehouseCode       field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (s stockLog) Table(newTableName string) *stockLog {
	s.stockLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockLog) As(alias string) *stockLog {
	s.stockLogDo.DO = *(s.stockLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockLog) updateTableName(table string) *stockLog {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt32(table, "id")
	s.Sku = field.NewString(table, "sku")
	s.Num = field.NewInt32(table, "num")
	s.QuantityAfterChange = field.NewInt32(table, "quantity_after_change")
	s.CreatedTime = field.NewTime(table, "created_time")
	s.WarehouseCode = field.NewString(table, "warehouse_code")

	s.fillFieldMap()

	return s
}

func (s *stockLog) WithContext(ctx context.Context) IStockLogDo { return s.stockLogDo.WithContext(ctx) }

func (s stockLog) TableName() string { return s.stockLogDo.TableName() }

func (s stockLog) Alias() string { return s.stockLogDo.Alias() }

func (s stockLog) Columns(cols ...field.Expr) gen.Columns { return s.stockLogDo.Columns(cols...) }

func (s *stockLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["num"] = s.Num
	s.fieldMap["quantity_after_change"] = s.QuantityAfterChange
	s.fieldMap["created_time"] = s.CreatedTime
	s.fieldMap["warehouse_code"] = s.WarehouseCode
}

func (s stockLog) clone(db *gorm.DB) stockLog {
	s.stockLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockLog) replaceDB(db *gorm.DB) stockLog {
	s.stockLogDo.ReplaceDB(db)
	return s
}

type stockLogDo struct{ gen.DO }

type IStockLogDo interface {
	gen.SubQuery
	Debug() IStockLogDo
	WithContext(ctx context.Context) IStockLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockLogDo
	WriteDB() IStockLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockLogDo
	Not(conds ...gen.Condition) IStockLogDo
	Or(conds ...gen.Condition) IStockLogDo
	Select(conds ...field.Expr) IStockLogDo
	Where(conds ...gen.Condition) IStockLogDo
	Order(conds ...field.Expr) IStockLogDo
	Distinct(cols ...field.Expr) IStockLogDo
	Omit(cols ...field.Expr) IStockLogDo
	Join(table schema.Tabler, on ...field.Expr) IStockLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockLogDo
	Group(cols ...field.Expr) IStockLogDo
	Having(conds ...gen.Condition) IStockLogDo
	Limit(limit int) IStockLogDo
	Offset(offset int) IStockLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockLogDo
	Unscoped() IStockLogDo
	Create(values ...*model.StockLog) error
	CreateInBatches(values model.StockLogSlice, batchSize int) error
	Save(values ...*model.StockLog) error
	First() (*model.StockLog, error)
	Take() (*model.StockLog, error)
	Last() (*model.StockLog, error)
	Find() (model.StockLogSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockLogSlice, err error)
	FindInBatches(result *model.StockLogSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockLogDo
	Assign(attrs ...field.AssignExpr) IStockLogDo
	Joins(fields ...field.RelationField) IStockLogDo
	Preload(fields ...field.RelationField) IStockLogDo
	FirstOrInit() (*model.StockLog, error)
	FirstOrCreate() (*model.StockLog, error)
	FindByPage(offset int, limit int) (result model.StockLogSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockLogDo) Debug() IStockLogDo {
	return s.withDO(s.DO.Debug())
}

func (s stockLogDo) WithContext(ctx context.Context) IStockLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockLogDo) ReadDB() IStockLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockLogDo) WriteDB() IStockLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockLogDo) Session(config *gorm.Session) IStockLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockLogDo) Clauses(conds ...clause.Expression) IStockLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockLogDo) Returning(value interface{}, columns ...string) IStockLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockLogDo) Not(conds ...gen.Condition) IStockLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockLogDo) Or(conds ...gen.Condition) IStockLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockLogDo) Select(conds ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockLogDo) Where(conds ...gen.Condition) IStockLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockLogDo) Order(conds ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockLogDo) Distinct(cols ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockLogDo) Omit(cols ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockLogDo) Join(table schema.Tabler, on ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockLogDo) Group(cols ...field.Expr) IStockLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockLogDo) Having(conds ...gen.Condition) IStockLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockLogDo) Limit(limit int) IStockLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockLogDo) Offset(offset int) IStockLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockLogDo) Unscoped() IStockLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockLogDo) Create(values ...*model.StockLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockLogDo) CreateInBatches(values model.StockLogSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockLogDo) Save(values ...*model.StockLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockLogDo) First() (*model.StockLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLog), nil
	}
}

func (s stockLogDo) Take() (*model.StockLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLog), nil
	}
}

func (s stockLogDo) Last() (*model.StockLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLog), nil
	}
}

func (s stockLogDo) Find() (model.StockLogSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockLogSlice{}, err
	}
	if slice, ok := result.([]*model.StockLog); ok {
		return model.StockLogSlice(slice), err
	}
	return model.StockLogSlice{}, err

}

func (s stockLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockLogSlice, err error) {
	buf := make([]*model.StockLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockLogDo) FindInBatches(result *model.StockLogSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockLogDo) Attrs(attrs ...field.AssignExpr) IStockLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockLogDo) Assign(attrs ...field.AssignExpr) IStockLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockLogDo) Joins(fields ...field.RelationField) IStockLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockLogDo) Preload(fields ...field.RelationField) IStockLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockLogDo) FirstOrInit() (*model.StockLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLog), nil
	}
}

func (s stockLogDo) FirstOrCreate() (*model.StockLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLog), nil
	}
}

func (s stockLogDo) FindByPage(offset int, limit int) (result model.StockLogSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockLogDo) Delete(models ...*model.StockLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockLogDo) withDO(do gen.Dao) *stockLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
