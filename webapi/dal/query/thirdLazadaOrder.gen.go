// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newThirdLazadaOrder(db *gorm.DB, opts ...gen.DOOption) thirdLazadaOrder {
	_thirdLazadaOrder := thirdLazadaOrder{}

	_thirdLazadaOrder.thirdLazadaOrderDo.UseDB(db, opts...)
	_thirdLazadaOrder.thirdLazadaOrderDo.UseModel(&model.ThirdLazadaOrder{})

	tableName := _thirdLazadaOrder.thirdLazadaOrderDo.TableName()
	_thirdLazadaOrder.ALL = field.NewAsterisk(tableName)
	_thirdLazadaOrder.OrderID = field.NewString(tableName, "order_id")
	_thirdLazadaOrder.SellerID = field.NewString(tableName, "seller_id")
	_thirdLazadaOrder.BranchNumber = field.NewString(tableName, "branch_number")
	_thirdLazadaOrder.TaxCode = field.NewString(tableName, "tax_code")
	_thirdLazadaOrder.ExtraAttributes = field.NewString(tableName, "extra_attributes")
	_thirdLazadaOrder.AddressUpdatedAt = field.NewString(tableName, "address_updated_at")
	_thirdLazadaOrder.ShippingFee = field.NewString(tableName, "shipping_fee")
	_thirdLazadaOrder.CustomerFirstName = field.NewString(tableName, "customer_first_name")
	_thirdLazadaOrder.PaymentMethod = field.NewString(tableName, "payment_method")
	_thirdLazadaOrder.Statuses = field.NewString(tableName, "statuses")
	_thirdLazadaOrder.Remarks = field.NewString(tableName, "remarks")
	_thirdLazadaOrder.OrderNumber = field.NewString(tableName, "order_number")
	_thirdLazadaOrder.Voucher = field.NewString(tableName, "voucher")
	_thirdLazadaOrder.NationalRegistrationNumber = field.NewString(tableName, "national_registration_number")
	_thirdLazadaOrder.PromisedShippingTimes = field.NewString(tableName, "promised_shipping_times")
	_thirdLazadaOrder.ItemsCount = field.NewInt32(tableName, "items_count")
	_thirdLazadaOrder.VoucherPlatform = field.NewString(tableName, "voucher_platform")
	_thirdLazadaOrder.VoucherSeller = field.NewString(tableName, "voucher_seller")
	_thirdLazadaOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_thirdLazadaOrder.Price = field.NewString(tableName, "price")
	_thirdLazadaOrder.WarehouseCode = field.NewString(tableName, "warehouse_code")
	_thirdLazadaOrder.ShippingFeeOriginal = field.NewString(tableName, "shipping_fee_original")
	_thirdLazadaOrder.ShippingFeeDiscountSeller = field.NewString(tableName, "shipping_fee_discount_seller")
	_thirdLazadaOrder.ShippingFeeDiscountPlatform = field.NewString(tableName, "shipping_fee_discount_platform")
	_thirdLazadaOrder.CustomerLastName = field.NewString(tableName, "customer_last_name")
	_thirdLazadaOrder.GiftOption = field.NewString(tableName, "gift_option")
	_thirdLazadaOrder.VoucherCode = field.NewString(tableName, "voucher_code")
	_thirdLazadaOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_thirdLazadaOrder.DeliveryInfo = field.NewString(tableName, "delivery_info")
	_thirdLazadaOrder.GiftMessage = field.NewString(tableName, "gift_message")
	_thirdLazadaOrder.BuyerNote = field.NewString(tableName, "buyer_note")
	_thirdLazadaOrder.AddressBilling = field.NewString(tableName, "address_billing")
	_thirdLazadaOrder.AddressShipping = field.NewString(tableName, "address_shipping")

	_thirdLazadaOrder.fillFieldMap()

	return _thirdLazadaOrder
}

type thirdLazadaOrder struct {
	thirdLazadaOrderDo thirdLazadaOrderDo

	ALL                         field.Asterisk
	OrderID                     field.String
	SellerID                    field.String
	BranchNumber                field.String
	TaxCode                     field.String
	ExtraAttributes             field.String
	AddressUpdatedAt            field.String
	ShippingFee                 field.String
	CustomerFirstName           field.String
	PaymentMethod               field.String
	Statuses                    field.String
	Remarks                     field.String
	OrderNumber                 field.String
	Voucher                     field.String
	NationalRegistrationNumber  field.String
	PromisedShippingTimes       field.String
	ItemsCount                  field.Int32
	VoucherPlatform             field.String
	VoucherSeller               field.String
	CreatedAt                   field.Time
	Price                       field.String
	WarehouseCode               field.String
	ShippingFeeOriginal         field.String
	ShippingFeeDiscountSeller   field.String
	ShippingFeeDiscountPlatform field.String
	CustomerLastName            field.String
	GiftOption                  field.String
	VoucherCode                 field.String
	UpdatedAt                   field.Time
	DeliveryInfo                field.String
	GiftMessage                 field.String
	BuyerNote                   field.String
	AddressBilling              field.String
	AddressShipping             field.String

	fieldMap map[string]field.Expr
}

func (t thirdLazadaOrder) Table(newTableName string) *thirdLazadaOrder {
	t.thirdLazadaOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thirdLazadaOrder) As(alias string) *thirdLazadaOrder {
	t.thirdLazadaOrderDo.DO = *(t.thirdLazadaOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thirdLazadaOrder) updateTableName(table string) *thirdLazadaOrder {
	t.ALL = field.NewAsterisk(table)
	t.OrderID = field.NewString(table, "order_id")
	t.SellerID = field.NewString(table, "seller_id")
	t.BranchNumber = field.NewString(table, "branch_number")
	t.TaxCode = field.NewString(table, "tax_code")
	t.ExtraAttributes = field.NewString(table, "extra_attributes")
	t.AddressUpdatedAt = field.NewString(table, "address_updated_at")
	t.ShippingFee = field.NewString(table, "shipping_fee")
	t.CustomerFirstName = field.NewString(table, "customer_first_name")
	t.PaymentMethod = field.NewString(table, "payment_method")
	t.Statuses = field.NewString(table, "statuses")
	t.Remarks = field.NewString(table, "remarks")
	t.OrderNumber = field.NewString(table, "order_number")
	t.Voucher = field.NewString(table, "voucher")
	t.NationalRegistrationNumber = field.NewString(table, "national_registration_number")
	t.PromisedShippingTimes = field.NewString(table, "promised_shipping_times")
	t.ItemsCount = field.NewInt32(table, "items_count")
	t.VoucherPlatform = field.NewString(table, "voucher_platform")
	t.VoucherSeller = field.NewString(table, "voucher_seller")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.Price = field.NewString(table, "price")
	t.WarehouseCode = field.NewString(table, "warehouse_code")
	t.ShippingFeeOriginal = field.NewString(table, "shipping_fee_original")
	t.ShippingFeeDiscountSeller = field.NewString(table, "shipping_fee_discount_seller")
	t.ShippingFeeDiscountPlatform = field.NewString(table, "shipping_fee_discount_platform")
	t.CustomerLastName = field.NewString(table, "customer_last_name")
	t.GiftOption = field.NewString(table, "gift_option")
	t.VoucherCode = field.NewString(table, "voucher_code")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.DeliveryInfo = field.NewString(table, "delivery_info")
	t.GiftMessage = field.NewString(table, "gift_message")
	t.BuyerNote = field.NewString(table, "buyer_note")
	t.AddressBilling = field.NewString(table, "address_billing")
	t.AddressShipping = field.NewString(table, "address_shipping")

	t.fillFieldMap()

	return t
}

func (t *thirdLazadaOrder) WithContext(ctx context.Context) IThirdLazadaOrderDo {
	return t.thirdLazadaOrderDo.WithContext(ctx)
}

func (t thirdLazadaOrder) TableName() string { return t.thirdLazadaOrderDo.TableName() }

func (t thirdLazadaOrder) Alias() string { return t.thirdLazadaOrderDo.Alias() }

func (t thirdLazadaOrder) Columns(cols ...field.Expr) gen.Columns {
	return t.thirdLazadaOrderDo.Columns(cols...)
}

func (t *thirdLazadaOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thirdLazadaOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 33)
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["seller_id"] = t.SellerID
	t.fieldMap["branch_number"] = t.BranchNumber
	t.fieldMap["tax_code"] = t.TaxCode
	t.fieldMap["extra_attributes"] = t.ExtraAttributes
	t.fieldMap["address_updated_at"] = t.AddressUpdatedAt
	t.fieldMap["shipping_fee"] = t.ShippingFee
	t.fieldMap["customer_first_name"] = t.CustomerFirstName
	t.fieldMap["payment_method"] = t.PaymentMethod
	t.fieldMap["statuses"] = t.Statuses
	t.fieldMap["remarks"] = t.Remarks
	t.fieldMap["order_number"] = t.OrderNumber
	t.fieldMap["voucher"] = t.Voucher
	t.fieldMap["national_registration_number"] = t.NationalRegistrationNumber
	t.fieldMap["promised_shipping_times"] = t.PromisedShippingTimes
	t.fieldMap["items_count"] = t.ItemsCount
	t.fieldMap["voucher_platform"] = t.VoucherPlatform
	t.fieldMap["voucher_seller"] = t.VoucherSeller
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["price"] = t.Price
	t.fieldMap["warehouse_code"] = t.WarehouseCode
	t.fieldMap["shipping_fee_original"] = t.ShippingFeeOriginal
	t.fieldMap["shipping_fee_discount_seller"] = t.ShippingFeeDiscountSeller
	t.fieldMap["shipping_fee_discount_platform"] = t.ShippingFeeDiscountPlatform
	t.fieldMap["customer_last_name"] = t.CustomerLastName
	t.fieldMap["gift_option"] = t.GiftOption
	t.fieldMap["voucher_code"] = t.VoucherCode
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["delivery_info"] = t.DeliveryInfo
	t.fieldMap["gift_message"] = t.GiftMessage
	t.fieldMap["buyer_note"] = t.BuyerNote
	t.fieldMap["address_billing"] = t.AddressBilling
	t.fieldMap["address_shipping"] = t.AddressShipping
}

func (t thirdLazadaOrder) clone(db *gorm.DB) thirdLazadaOrder {
	t.thirdLazadaOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thirdLazadaOrder) replaceDB(db *gorm.DB) thirdLazadaOrder {
	t.thirdLazadaOrderDo.ReplaceDB(db)
	return t
}

type thirdLazadaOrderDo struct{ gen.DO }

type IThirdLazadaOrderDo interface {
	gen.SubQuery
	Debug() IThirdLazadaOrderDo
	WithContext(ctx context.Context) IThirdLazadaOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThirdLazadaOrderDo
	WriteDB() IThirdLazadaOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThirdLazadaOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThirdLazadaOrderDo
	Not(conds ...gen.Condition) IThirdLazadaOrderDo
	Or(conds ...gen.Condition) IThirdLazadaOrderDo
	Select(conds ...field.Expr) IThirdLazadaOrderDo
	Where(conds ...gen.Condition) IThirdLazadaOrderDo
	Order(conds ...field.Expr) IThirdLazadaOrderDo
	Distinct(cols ...field.Expr) IThirdLazadaOrderDo
	Omit(cols ...field.Expr) IThirdLazadaOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo
	Group(cols ...field.Expr) IThirdLazadaOrderDo
	Having(conds ...gen.Condition) IThirdLazadaOrderDo
	Limit(limit int) IThirdLazadaOrderDo
	Offset(offset int) IThirdLazadaOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdLazadaOrderDo
	Unscoped() IThirdLazadaOrderDo
	Create(values ...*model.ThirdLazadaOrder) error
	CreateInBatches(values model.ThirdLazadaOrderSlice, batchSize int) error
	Save(values ...*model.ThirdLazadaOrder) error
	First() (*model.ThirdLazadaOrder, error)
	Take() (*model.ThirdLazadaOrder, error)
	Last() (*model.ThirdLazadaOrder, error)
	Find() (model.ThirdLazadaOrderSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdLazadaOrderSlice, err error)
	FindInBatches(result *model.ThirdLazadaOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThirdLazadaOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThirdLazadaOrderDo
	Assign(attrs ...field.AssignExpr) IThirdLazadaOrderDo
	Joins(fields ...field.RelationField) IThirdLazadaOrderDo
	Preload(fields ...field.RelationField) IThirdLazadaOrderDo
	FirstOrInit() (*model.ThirdLazadaOrder, error)
	FirstOrCreate() (*model.ThirdLazadaOrder, error)
	FindByPage(offset int, limit int) (result model.ThirdLazadaOrderSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThirdLazadaOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thirdLazadaOrderDo) Debug() IThirdLazadaOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thirdLazadaOrderDo) WithContext(ctx context.Context) IThirdLazadaOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thirdLazadaOrderDo) ReadDB() IThirdLazadaOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thirdLazadaOrderDo) WriteDB() IThirdLazadaOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thirdLazadaOrderDo) Session(config *gorm.Session) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thirdLazadaOrderDo) Clauses(conds ...clause.Expression) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thirdLazadaOrderDo) Returning(value interface{}, columns ...string) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thirdLazadaOrderDo) Not(conds ...gen.Condition) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thirdLazadaOrderDo) Or(conds ...gen.Condition) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thirdLazadaOrderDo) Select(conds ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thirdLazadaOrderDo) Where(conds ...gen.Condition) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thirdLazadaOrderDo) Order(conds ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thirdLazadaOrderDo) Distinct(cols ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thirdLazadaOrderDo) Omit(cols ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thirdLazadaOrderDo) Join(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thirdLazadaOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thirdLazadaOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thirdLazadaOrderDo) Group(cols ...field.Expr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thirdLazadaOrderDo) Having(conds ...gen.Condition) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thirdLazadaOrderDo) Limit(limit int) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thirdLazadaOrderDo) Offset(offset int) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thirdLazadaOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thirdLazadaOrderDo) Unscoped() IThirdLazadaOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thirdLazadaOrderDo) Create(values ...*model.ThirdLazadaOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thirdLazadaOrderDo) CreateInBatches(values model.ThirdLazadaOrderSlice, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thirdLazadaOrderDo) Save(values ...*model.ThirdLazadaOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thirdLazadaOrderDo) First() (*model.ThirdLazadaOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrder), nil
	}
}

func (t thirdLazadaOrderDo) Take() (*model.ThirdLazadaOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrder), nil
	}
}

func (t thirdLazadaOrderDo) Last() (*model.ThirdLazadaOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrder), nil
	}
}

func (t thirdLazadaOrderDo) Find() (model.ThirdLazadaOrderSlice, error) {
	result, err := t.DO.Find()
	if err != nil {
		return model.ThirdLazadaOrderSlice{}, err
	}
	if slice, ok := result.([]*model.ThirdLazadaOrder); ok {
		return model.ThirdLazadaOrderSlice(slice), err
	}
	return model.ThirdLazadaOrderSlice{}, err

}

func (t thirdLazadaOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.ThirdLazadaOrderSlice, err error) {
	buf := make([]*model.ThirdLazadaOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thirdLazadaOrderDo) FindInBatches(result *model.ThirdLazadaOrderSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thirdLazadaOrderDo) Attrs(attrs ...field.AssignExpr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thirdLazadaOrderDo) Assign(attrs ...field.AssignExpr) IThirdLazadaOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thirdLazadaOrderDo) Joins(fields ...field.RelationField) IThirdLazadaOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thirdLazadaOrderDo) Preload(fields ...field.RelationField) IThirdLazadaOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thirdLazadaOrderDo) FirstOrInit() (*model.ThirdLazadaOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrder), nil
	}
}

func (t thirdLazadaOrderDo) FirstOrCreate() (*model.ThirdLazadaOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThirdLazadaOrder), nil
	}
}

func (t thirdLazadaOrderDo) FindByPage(offset int, limit int) (result model.ThirdLazadaOrderSlice, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thirdLazadaOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thirdLazadaOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thirdLazadaOrderDo) Delete(models ...*model.ThirdLazadaOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thirdLazadaOrderDo) withDO(do gen.Dao) *thirdLazadaOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
