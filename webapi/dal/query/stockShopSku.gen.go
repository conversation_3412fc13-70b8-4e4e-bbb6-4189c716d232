// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockShopSku(db *gorm.DB, opts ...gen.DOOption) stockShopSku {
	_stockShopSku := stockShopSku{}

	_stockShopSku.stockShopSkuDo.UseDB(db, opts...)
	_stockShopSku.stockShopSkuDo.UseModel(&model.StockShopSku{})

	tableName := _stockShopSku.stockShopSkuDo.TableName()
	_stockShopSku.ALL = field.NewAsterisk(tableName)
	_stockShopSku.ID = field.NewInt64(tableName, "id")
	_stockShopSku.ShopID = field.NewInt32(tableName, "shop_id")
	_stockShopSku.Sku = field.NewString(tableName, "sku")
	_stockShopSku.ProductID = field.NewString(tableName, "product_id")
	_stockShopSku.SkuID = field.NewString(tableName, "sku_id")
	_stockShopSku.OnShelfStock = field.NewInt32(tableName, "on_shelf_stock")
	_stockShopSku.Stock = field.NewInt32(tableName, "stock")
	_stockShopSku.AlreadyOccupied = field.NewInt32(tableName, "already_occupied")
	_stockShopSku.OccupationFailed = field.NewInt32(tableName, "occupation_failed")
	_stockShopSku.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_stockShopSku.fillFieldMap()

	return _stockShopSku
}

type stockShopSku struct {
	stockShopSkuDo stockShopSkuDo

	ALL              field.Asterisk
	ID               field.Int64
	ShopID           field.Int32
	Sku              field.String
	ProductID        field.String
	SkuID            field.String
	OnShelfStock     field.Int32
	Stock            field.Int32
	AlreadyOccupied  field.Int32
	OccupationFailed field.Int32
	WarehouseCode    field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (s stockShopSku) Table(newTableName string) *stockShopSku {
	s.stockShopSkuDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockShopSku) As(alias string) *stockShopSku {
	s.stockShopSkuDo.DO = *(s.stockShopSkuDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockShopSku) updateTableName(table string) *stockShopSku {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ShopID = field.NewInt32(table, "shop_id")
	s.Sku = field.NewString(table, "sku")
	s.ProductID = field.NewString(table, "product_id")
	s.SkuID = field.NewString(table, "sku_id")
	s.OnShelfStock = field.NewInt32(table, "on_shelf_stock")
	s.Stock = field.NewInt32(table, "stock")
	s.AlreadyOccupied = field.NewInt32(table, "already_occupied")
	s.OccupationFailed = field.NewInt32(table, "occupation_failed")
	s.WarehouseCode = field.NewString(table, "warehouse_code")

	s.fillFieldMap()

	return s
}

func (s *stockShopSku) WithContext(ctx context.Context) IStockShopSkuDo {
	return s.stockShopSkuDo.WithContext(ctx)
}

func (s stockShopSku) TableName() string { return s.stockShopSkuDo.TableName() }

func (s stockShopSku) Alias() string { return s.stockShopSkuDo.Alias() }

func (s stockShopSku) Columns(cols ...field.Expr) gen.Columns {
	return s.stockShopSkuDo.Columns(cols...)
}

func (s *stockShopSku) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockShopSku) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["shop_id"] = s.ShopID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["product_id"] = s.ProductID
	s.fieldMap["sku_id"] = s.SkuID
	s.fieldMap["on_shelf_stock"] = s.OnShelfStock
	s.fieldMap["stock"] = s.Stock
	s.fieldMap["already_occupied"] = s.AlreadyOccupied
	s.fieldMap["occupation_failed"] = s.OccupationFailed
	s.fieldMap["warehouse_code"] = s.WarehouseCode
}

func (s stockShopSku) clone(db *gorm.DB) stockShopSku {
	s.stockShopSkuDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockShopSku) replaceDB(db *gorm.DB) stockShopSku {
	s.stockShopSkuDo.ReplaceDB(db)
	return s
}

type stockShopSkuDo struct{ gen.DO }

type IStockShopSkuDo interface {
	gen.SubQuery
	Debug() IStockShopSkuDo
	WithContext(ctx context.Context) IStockShopSkuDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockShopSkuDo
	WriteDB() IStockShopSkuDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockShopSkuDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockShopSkuDo
	Not(conds ...gen.Condition) IStockShopSkuDo
	Or(conds ...gen.Condition) IStockShopSkuDo
	Select(conds ...field.Expr) IStockShopSkuDo
	Where(conds ...gen.Condition) IStockShopSkuDo
	Order(conds ...field.Expr) IStockShopSkuDo
	Distinct(cols ...field.Expr) IStockShopSkuDo
	Omit(cols ...field.Expr) IStockShopSkuDo
	Join(table schema.Tabler, on ...field.Expr) IStockShopSkuDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockShopSkuDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockShopSkuDo
	Group(cols ...field.Expr) IStockShopSkuDo
	Having(conds ...gen.Condition) IStockShopSkuDo
	Limit(limit int) IStockShopSkuDo
	Offset(offset int) IStockShopSkuDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockShopSkuDo
	Unscoped() IStockShopSkuDo
	Create(values ...*model.StockShopSku) error
	CreateInBatches(values model.StockShopSkuSlice, batchSize int) error
	Save(values ...*model.StockShopSku) error
	First() (*model.StockShopSku, error)
	Take() (*model.StockShopSku, error)
	Last() (*model.StockShopSku, error)
	Find() (model.StockShopSkuSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockShopSkuSlice, err error)
	FindInBatches(result *model.StockShopSkuSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockShopSku) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockShopSkuDo
	Assign(attrs ...field.AssignExpr) IStockShopSkuDo
	Joins(fields ...field.RelationField) IStockShopSkuDo
	Preload(fields ...field.RelationField) IStockShopSkuDo
	FirstOrInit() (*model.StockShopSku, error)
	FirstOrCreate() (*model.StockShopSku, error)
	FindByPage(offset int, limit int) (result model.StockShopSkuSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockShopSkuDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockShopSkuDo) Debug() IStockShopSkuDo {
	return s.withDO(s.DO.Debug())
}

func (s stockShopSkuDo) WithContext(ctx context.Context) IStockShopSkuDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockShopSkuDo) ReadDB() IStockShopSkuDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockShopSkuDo) WriteDB() IStockShopSkuDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockShopSkuDo) Session(config *gorm.Session) IStockShopSkuDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockShopSkuDo) Clauses(conds ...clause.Expression) IStockShopSkuDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockShopSkuDo) Returning(value interface{}, columns ...string) IStockShopSkuDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockShopSkuDo) Not(conds ...gen.Condition) IStockShopSkuDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockShopSkuDo) Or(conds ...gen.Condition) IStockShopSkuDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockShopSkuDo) Select(conds ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockShopSkuDo) Where(conds ...gen.Condition) IStockShopSkuDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockShopSkuDo) Order(conds ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockShopSkuDo) Distinct(cols ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockShopSkuDo) Omit(cols ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockShopSkuDo) Join(table schema.Tabler, on ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockShopSkuDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockShopSkuDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockShopSkuDo) Group(cols ...field.Expr) IStockShopSkuDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockShopSkuDo) Having(conds ...gen.Condition) IStockShopSkuDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockShopSkuDo) Limit(limit int) IStockShopSkuDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockShopSkuDo) Offset(offset int) IStockShopSkuDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockShopSkuDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockShopSkuDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockShopSkuDo) Unscoped() IStockShopSkuDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockShopSkuDo) Create(values ...*model.StockShopSku) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockShopSkuDo) CreateInBatches(values model.StockShopSkuSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockShopSkuDo) Save(values ...*model.StockShopSku) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockShopSkuDo) First() (*model.StockShopSku, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockShopSku), nil
	}
}

func (s stockShopSkuDo) Take() (*model.StockShopSku, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockShopSku), nil
	}
}

func (s stockShopSkuDo) Last() (*model.StockShopSku, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockShopSku), nil
	}
}

func (s stockShopSkuDo) Find() (model.StockShopSkuSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockShopSkuSlice{}, err
	}
	if slice, ok := result.([]*model.StockShopSku); ok {
		return model.StockShopSkuSlice(slice), err
	}
	return model.StockShopSkuSlice{}, err

}

func (s stockShopSkuDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockShopSkuSlice, err error) {
	buf := make([]*model.StockShopSku, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockShopSkuDo) FindInBatches(result *model.StockShopSkuSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockShopSkuDo) Attrs(attrs ...field.AssignExpr) IStockShopSkuDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockShopSkuDo) Assign(attrs ...field.AssignExpr) IStockShopSkuDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockShopSkuDo) Joins(fields ...field.RelationField) IStockShopSkuDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockShopSkuDo) Preload(fields ...field.RelationField) IStockShopSkuDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockShopSkuDo) FirstOrInit() (*model.StockShopSku, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockShopSku), nil
	}
}

func (s stockShopSkuDo) FirstOrCreate() (*model.StockShopSku, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockShopSku), nil
	}
}

func (s stockShopSkuDo) FindByPage(offset int, limit int) (result model.StockShopSkuSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockShopSkuDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockShopSkuDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockShopSkuDo) Delete(models ...*model.StockShopSku) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockShopSkuDo) withDO(do gen.Dao) *stockShopSkuDo {
	s.DO = *do.(*gen.DO)
	return s
}
