// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockLack(db *gorm.DB, opts ...gen.DOOption) stockLack {
	_stockLack := stockLack{}

	_stockLack.stockLackDo.UseDB(db, opts...)
	_stockLack.stockLackDo.UseModel(&model.StockLack{})

	tableName := _stockLack.stockLackDo.TableName()
	_stockLack.ALL = field.NewAsterisk(tableName)
	_stockLack.ID = field.NewInt64(tableName, "id")
	_stockLack.ShopID = field.NewInt32(tableName, "shop_id")
	_stockLack.OrderID = field.NewString(tableName, "order_id")
	_stockLack.Sku = field.NewString(tableName, "sku")
	_stockLack.Num = field.NewInt32(tableName, "num")
	_stockLack.CreatedTime = field.NewTime(tableName, "created_time")
	_stockLack.UpdateTime = field.NewTime(tableName, "update_time")

	_stockLack.fillFieldMap()

	return _stockLack
}

type stockLack struct {
	stockLackDo stockLackDo

	ALL         field.Asterisk
	ID          field.Int64
	ShopID      field.Int32
	OrderID     field.String
	Sku         field.String
	Num         field.Int32
	CreatedTime field.Time
	UpdateTime  field.Time

	fieldMap map[string]field.Expr
}

func (s stockLack) Table(newTableName string) *stockLack {
	s.stockLackDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockLack) As(alias string) *stockLack {
	s.stockLackDo.DO = *(s.stockLackDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockLack) updateTableName(table string) *stockLack {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ShopID = field.NewInt32(table, "shop_id")
	s.OrderID = field.NewString(table, "order_id")
	s.Sku = field.NewString(table, "sku")
	s.Num = field.NewInt32(table, "num")
	s.CreatedTime = field.NewTime(table, "created_time")
	s.UpdateTime = field.NewTime(table, "update_time")

	s.fillFieldMap()

	return s
}

func (s *stockLack) WithContext(ctx context.Context) IStockLackDo {
	return s.stockLackDo.WithContext(ctx)
}

func (s stockLack) TableName() string { return s.stockLackDo.TableName() }

func (s stockLack) Alias() string { return s.stockLackDo.Alias() }

func (s stockLack) Columns(cols ...field.Expr) gen.Columns { return s.stockLackDo.Columns(cols...) }

func (s *stockLack) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockLack) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 7)
	s.fieldMap["id"] = s.ID
	s.fieldMap["shop_id"] = s.ShopID
	s.fieldMap["order_id"] = s.OrderID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["num"] = s.Num
	s.fieldMap["created_time"] = s.CreatedTime
	s.fieldMap["update_time"] = s.UpdateTime
}

func (s stockLack) clone(db *gorm.DB) stockLack {
	s.stockLackDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockLack) replaceDB(db *gorm.DB) stockLack {
	s.stockLackDo.ReplaceDB(db)
	return s
}

type stockLackDo struct{ gen.DO }

type IStockLackDo interface {
	gen.SubQuery
	Debug() IStockLackDo
	WithContext(ctx context.Context) IStockLackDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockLackDo
	WriteDB() IStockLackDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockLackDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockLackDo
	Not(conds ...gen.Condition) IStockLackDo
	Or(conds ...gen.Condition) IStockLackDo
	Select(conds ...field.Expr) IStockLackDo
	Where(conds ...gen.Condition) IStockLackDo
	Order(conds ...field.Expr) IStockLackDo
	Distinct(cols ...field.Expr) IStockLackDo
	Omit(cols ...field.Expr) IStockLackDo
	Join(table schema.Tabler, on ...field.Expr) IStockLackDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockLackDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockLackDo
	Group(cols ...field.Expr) IStockLackDo
	Having(conds ...gen.Condition) IStockLackDo
	Limit(limit int) IStockLackDo
	Offset(offset int) IStockLackDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockLackDo
	Unscoped() IStockLackDo
	Create(values ...*model.StockLack) error
	CreateInBatches(values model.StockLackSlice, batchSize int) error
	Save(values ...*model.StockLack) error
	First() (*model.StockLack, error)
	Take() (*model.StockLack, error)
	Last() (*model.StockLack, error)
	Find() (model.StockLackSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockLackSlice, err error)
	FindInBatches(result *model.StockLackSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockLack) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockLackDo
	Assign(attrs ...field.AssignExpr) IStockLackDo
	Joins(fields ...field.RelationField) IStockLackDo
	Preload(fields ...field.RelationField) IStockLackDo
	FirstOrInit() (*model.StockLack, error)
	FirstOrCreate() (*model.StockLack, error)
	FindByPage(offset int, limit int) (result model.StockLackSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockLackDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockLackDo) Debug() IStockLackDo {
	return s.withDO(s.DO.Debug())
}

func (s stockLackDo) WithContext(ctx context.Context) IStockLackDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockLackDo) ReadDB() IStockLackDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockLackDo) WriteDB() IStockLackDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockLackDo) Session(config *gorm.Session) IStockLackDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockLackDo) Clauses(conds ...clause.Expression) IStockLackDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockLackDo) Returning(value interface{}, columns ...string) IStockLackDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockLackDo) Not(conds ...gen.Condition) IStockLackDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockLackDo) Or(conds ...gen.Condition) IStockLackDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockLackDo) Select(conds ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockLackDo) Where(conds ...gen.Condition) IStockLackDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockLackDo) Order(conds ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockLackDo) Distinct(cols ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockLackDo) Omit(cols ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockLackDo) Join(table schema.Tabler, on ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockLackDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockLackDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockLackDo) Group(cols ...field.Expr) IStockLackDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockLackDo) Having(conds ...gen.Condition) IStockLackDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockLackDo) Limit(limit int) IStockLackDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockLackDo) Offset(offset int) IStockLackDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockLackDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockLackDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockLackDo) Unscoped() IStockLackDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockLackDo) Create(values ...*model.StockLack) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockLackDo) CreateInBatches(values model.StockLackSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockLackDo) Save(values ...*model.StockLack) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockLackDo) First() (*model.StockLack, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLack), nil
	}
}

func (s stockLackDo) Take() (*model.StockLack, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLack), nil
	}
}

func (s stockLackDo) Last() (*model.StockLack, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLack), nil
	}
}

func (s stockLackDo) Find() (model.StockLackSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockLackSlice{}, err
	}
	if slice, ok := result.([]*model.StockLack); ok {
		return model.StockLackSlice(slice), err
	}
	return model.StockLackSlice{}, err

}

func (s stockLackDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockLackSlice, err error) {
	buf := make([]*model.StockLack, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockLackDo) FindInBatches(result *model.StockLackSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockLackDo) Attrs(attrs ...field.AssignExpr) IStockLackDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockLackDo) Assign(attrs ...field.AssignExpr) IStockLackDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockLackDo) Joins(fields ...field.RelationField) IStockLackDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockLackDo) Preload(fields ...field.RelationField) IStockLackDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockLackDo) FirstOrInit() (*model.StockLack, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLack), nil
	}
}

func (s stockLackDo) FirstOrCreate() (*model.StockLack, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockLack), nil
	}
}

func (s stockLackDo) FindByPage(offset int, limit int) (result model.StockLackSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockLackDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockLackDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockLackDo) Delete(models ...*model.StockLack) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockLackDo) withDO(do gen.Dao) *stockLackDo {
	s.DO = *do.(*gen.DO)
	return s
}
