// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newStockNew(db *gorm.DB, opts ...gen.DOOption) stockNew {
	_stockNew := stockNew{}

	_stockNew.stockNewDo.UseDB(db, opts...)
	_stockNew.stockNewDo.UseModel(&model.StockNew{})

	tableName := _stockNew.stockNewDo.TableName()
	_stockNew.ALL = field.NewAsterisk(tableName)
	_stockNew.Sku = field.NewString(tableName, "sku")
	_stockNew.Quantity = field.NewInt32(tableName, "quantity")
	_stockNew.FreezeQuantity = field.NewInt32(tableName, "freeze_quantity")
	_stockNew.Safety = field.NewInt32(tableName, "safety")
	_stockNew.UpdateTime = field.NewTime(tableName, "update_time")
	_stockNew.WarehouseCode = field.NewString(tableName, "warehouse_code")

	_stockNew.fillFieldMap()

	return _stockNew
}

type stockNew struct {
	stockNewDo stockNewDo

	ALL            field.Asterisk
	Sku            field.String
	Quantity       field.Int32
	FreezeQuantity field.Int32
	Safety         field.Int32
	UpdateTime     field.Time
	WarehouseCode  field.String // 仓库编码

	fieldMap map[string]field.Expr
}

func (s stockNew) Table(newTableName string) *stockNew {
	s.stockNewDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s stockNew) As(alias string) *stockNew {
	s.stockNewDo.DO = *(s.stockNewDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *stockNew) updateTableName(table string) *stockNew {
	s.ALL = field.NewAsterisk(table)
	s.Sku = field.NewString(table, "sku")
	s.Quantity = field.NewInt32(table, "quantity")
	s.FreezeQuantity = field.NewInt32(table, "freeze_quantity")
	s.Safety = field.NewInt32(table, "safety")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.WarehouseCode = field.NewString(table, "warehouse_code")

	s.fillFieldMap()

	return s
}

func (s *stockNew) WithContext(ctx context.Context) IStockNewDo { return s.stockNewDo.WithContext(ctx) }

func (s stockNew) TableName() string { return s.stockNewDo.TableName() }

func (s stockNew) Alias() string { return s.stockNewDo.Alias() }

func (s stockNew) Columns(cols ...field.Expr) gen.Columns { return s.stockNewDo.Columns(cols...) }

func (s *stockNew) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *stockNew) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["quantity"] = s.Quantity
	s.fieldMap["freeze_quantity"] = s.FreezeQuantity
	s.fieldMap["safety"] = s.Safety
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["warehouse_code"] = s.WarehouseCode
}

func (s stockNew) clone(db *gorm.DB) stockNew {
	s.stockNewDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s stockNew) replaceDB(db *gorm.DB) stockNew {
	s.stockNewDo.ReplaceDB(db)
	return s
}

type stockNewDo struct{ gen.DO }

type IStockNewDo interface {
	gen.SubQuery
	Debug() IStockNewDo
	WithContext(ctx context.Context) IStockNewDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStockNewDo
	WriteDB() IStockNewDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStockNewDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStockNewDo
	Not(conds ...gen.Condition) IStockNewDo
	Or(conds ...gen.Condition) IStockNewDo
	Select(conds ...field.Expr) IStockNewDo
	Where(conds ...gen.Condition) IStockNewDo
	Order(conds ...field.Expr) IStockNewDo
	Distinct(cols ...field.Expr) IStockNewDo
	Omit(cols ...field.Expr) IStockNewDo
	Join(table schema.Tabler, on ...field.Expr) IStockNewDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStockNewDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStockNewDo
	Group(cols ...field.Expr) IStockNewDo
	Having(conds ...gen.Condition) IStockNewDo
	Limit(limit int) IStockNewDo
	Offset(offset int) IStockNewDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStockNewDo
	Unscoped() IStockNewDo
	Create(values ...*model.StockNew) error
	CreateInBatches(values model.StockNewSlice, batchSize int) error
	Save(values ...*model.StockNew) error
	First() (*model.StockNew, error)
	Take() (*model.StockNew, error)
	Last() (*model.StockNew, error)
	Find() (model.StockNewSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockNewSlice, err error)
	FindInBatches(result *model.StockNewSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StockNew) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStockNewDo
	Assign(attrs ...field.AssignExpr) IStockNewDo
	Joins(fields ...field.RelationField) IStockNewDo
	Preload(fields ...field.RelationField) IStockNewDo
	FirstOrInit() (*model.StockNew, error)
	FirstOrCreate() (*model.StockNew, error)
	FindByPage(offset int, limit int) (result model.StockNewSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStockNewDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s stockNewDo) Debug() IStockNewDo {
	return s.withDO(s.DO.Debug())
}

func (s stockNewDo) WithContext(ctx context.Context) IStockNewDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s stockNewDo) ReadDB() IStockNewDo {
	return s.Clauses(dbresolver.Read)
}

func (s stockNewDo) WriteDB() IStockNewDo {
	return s.Clauses(dbresolver.Write)
}

func (s stockNewDo) Session(config *gorm.Session) IStockNewDo {
	return s.withDO(s.DO.Session(config))
}

func (s stockNewDo) Clauses(conds ...clause.Expression) IStockNewDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s stockNewDo) Returning(value interface{}, columns ...string) IStockNewDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s stockNewDo) Not(conds ...gen.Condition) IStockNewDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s stockNewDo) Or(conds ...gen.Condition) IStockNewDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s stockNewDo) Select(conds ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s stockNewDo) Where(conds ...gen.Condition) IStockNewDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s stockNewDo) Order(conds ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s stockNewDo) Distinct(cols ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s stockNewDo) Omit(cols ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s stockNewDo) Join(table schema.Tabler, on ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s stockNewDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s stockNewDo) RightJoin(table schema.Tabler, on ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s stockNewDo) Group(cols ...field.Expr) IStockNewDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s stockNewDo) Having(conds ...gen.Condition) IStockNewDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s stockNewDo) Limit(limit int) IStockNewDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s stockNewDo) Offset(offset int) IStockNewDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s stockNewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStockNewDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s stockNewDo) Unscoped() IStockNewDo {
	return s.withDO(s.DO.Unscoped())
}

func (s stockNewDo) Create(values ...*model.StockNew) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s stockNewDo) CreateInBatches(values model.StockNewSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s stockNewDo) Save(values ...*model.StockNew) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s stockNewDo) First() (*model.StockNew, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockNew), nil
	}
}

func (s stockNewDo) Take() (*model.StockNew, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockNew), nil
	}
}

func (s stockNewDo) Last() (*model.StockNew, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockNew), nil
	}
}

func (s stockNewDo) Find() (model.StockNewSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.StockNewSlice{}, err
	}
	if slice, ok := result.([]*model.StockNew); ok {
		return model.StockNewSlice(slice), err
	}
	return model.StockNewSlice{}, err

}

func (s stockNewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.StockNewSlice, err error) {
	buf := make([]*model.StockNew, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s stockNewDo) FindInBatches(result *model.StockNewSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s stockNewDo) Attrs(attrs ...field.AssignExpr) IStockNewDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s stockNewDo) Assign(attrs ...field.AssignExpr) IStockNewDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s stockNewDo) Joins(fields ...field.RelationField) IStockNewDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s stockNewDo) Preload(fields ...field.RelationField) IStockNewDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s stockNewDo) FirstOrInit() (*model.StockNew, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockNew), nil
	}
}

func (s stockNewDo) FirstOrCreate() (*model.StockNew, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StockNew), nil
	}
}

func (s stockNewDo) FindByPage(offset int, limit int) (result model.StockNewSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s stockNewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s stockNewDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s stockNewDo) Delete(models ...*model.StockNew) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *stockNewDo) withDO(do gen.Dao) *stockNewDo {
	s.DO = *do.(*gen.DO)
	return s
}
