// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"webapi/dal/model"
)

func newSkuRelationNew(db *gorm.DB, opts ...gen.DOOption) skuRelationNew {
	_skuRelationNew := skuRelationNew{}

	_skuRelationNew.skuRelationNewDo.UseDB(db, opts...)
	_skuRelationNew.skuRelationNewDo.UseModel(&model.SkuRelationNew{})

	tableName := _skuRelationNew.skuRelationNewDo.TableName()
	_skuRelationNew.ALL = field.NewAsterisk(tableName)
	_skuRelationNew.ID = field.NewInt64(tableName, "id")
	_skuRelationNew.ShopID = field.NewInt32(tableName, "shop_id")
	_skuRelationNew.ProductID = field.NewString(tableName, "product_id")
	_skuRelationNew.SkuID = field.NewString(tableName, "sku_id")
	_skuRelationNew.Sku = field.NewString(tableName, "sku")
	_skuRelationNew.CreatedTime = field.NewTime(tableName, "created_time")

	_skuRelationNew.fillFieldMap()

	return _skuRelationNew
}

type skuRelationNew struct {
	skuRelationNewDo skuRelationNewDo

	ALL         field.Asterisk
	ID          field.Int64
	ShopID      field.Int32
	ProductID   field.String
	SkuID       field.String
	Sku         field.String
	CreatedTime field.Time

	fieldMap map[string]field.Expr
}

func (s skuRelationNew) Table(newTableName string) *skuRelationNew {
	s.skuRelationNewDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s skuRelationNew) As(alias string) *skuRelationNew {
	s.skuRelationNewDo.DO = *(s.skuRelationNewDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *skuRelationNew) updateTableName(table string) *skuRelationNew {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ShopID = field.NewInt32(table, "shop_id")
	s.ProductID = field.NewString(table, "product_id")
	s.SkuID = field.NewString(table, "sku_id")
	s.Sku = field.NewString(table, "sku")
	s.CreatedTime = field.NewTime(table, "created_time")

	s.fillFieldMap()

	return s
}

func (s *skuRelationNew) WithContext(ctx context.Context) ISkuRelationNewDo {
	return s.skuRelationNewDo.WithContext(ctx)
}

func (s skuRelationNew) TableName() string { return s.skuRelationNewDo.TableName() }

func (s skuRelationNew) Alias() string { return s.skuRelationNewDo.Alias() }

func (s skuRelationNew) Columns(cols ...field.Expr) gen.Columns {
	return s.skuRelationNewDo.Columns(cols...)
}

func (s *skuRelationNew) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *skuRelationNew) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["shop_id"] = s.ShopID
	s.fieldMap["product_id"] = s.ProductID
	s.fieldMap["sku_id"] = s.SkuID
	s.fieldMap["sku"] = s.Sku
	s.fieldMap["created_time"] = s.CreatedTime
}

func (s skuRelationNew) clone(db *gorm.DB) skuRelationNew {
	s.skuRelationNewDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s skuRelationNew) replaceDB(db *gorm.DB) skuRelationNew {
	s.skuRelationNewDo.ReplaceDB(db)
	return s
}

type skuRelationNewDo struct{ gen.DO }

type ISkuRelationNewDo interface {
	gen.SubQuery
	Debug() ISkuRelationNewDo
	WithContext(ctx context.Context) ISkuRelationNewDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISkuRelationNewDo
	WriteDB() ISkuRelationNewDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISkuRelationNewDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISkuRelationNewDo
	Not(conds ...gen.Condition) ISkuRelationNewDo
	Or(conds ...gen.Condition) ISkuRelationNewDo
	Select(conds ...field.Expr) ISkuRelationNewDo
	Where(conds ...gen.Condition) ISkuRelationNewDo
	Order(conds ...field.Expr) ISkuRelationNewDo
	Distinct(cols ...field.Expr) ISkuRelationNewDo
	Omit(cols ...field.Expr) ISkuRelationNewDo
	Join(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo
	Group(cols ...field.Expr) ISkuRelationNewDo
	Having(conds ...gen.Condition) ISkuRelationNewDo
	Limit(limit int) ISkuRelationNewDo
	Offset(offset int) ISkuRelationNewDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISkuRelationNewDo
	Unscoped() ISkuRelationNewDo
	Create(values ...*model.SkuRelationNew) error
	CreateInBatches(values model.SkuRelationNewSlice, batchSize int) error
	Save(values ...*model.SkuRelationNew) error
	First() (*model.SkuRelationNew, error)
	Take() (*model.SkuRelationNew, error)
	Last() (*model.SkuRelationNew, error)
	Find() (model.SkuRelationNewSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.SkuRelationNewSlice, err error)
	FindInBatches(result *model.SkuRelationNewSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SkuRelationNew) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISkuRelationNewDo
	Assign(attrs ...field.AssignExpr) ISkuRelationNewDo
	Joins(fields ...field.RelationField) ISkuRelationNewDo
	Preload(fields ...field.RelationField) ISkuRelationNewDo
	FirstOrInit() (*model.SkuRelationNew, error)
	FirstOrCreate() (*model.SkuRelationNew, error)
	FindByPage(offset int, limit int) (result model.SkuRelationNewSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISkuRelationNewDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s skuRelationNewDo) Debug() ISkuRelationNewDo {
	return s.withDO(s.DO.Debug())
}

func (s skuRelationNewDo) WithContext(ctx context.Context) ISkuRelationNewDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s skuRelationNewDo) ReadDB() ISkuRelationNewDo {
	return s.Clauses(dbresolver.Read)
}

func (s skuRelationNewDo) WriteDB() ISkuRelationNewDo {
	return s.Clauses(dbresolver.Write)
}

func (s skuRelationNewDo) Session(config *gorm.Session) ISkuRelationNewDo {
	return s.withDO(s.DO.Session(config))
}

func (s skuRelationNewDo) Clauses(conds ...clause.Expression) ISkuRelationNewDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s skuRelationNewDo) Returning(value interface{}, columns ...string) ISkuRelationNewDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s skuRelationNewDo) Not(conds ...gen.Condition) ISkuRelationNewDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s skuRelationNewDo) Or(conds ...gen.Condition) ISkuRelationNewDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s skuRelationNewDo) Select(conds ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s skuRelationNewDo) Where(conds ...gen.Condition) ISkuRelationNewDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s skuRelationNewDo) Order(conds ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s skuRelationNewDo) Distinct(cols ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s skuRelationNewDo) Omit(cols ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s skuRelationNewDo) Join(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s skuRelationNewDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s skuRelationNewDo) RightJoin(table schema.Tabler, on ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s skuRelationNewDo) Group(cols ...field.Expr) ISkuRelationNewDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s skuRelationNewDo) Having(conds ...gen.Condition) ISkuRelationNewDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s skuRelationNewDo) Limit(limit int) ISkuRelationNewDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s skuRelationNewDo) Offset(offset int) ISkuRelationNewDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s skuRelationNewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISkuRelationNewDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s skuRelationNewDo) Unscoped() ISkuRelationNewDo {
	return s.withDO(s.DO.Unscoped())
}

func (s skuRelationNewDo) Create(values ...*model.SkuRelationNew) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s skuRelationNewDo) CreateInBatches(values model.SkuRelationNewSlice, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s skuRelationNewDo) Save(values ...*model.SkuRelationNew) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s skuRelationNewDo) First() (*model.SkuRelationNew, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SkuRelationNew), nil
	}
}

func (s skuRelationNewDo) Take() (*model.SkuRelationNew, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SkuRelationNew), nil
	}
}

func (s skuRelationNewDo) Last() (*model.SkuRelationNew, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SkuRelationNew), nil
	}
}

func (s skuRelationNewDo) Find() (model.SkuRelationNewSlice, error) {
	result, err := s.DO.Find()
	if err != nil {
		return model.SkuRelationNewSlice{}, err
	}
	if slice, ok := result.([]*model.SkuRelationNew); ok {
		return model.SkuRelationNewSlice(slice), err
	}
	return model.SkuRelationNewSlice{}, err

}

func (s skuRelationNewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results model.SkuRelationNewSlice, err error) {
	buf := make([]*model.SkuRelationNew, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s skuRelationNewDo) FindInBatches(result *model.SkuRelationNewSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s skuRelationNewDo) Attrs(attrs ...field.AssignExpr) ISkuRelationNewDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s skuRelationNewDo) Assign(attrs ...field.AssignExpr) ISkuRelationNewDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s skuRelationNewDo) Joins(fields ...field.RelationField) ISkuRelationNewDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s skuRelationNewDo) Preload(fields ...field.RelationField) ISkuRelationNewDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s skuRelationNewDo) FirstOrInit() (*model.SkuRelationNew, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SkuRelationNew), nil
	}
}

func (s skuRelationNewDo) FirstOrCreate() (*model.SkuRelationNew, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SkuRelationNew), nil
	}
}

func (s skuRelationNewDo) FindByPage(offset int, limit int) (result model.SkuRelationNewSlice, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s skuRelationNewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s skuRelationNewDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s skuRelationNewDo) Delete(models ...*model.SkuRelationNew) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *skuRelationNewDo) withDO(do gen.Dao) *skuRelationNewDo {
	s.DO = *do.(*gen.DO)
	return s
}
