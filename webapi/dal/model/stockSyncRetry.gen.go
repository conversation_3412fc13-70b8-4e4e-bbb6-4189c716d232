// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockSyncRetry = "stock_sync_retry"

// StockSyncRetry mapped from table <stock_sync_retry>
type StockSyncRetry struct {
	ID          int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	StrategyID  int32     `gorm:"column:strategy_id;type:int" json:"strategy_id"`
	Stock       int32     `gorm:"column:stock;type:int" json:"stock"`
	Sku         string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	ShopID      int32     `gorm:"column:shop_id;type:int" json:"shop_id"`
	ProductID   string    `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	SkuID       string    `gorm:"column:sku_id;type:varchar(100)" json:"sku_id"`
	CreatedTime time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
}

// TableName StockSyncRetry's table name
func (*StockSyncRetry) TableName() string {
	return TableNameStockSyncRetry
}
