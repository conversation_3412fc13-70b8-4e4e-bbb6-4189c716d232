// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdShoplineProduct = "third_shopline_product"

// ThirdShoplineProduct mapped from table <third_shopline_product>
type ThirdShoplineProduct struct {
	ID                string `gorm:"column:id;type:varchar(100);primaryKey" json:"id"`
	InventoryItemID   string `gorm:"column:inventory_item_id;type:varchar(100)" json:"inventory_item_id"`
	InventoryQuantity int32  `gorm:"column:inventory_quantity;type:int" json:"inventory_quantity"`
	ProductID         string `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	Sku               string `gorm:"column:sku;type:varchar(100)" json:"sku"`
}

// TableName ThirdShoplineProduct's table name
func (*ThirdShoplineProduct) TableName() string {
	return TableNameThirdShoplineProduct
}
