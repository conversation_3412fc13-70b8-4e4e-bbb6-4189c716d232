// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameFreebie = "freebie"

// Freebie 赠品表
type Freebie struct {
	ID            int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:赠品ID，主键" json:"id"`                                             // 赠品ID，主键
	SkuNo         string         `gorm:"column:sku_no;type:varchar(50);not null;comment:sku编码" json:"sku_no"`                                                       // sku编码
	ActiveStatus  string         `gorm:"column:active_status;type:varchar(10);not null;comment:赠品记录生效状态：pending-待生效, active-生效中, invalid-已失效" json:"active_status"` // 赠品记录生效状态：pending-待生效, active-生效中, invalid-已失效
	StartAt       time.Time      `gorm:"column:start_at;type:timestamp;comment:开始时间" json:"start_at"`                                                               // 开始时间
	EndAt         time.Time      `gorm:"column:end_at;type:timestamp;comment:结束时间" json:"end_at"`                                                                   // 结束时间
	CreatedAt     time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                        // 创建时间
	CreatedBy     int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                                                  // 创建人
	UpdatedAt     time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                                              // 更新时间，更新时自动更新为当前时间
	UpdatedBy     int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                                                  // 更新人
	FlagDeleted   int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`                                // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy     int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                                                  // 删除人
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                                           // 删除时间
	FlagRule      int32          `gorm:"column:flag_rule;type:tinyint;not null;comment:是否有规则，0-否，1-是" json:"flag_rule"`                                             // 是否有规则，0-否，1-是
	WarehouseCode string         `gorm:"column:warehouse_code;type:varchar(64);not null;default:BOE_BC_JKT_EC_PRO;comment:仓库编码" json:"warehouse_code"`              // 仓库编码
}

// TableName Freebie's table name
func (*Freebie) TableName() string {
	return TableNameFreebie
}
