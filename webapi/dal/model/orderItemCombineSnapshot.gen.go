// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderItemCombineSnapshot = "order_item_combine_snapshot"

// OrderItemCombineSnapshot mapped from table <order_item_combine_snapshot>
type OrderItemCombineSnapshot struct {
	ID         int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	OrderID    string `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Sku        string `gorm:"column:sku;type:varchar(100)" json:"sku"`
	CombineSku string `gorm:"column:combine_sku;type:varchar(100)" json:"combine_sku"`
	Count      int32  `gorm:"column:count;type:int" json:"count"`
}

// TableName OrderItemCombineSnapshot's table name
func (*OrderItemCombineSnapshot) TableName() string {
	return TableNameOrderItemCombineSnapshot
}
