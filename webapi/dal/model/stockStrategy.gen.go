// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockStrategy = "stock_strategy"

// StockStrategy mapped from table <stock_strategy>
type StockStrategy struct {
	ID            int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Name          string    `gorm:"column:name;type:varchar(140)" json:"name"`
	ShopType      string    `gorm:"column:shop_type;type:varchar(20)" json:"shop_type"`
	CalcType      string    `gorm:"column:calc_type;type:varchar(20)" json:"calc_type"`
	DimensionType string    `gorm:"column:dimension_type;type:varchar(20)" json:"dimension_type"`
	Proportion    int32     `gorm:"column:proportion;type:int" json:"proportion"`
	Num           int32     `gorm:"column:num;type:int" json:"num"`
	WorkType      string    `gorm:"column:work_type;type:varchar(20)" json:"work_type"`
	WorkTime      time.Time `gorm:"column:work_time;type:datetime" json:"work_time"`
	SyncTime      time.Time `gorm:"column:sync_time;type:datetime" json:"sync_time"`
	CreatedTime   time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	State         string    `gorm:"column:state;type:varchar(20)" json:"state"`
	WarehouseCode string    `gorm:"column:warehouse_code;type:varchar(64);comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName StockStrategy's table name
func (*StockStrategy) TableName() string {
	return TableNameStockStrategy
}
