// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderItemChange = "order_item_change"

// OrderItemChange mapped from table <order_item_change>
type OrderItemChange struct {
	ID          int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderID     string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Sku         string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Num         int32     `gorm:"column:num;type:int" json:"num"`
	Type        string    `gorm:"column:type;type:varchar(6)" json:"type"`
	CreatedTime time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
}

// TableName OrderItemChange's table name
func (*OrderItemChange) TableName() string {
	return TableNameOrderItemChange
}
