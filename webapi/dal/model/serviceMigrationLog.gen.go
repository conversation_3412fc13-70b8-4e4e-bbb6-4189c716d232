// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameServiceMigrationLog = "service_migration_log"

// ServiceMigrationLog 服务迁移日志表
type ServiceMigrationLog struct {
	ID               int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键 ID" json:"id"`                       // 主键 ID
	RequestURL       string    `gorm:"column:request_url;type:text;not null;comment:请求 URL" json:"request_url"`                           // 请求 URL
	RequestMethod    string    `gorm:"column:request_method;type:varchar(10);not null;comment:请求方法" json:"request_method"`                // 请求方法
	RequestPayload   string    `gorm:"column:request_payload;type:text;comment:请求参数" json:"request_payload"`                              // 请求参数
	CreatedAt        time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	ConvertedPayload string    `gorm:"column:converted_payload;type:longtext;comment:转换参数" json:"converted_payload"`                      // 转换参数
	ResponsePayload  string    `gorm:"column:response_payload;type:longtext;comment:API 响应内容" json:"response_payload"`                    // API 响应内容
	FlagNotifySent   bool      `gorm:"column:flag_notify_sent;type:tinyint(1);not null;comment:失败是否已同步飞书" json:"flag_notify_sent"`        // 失败是否已同步飞书
}

// TableName ServiceMigrationLog's table name
func (*ServiceMigrationLog) TableName() string {
	return TableNameServiceMigrationLog
}
