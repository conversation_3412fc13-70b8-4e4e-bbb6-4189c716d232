// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCampaignProductStock = "campaign_product_stock"

// CampaignProductStock mapped from table <campaign_product_stock>
type CampaignProductStock struct {
	ID               int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CampaignInsideID int32     `gorm:"column:campaign_inside_id;type:int;not null;default:-1;comment:campaign表id" json:"campaign_inside_id"` // campaign表id
	Sku              string    `gorm:"column:sku;type:varchar(100);not null;comment:SKU" json:"sku"`                                         // SKU
	CampaignID       string    `gorm:"column:campaign_id;type:varchar(100);not null;comment:三方活动ID" json:"campaign_id"`                      // 三方活动ID
	OriginQuantity   int32     `gorm:"column:origin_quantity;type:int;not null;comment:活动库存数量" json:"origin_quantity"`                       // 活动库存数量
	Quantity         int32     `gorm:"column:quantity;type:int;not null;comment:活动库存数量" json:"quantity"`                                     // 活动库存数量
	FreezeQuantity   int32     `gorm:"column:freeze_quantity;type:int;not null;comment:活动库存锁定数量" json:"freeze_quantity"`                     // 活动库存锁定数量
	WarehouseCode    string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"`                   // 仓库编码
	CreatedAt        time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`   // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`   // 更新时间
}

// TableName CampaignProductStock's table name
func (*CampaignProductStock) TableName() string {
	return TableNameCampaignProductStock
}
