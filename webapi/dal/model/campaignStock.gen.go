// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCampaignStock = "campaign_stock"

// CampaignStock mapped from table <campaign_stock>
type CampaignStock struct {
	ID             int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID         int32     `gorm:"column:shop_id;type:int;not null;comment:店铺ID" json:"shop_id"` // 店铺ID
	Sku            string    `gorm:"column:sku;type:varchar(100);not null;comment:SKU" json:"sku"` // SKU
	CampaignType   string    `gorm:"column:campaign_type;type:varchar(255);not null" json:"campaign_type"`
	Quantity       int32     `gorm:"column:quantity;type:int;not null" json:"quantity"`
	FreezeQuantity int32     `gorm:"column:freeze_quantity;type:int;not null" json:"freeze_quantity"`
	WarehouseCode  string    `gorm:"column:warehouse_code;type:varchar(32);not null" json:"warehouse_code"`
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy      int32     `gorm:"column:created_by;type:int;comment:中台用户ID" json:"created_by"` // 中台用户ID
	UpdatedBy      int32     `gorm:"column:updated_by;type:int;comment:中台用户ID" json:"updated_by"` // 中台用户ID
}

// TableName CampaignStock's table name
func (*CampaignStock) TableName() string {
	return TableNameCampaignStock
}
