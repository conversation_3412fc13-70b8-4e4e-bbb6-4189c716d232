// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSampleBorrowDetail = "sample_borrow_detail"

// SampleBorrowDetail mapped from table <sample_borrow_detail>
type SampleBorrowDetail struct {
	ID                  int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	RequestID           int32     `gorm:"column:request_id;type:int;not null;default:-1;comment:借样申请id" json:"request_id"`                                           // 借样申请id
	SkuNo               string    `gorm:"column:sku_no;type:varchar(255);not null;comment:商品编码" json:"sku_no"`                                                       // 商品编码
	Barcode             string    `gorm:"column:barcode;type:varchar(64);not null;comment:条形码" json:"barcode"`                                                       // 条形码
	LocationCode        string    `gorm:"column:location_code;type:varchar(64);not null;comment:库位编码" json:"location_code"`                                          // 库位编码
	SkuName             string    `gorm:"column:sku_name;type:varchar(255);not null;comment:商品名称" json:"sku_name"`                                                   // 商品名称
	BorrowEmail         string    `gorm:"column:borrow_email;type:varchar(100);not null;comment:借出人邮箱" json:"borrow_email"`                                          // 借出人邮箱
	BorrowNumber        int32     `gorm:"column:borrow_number;type:int;not null;default:-1;comment:借出数量" json:"borrow_number"`                                       // 借出数量
	Purpose             string    `gorm:"column:purpose;type:varchar(255);not null;comment:用途" json:"purpose"`                                                       // 用途
	EstimatedReturnDate time.Time `gorm:"column:estimated_return_date;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:预计归还日期" json:"estimated_return_date"` // 预计归还日期
	CreateTime          time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                       // 创建时间
}

// TableName SampleBorrowDetail's table name
func (*SampleBorrowDetail) TableName() string {
	return TableNameSampleBorrowDetail
}
