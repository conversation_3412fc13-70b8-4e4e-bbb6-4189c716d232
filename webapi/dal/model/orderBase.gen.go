// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderBase = "order_base"

// OrderBase mapped from table <order_base>
type OrderBase struct {
	ID                     string    `gorm:"column:id;type:varchar(100);primaryKey" json:"id"`
	ChannelOrderID         string    `gorm:"column:channel_order_id;type:varchar(100)" json:"channel_order_id"`
	State                  string    `gorm:"column:state;type:varchar(32)" json:"state"`
	DisplayState           string    `gorm:"column:display_state;type:varchar(32)" json:"display_state"`
	StorePlatform          string    `gorm:"column:store_platform;type:varchar(20)" json:"store_platform"`
	ShopID                 int32     `gorm:"column:shop_id;type:smallint" json:"shop_id"`
	StoreState             string    `gorm:"column:store_state;type:varchar(50)" json:"store_state"`
	WarehouseID            string    `gorm:"column:warehouse_id;type:varchar(50)" json:"warehouse_id"`
	ExpressID              string    `gorm:"column:express_id;type:varchar(50)" json:"express_id"`
	WarehouseState         string    `gorm:"column:warehouse_state;type:varchar(20)" json:"warehouse_state"`
	OrderTime              time.Time `gorm:"column:order_time;type:datetime" json:"order_time"`
	PayTime                time.Time `gorm:"column:pay_time;type:datetime" json:"pay_time"`
	PayWay                 string    `gorm:"column:pay_way;type:varchar(50)" json:"pay_way"`
	OutOfStockReason       string    `gorm:"column:out_of_stock_reason;type:varchar(50)" json:"out_of_stock_reason"`
	ExceptionReason        string    `gorm:"column:exception_reason;type:varchar(20)" json:"exception_reason"`
	PreOrder               bool      `gorm:"column:pre_order;type:tinyint(1)" json:"pre_order"`
	DeliverFirst           bool      `gorm:"column:deliver_first;type:tinyint(1)" json:"deliver_first"`
	Remark                 bool      `gorm:"column:remark;type:tinyint(1)" json:"remark"`
	Hold                   bool      `gorm:"column:hold;type:tinyint(1)" json:"hold"`
	Exception              bool      `gorm:"column:exception;type:tinyint(1)" json:"exception"`
	Oversold               bool      `gorm:"column:oversold;type:tinyint(1)" json:"oversold"`
	UpdateTime             time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
	WarehouseCode          string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"`                                                      // 仓库编码
	CustomAttributes       string    `gorm:"column:custom_attributes;type:varchar(5000);comment:自定义属性" json:"custom_attributes"`                                                      // 自定义属性
	SalesTransactionMaster string    `gorm:"column:sales_transaction_master;type:json;comment:交易链路主体" json:"sales_transaction_master"`                                                // 交易链路主体
	OrderType              int32     `gorm:"column:order_type;type:int;not null;comment:订单类型;0-normal；1-直播订单" json:"order_type"`                                                      // 订单类型;0-normal；1-直播订单
	OrderSampleType        string    `gorm:"column:order_sample_type;type:varchar(50);not null;default:general;comment:订单类型：general-普通订单，sample_order-样品订单" json:"order_sample_type"` // 订单类型：general-普通订单，sample_order-样品订单
}

// TableName OrderBase's table name
func (*OrderBase) TableName() string {
	return TableNameOrderBase
}
