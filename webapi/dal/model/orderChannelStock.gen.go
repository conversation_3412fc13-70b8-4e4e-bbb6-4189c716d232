// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderChannelStock = "order_channel_stock"

// OrderChannelStock mapped from table <order_channel_stock>
type OrderChannelStock struct {
	ID               int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID           int32  `gorm:"column:shop_id;type:int" json:"shop_id"`
	OrderID          string `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Sku              string `gorm:"column:sku;type:varchar(100)" json:"sku"`
	ProductID        string `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	SkuID            string `gorm:"column:sku_id;type:varchar(100)" json:"sku_id"`
	Quantity         int32  `gorm:"column:quantity;type:int" json:"quantity"`
	AlreadyOccupied  int32  `gorm:"column:already_occupied;type:int" json:"already_occupied"`
	OccupationFailed int32  `gorm:"column:occupation_failed;type:int" json:"occupation_failed"`
	Valid            bool   `gorm:"column:valid;type:tinyint(1)" json:"valid"`
}

// TableName OrderChannelStock's table name
func (*OrderChannelStock) TableName() string {
	return TableNameOrderChannelStock
}
