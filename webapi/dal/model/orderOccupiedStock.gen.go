// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderOccupiedStock = "order_occupied_stock"

// OrderOccupiedStock mapped from table <order_occupied_stock>
type OrderOccupiedStock struct {
	ID               int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	OrderID          string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Sku              string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Quantity         int32     `gorm:"column:quantity;type:int" json:"quantity"`
	AlreadyOccupied  int32     `gorm:"column:already_occupied;type:int" json:"already_occupied"`
	OccupationFailed int32     `gorm:"column:occupation_failed;type:int" json:"occupation_failed"`
	CreatedTime      time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	Valid            bool      `gorm:"column:valid;type:tinyint(1);default:1" json:"valid"`
}

// TableName OrderOccupiedStock's table name
func (*OrderOccupiedStock) TableName() string {
	return TableNameOrderOccupiedStock
}
