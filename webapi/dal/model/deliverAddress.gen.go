// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDeliverAddress = "deliver_address"

// DeliverAddress mapped from table <deliver_address>
type DeliverAddress struct {
	ID       int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Company  string `gorm:"column:company;type:varchar(100)" json:"company"`
	Name     string `gorm:"column:name;type:varchar(100)" json:"name"`
	Mobile   string `gorm:"column:mobile;type:varchar(20)" json:"mobile"`
	Province string `gorm:"column:province;type:varchar(100)" json:"province"`
	City     string `gorm:"column:city;type:varchar(100)" json:"city"`
	Area     string `gorm:"column:area;type:varchar(100)" json:"area"`
	Town     string `gorm:"column:town;type:varchar(100)" json:"town"`
	Detail   string `gorm:"column:detail;type:varchar(1000)" json:"detail"`
}

// TableName DeliverAddress's table name
func (*DeliverAddress) TableName() string {
	return TableNameDeliverAddress
}
