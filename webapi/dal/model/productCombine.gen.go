// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProductCombine = "product_combine"

// ProductCombine mapped from table <product_combine>
type ProductCombine struct {
	CombineSku    string    `gorm:"column:combine_sku;type:varchar(100);primaryKey" json:"combine_sku"`
	Name          string    `gorm:"column:name;type:varchar(100)" json:"name"`
	Price         float64   `gorm:"column:price;type:decimal(10,2)" json:"price"`
	CreatedTime   time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
	State         string    `gorm:"column:state;type:varchar(5)" json:"state"`
	Gtin          string    `gorm:"column:gtin;type:varchar(100)" json:"gtin"`
	Weight        int32     `gorm:"column:weight;type:int" json:"weight"`
	Length        int32     `gorm:"column:length;type:int" json:"length"`
	Width         int32     `gorm:"column:width;type:int" json:"width"`
	Height        int32     `gorm:"column:height;type:int" json:"height"`
	WarehouseCode string    `gorm:"column:warehouse_code;type:varchar(32);primaryKey;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName ProductCombine's table name
func (*ProductCombine) TableName() string {
	return TableNameProductCombine
}
