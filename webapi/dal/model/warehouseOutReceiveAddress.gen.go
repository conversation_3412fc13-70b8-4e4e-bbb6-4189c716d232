// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameWarehouseOutReceiveAddress = "warehouse_out_receive_address"

// WarehouseOutReceiveAddress mapped from table <warehouse_out_receive_address>
type WarehouseOutReceiveAddress struct {
	ID       int32  `gorm:"column:id;type:int;primaryKey" json:"id"`
	Company  string `gorm:"column:company;type:varchar(100)" json:"company"`
	Name     string `gorm:"column:name;type:varchar(100)" json:"name"`
	Mobile   string `gorm:"column:mobile;type:varchar(100)" json:"mobile"`
	Province string `gorm:"column:province;type:varchar(100)" json:"province"`
	City     string `gorm:"column:city;type:varchar(100)" json:"city"`
	Area     string `gorm:"column:area;type:varchar(100)" json:"area"`
	Town     string `gorm:"column:town;type:varchar(100)" json:"town"`
	Detail   string `gorm:"column:detail;type:varchar(300)" json:"detail"`
}

// TableName WarehouseOutReceiveAddress's table name
func (*WarehouseOutReceiveAddress) TableName() string {
	return TableNameWarehouseOutReceiveAddress
}
