// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDeliverItem = "deliver_item"

// DeliverItem mapped from table <deliver_item>
type DeliverItem struct {
	ID                     int32   `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	DeliverID              int32   `gorm:"column:deliver_id;type:int" json:"deliver_id"`
	LineNum                int32   `gorm:"column:line_num;type:smallint" json:"line_num"`
	Sku                    string  `gorm:"column:sku;type:varchar(50)" json:"sku"`
	Quantity               int32   `gorm:"column:quantity;type:int" json:"quantity"`
	Price                  float64 `gorm:"column:price;type:decimal(10,2)" json:"price"`
	TaxRate                float32 `gorm:"column:tax_rate;type:float" json:"tax_rate"`
	Amount                 float64 `gorm:"column:amount;type:decimal(10,2)" json:"amount"`
	TaxAmount              float64 `gorm:"column:tax_amount;type:decimal(10,2)" json:"tax_amount"`
	DiscountAmount         float64 `gorm:"column:discount_amount;type:decimal(10,2)" json:"discount_amount"`
	PaidAmount             float64 `gorm:"column:paid_amount;type:decimal(10,2)" json:"paid_amount"`
	Currency               string  `gorm:"column:currency;type:varchar(10)" json:"currency"`
	ProductMark            string  `gorm:"column:product_mark;type:varchar(32);comment:商品标识" json:"product_mark"`                    // 商品标识
	SalesTransactionMaster string  `gorm:"column:sales_transaction_master;type:json;comment:交易链路主体" json:"sales_transaction_master"` // 交易链路主体
	CampaignID             string  `gorm:"column:campaign_id;type:varchar(64);comment:活动ID" json:"campaign_id"`                      // 活动ID
}

// TableName DeliverItem's table name
func (*DeliverItem) TableName() string {
	return TableNameDeliverItem
}
