// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOutboundReason = "outbound_reason"

// OutboundReason 出库原因表（支持软删除）
type OutboundReason struct {
	ID          int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	ReasonName  string    `gorm:"column:reason_name;type:varchar(100);not null;comment:出库原因名称，用于展示，如 样品出库、销售发货" json:"reason_name"`    // 出库原因名称，用于展示，如 样品出库、销售发货
	FlagDeleted bool      `gorm:"column:flag_deleted;type:tinyint(1);comment:删除标记：0-未删除，1-已删除（软删除）" json:"flag_deleted"`               // 删除标记：0-未删除，1-已删除（软删除）
	CreateTime  time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName OutboundReason's table name
func (*OutboundReason) TableName() string {
	return TableNameOutboundReason
}
