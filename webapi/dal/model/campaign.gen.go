// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCampaign = "campaign"

// Campaign mapped from table <campaign>
type Campaign struct {
	ID            int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID        int32     `gorm:"column:shop_id;type:int;not null;default:-(1);comment:店铺ID" json:"shop_id"`                                                             // 店铺ID
	CampaignID    string    `gorm:"column:campaign_id;type:varchar(100);not null;comment:三方活动ID" json:"campaign_id"`                                                       // 三方活动ID
	CampaignName  string    `gorm:"column:campaign_name;type:varchar(255);not null;comment:活动名称" json:"campaign_name"`                                                     // 活动名称
	WarehouseCode string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"`                                                    // 仓库编码
	StartTime     time.Time `gorm:"column:start_time;type:datetime;comment:活动开始时间" json:"start_time"`                                                                      // 活动开始时间
	EndTime       time.Time `gorm:"column:end_time;type:datetime;comment:活动结束时间" json:"end_time"`                                                                          // 活动结束时间
	EnableStatus  string    `gorm:"column:enable_status;type:varchar(16);not null;default:pending;comment:活动生效状态；pending-待生效,enable-生效中,disable-已失效" json:"enable_status"` // 活动生效状态；pending-待生效,enable-生效中,disable-已失效
	CreatedAt     time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                    // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                    // 更新时间
	CreatedBy     int32     `gorm:"column:created_by;type:int;comment:中台用户ID" json:"created_by"`                                                                           // 中台用户ID
	UpdatedBy     int32     `gorm:"column:updated_by;type:int;comment:中台用户ID" json:"updated_by"`                                                                           // 中台用户ID
}

// TableName Campaign's table name
func (*Campaign) TableName() string {
	return TableNameCampaign
}
