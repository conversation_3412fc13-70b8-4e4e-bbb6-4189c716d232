// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRetryThirdPlatformRequest = "retry_third_platform_request"

// RetryThirdPlatformRequest mapped from table <retry_third_platform_request>
type RetryThirdPlatformRequest struct {
	ID              int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderID         string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Type            string    `gorm:"column:type;type:varchar(32)" json:"type"`
	CreatedTime     time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	NextRetryTime   time.Time `gorm:"column:next_retry_time;type:datetime" json:"next_retry_time"`
	FinishTime      time.Time `gorm:"column:finish_time;type:datetime" json:"finish_time"`
	RetryCount      int32     `gorm:"column:retry_count;type:int" json:"retry_count"`
	Finish          bool      `gorm:"column:finish;type:tinyint(1)" json:"finish"`
	CallBackContext string    `gorm:"column:call_back_context;type:text" json:"call_back_context"`
}

// TableName RetryThirdPlatformRequest's table name
func (*RetryThirdPlatformRequest) TableName() string {
	return TableNameRetryThirdPlatformRequest
}
