// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameWarehouse = "warehouse"

// Warehouse 仓库表
type Warehouse struct {
	ID                    int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                         // 主键id
	WarehouseCode         string         `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"`                 // 仓库编码
	Name                  string         `gorm:"column:name;type:varchar(128);not null;comment:仓库名称" json:"name"`                                    // 仓库名称
	ThirdCode             string         `gorm:"column:third_code;type:varchar(54);not null;comment:第三方仓库编码" json:"third_code"`                      // 第三方仓库编码
	ActiveFlag            bool           `gorm:"column:active_flag;type:tinyint(1);not null;comment:是否启用；1-是；0-否" json:"active_flag"`                // 是否启用；1-是；0-否
	CreatedAt             time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy             int64          `gorm:"column:created_by;type:bigint;comment:创建人" json:"created_by"`                                        // 创建人
	UpdatedAt             time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	UpdatedBy             int64          `gorm:"column:updated_by;type:bigint;comment:更新人" json:"updated_by"`                                        // 更新人
	FlagDeleted           int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy             int64          `gorm:"column:deleted_by;type:bigint;comment:删除人" json:"deleted_by"`                                        // 删除人
	DeletedAt             gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
	DefaultFlag           int32          `gorm:"column:default_flag;type:tinyint;not null;comment:是否当前地区默认仓库;1-是；0-否" json:"default_flag"`           // 是否当前地区默认仓库;1-是；0-否
	PhysicalWarehouseCode string         `gorm:"column:physical_warehouse_code;type:varchar(64);comment:实体仓编码" json:"physical_warehouse_code"`       // 实体仓编码
	LogicalWarehouseCode  string         `gorm:"column:logical_warehouse_code;type:varchar(64);comment:逻辑仓编码" json:"logical_warehouse_code"`         // 逻辑仓编码
}

// TableName Warehouse's table name
func (*Warehouse) TableName() string {
	return TableNameWarehouse
}
