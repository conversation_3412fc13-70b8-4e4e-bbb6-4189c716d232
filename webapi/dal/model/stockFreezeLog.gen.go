// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockFreezeLog = "stock_freeze_log"

// StockFreezeLog mapped from table <stock_freeze_log>
type StockFreezeLog struct {
	ID                  int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Sku                 string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Num                 int32     `gorm:"column:num;type:int" json:"num"`
	OrderID             string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	QuantityAfterChange int32     `gorm:"column:quantity_after_change;type:int" json:"quantity_after_change"`
	CreatedTime         time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	Valid               bool      `gorm:"column:valid;type:tinyint(1);default:1" json:"valid"`
	WarehouseCode       string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName StockFreezeLog's table name
func (*StockFreezeLog) TableName() string {
	return TableNameStockFreezeLog
}
