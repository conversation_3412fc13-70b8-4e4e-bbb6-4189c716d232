// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameProductCombineItem = "product_combine_item"

// ProductCombineItem mapped from table <product_combine_item>
type ProductCombineItem struct {
	ID         int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Sku        string `gorm:"column:sku;type:varchar(100)" json:"sku"`
	CombineSku string `gorm:"column:combine_sku;type:varchar(100)" json:"combine_sku"`
	Count      int32  `gorm:"column:count;type:int" json:"count"`
}

// TableName ProductCombineItem's table name
func (*ProductCombineItem) TableName() string {
	return TableNameProductCombineItem
}
