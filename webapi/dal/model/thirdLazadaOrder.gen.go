// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameThirdLazadaOrder = "third_lazada_order"

// ThirdLazadaOrder mapped from table <third_lazada_order>
type ThirdLazadaOrder struct {
	OrderID                     string    `gorm:"column:order_id;type:varchar(50);primaryKey" json:"order_id"`
	SellerID                    string    `gorm:"column:seller_id;type:varchar(50)" json:"seller_id"`
	BranchNumber                string    `gorm:"column:branch_number;type:varchar(20)" json:"branch_number"`
	TaxCode                     string    `gorm:"column:tax_code;type:varchar(20)" json:"tax_code"`
	ExtraAttributes             string    `gorm:"column:extra_attributes;type:varchar(200)" json:"extra_attributes"`
	AddressUpdatedAt            string    `gorm:"column:address_updated_at;type:varchar(50)" json:"address_updated_at"`
	ShippingFee                 string    `gorm:"column:shipping_fee;type:varchar(32)" json:"shipping_fee"`
	CustomerFirstName           string    `gorm:"column:customer_first_name;type:varchar(100)" json:"customer_first_name"`
	PaymentMethod               string    `gorm:"column:payment_method;type:varchar(32)" json:"payment_method"`
	Statuses                    string    `gorm:"column:statuses;type:json" json:"statuses"`
	Remarks                     string    `gorm:"column:remarks;type:varchar(255)" json:"remarks"`
	OrderNumber                 string    `gorm:"column:order_number;type:varchar(50)" json:"order_number"`
	Voucher                     string    `gorm:"column:voucher;type:varchar(32)" json:"voucher"`
	NationalRegistrationNumber  string    `gorm:"column:national_registration_number;type:varchar(20)" json:"national_registration_number"`
	PromisedShippingTimes       string    `gorm:"column:promised_shipping_times;type:varchar(32)" json:"promised_shipping_times"`
	ItemsCount                  int32     `gorm:"column:items_count;type:int" json:"items_count"`
	VoucherPlatform             string    `gorm:"column:voucher_platform;type:varchar(255)" json:"voucher_platform"`
	VoucherSeller               string    `gorm:"column:voucher_seller;type:varchar(255)" json:"voucher_seller"`
	CreatedAt                   time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
	Price                       string    `gorm:"column:price;type:varchar(32)" json:"price"`
	WarehouseCode               string    `gorm:"column:warehouse_code;type:varchar(32)" json:"warehouse_code"`
	ShippingFeeOriginal         string    `gorm:"column:shipping_fee_original;type:varchar(32)" json:"shipping_fee_original"`
	ShippingFeeDiscountSeller   string    `gorm:"column:shipping_fee_discount_seller;type:varchar(32)" json:"shipping_fee_discount_seller"`
	ShippingFeeDiscountPlatform string    `gorm:"column:shipping_fee_discount_platform;type:varchar(32)" json:"shipping_fee_discount_platform"`
	CustomerLastName            string    `gorm:"column:customer_last_name;type:varchar(100)" json:"customer_last_name"`
	GiftOption                  string    `gorm:"column:gift_option;type:varchar(32)" json:"gift_option"`
	VoucherCode                 string    `gorm:"column:voucher_code;type:varchar(32)" json:"voucher_code"`
	UpdatedAt                   time.Time `gorm:"column:updated_at;type:datetime" json:"updated_at"`
	DeliveryInfo                string    `gorm:"column:delivery_info;type:varchar(32)" json:"delivery_info"`
	GiftMessage                 string    `gorm:"column:gift_message;type:varchar(255)" json:"gift_message"`
	BuyerNote                   string    `gorm:"column:buyer_note;type:varchar(255)" json:"buyer_note"`
	AddressBilling              string    `gorm:"column:address_billing;type:json" json:"address_billing"`
	AddressShipping             string    `gorm:"column:address_shipping;type:json" json:"address_shipping"`
}

// TableName ThirdLazadaOrder's table name
func (*ThirdLazadaOrder) TableName() string {
	return TableNameThirdLazadaOrder
}
