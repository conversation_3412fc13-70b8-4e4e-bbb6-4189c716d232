// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderException = "order_exception"

// OrderException mapped from table <order_exception>
type OrderException struct {
	ID  string `gorm:"column:id;type:varchar(100);primaryKey" json:"id"`
	Msg string `gorm:"column:msg;type:varchar(200)" json:"msg"`
}

// TableName OrderException's table name
func (*OrderException) TableName() string {
	return TableNameOrderException
}
