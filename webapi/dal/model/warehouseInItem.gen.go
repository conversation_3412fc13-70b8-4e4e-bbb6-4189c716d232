// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameWarehouseInItem = "warehouse_in_item"

// WarehouseInItem mapped from table <warehouse_in_item>
type WarehouseInItem struct {
	ID                     int32   `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	ReceiptID              int32   `gorm:"column:receipt_id;type:int" json:"receipt_id"`
	Sku                    string  `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Name                   string  `gorm:"column:name;type:varchar(100)" json:"name"`
	Price                  float64 `gorm:"column:price;type:decimal(10,2)" json:"price"`
	Currency               string  `gorm:"column:currency;type:varchar(10)" json:"currency"`
	Quantity               int32   `gorm:"column:quantity;type:int" json:"quantity"`
	SalesTransactionMaster string  `gorm:"column:sales_transaction_master;type:json;comment:交易链路主体" json:"sales_transaction_master"` // 交易链路主体
}

// TableName WarehouseInItem's table name
func (*WarehouseInItem) TableName() string {
	return TableNameWarehouseInItem
}
