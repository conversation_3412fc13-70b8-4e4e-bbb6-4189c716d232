// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSkuRelation = "sku_relation"

// SkuRelation mapped from table <sku_relation>
type SkuRelation struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID      int32     `gorm:"column:shop_id;type:int" json:"shop_id"`
	ProductID   string    `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	SkuID       string    `gorm:"column:sku_id;type:varchar(100)" json:"sku_id"`
	Sku         string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	CreatedTime time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
}

// TableName SkuRelation's table name
func (*SkuRelation) TableName() string {
	return TableNameSkuRelation
}
