// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameSampleStock = "sample_stock"

// SampleStock 样品仓库存表
type SampleStock struct {
	ID               int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:库存ID,主键" json:"id"`                                                  // 库存ID,主键
	SkuNo            string         `gorm:"column:sku_no;type:varchar(50);not null;comment:sku 编码" json:"sku_no"`                                                           // sku 编码
	Barcode          string         `gorm:"column:barcode;type:varchar(64);not null;comment:条形码" json:"barcode"`                                                            // 条形码
	SkuName          string         `gorm:"column:sku_name;type:varchar(255);comment:sku 名称" json:"sku_name"`                                                               // sku 名称
	LocationCode     string         `gorm:"column:location_code;type:varchar(64);comment:库位编码" json:"location_code"`                                                        // 库位编码
	Quantity         int32          `gorm:"column:quantity;type:int;not null;comment:样品库存数量" json:"quantity"`                                                               // 样品库存数量
	ReservedQuantity int32          `gorm:"column:reserved_quantity;type:int;not null;comment:预占库存" json:"reserved_quantity"`                                               // 预占库存
	ChannelPlatform  string         `gorm:"column:channel_platform;type:varchar(16);not null;default:TIKTOK;comment:渠道平台（预留字段，后续可能还会接入shopee直播间等）" json:"channel_platform"` // 渠道平台（预留字段，后续可能还会接入shopee直播间等）
	CreatedAt        time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                             // 创建时间
	CreatedBy        int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                                                       // 创建人
	UpdatedAt        time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                                                   // 更新时间，更新时自动更新为当前时间
	UpdatedBy        int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                                                       // 更新人
	FlagDeleted      int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`                                     // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy        int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                                                       // 删除人
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                                                // 删除时间
	ActualQuantity   int32          `gorm:"column:actual_quantity;type:int;not null;comment:实际库存数量" json:"actual_quantity"`                                                 // 实际库存数量
	SampleDisplay    string         `gorm:"column:sample_display;type:varchar(500);not null;comment:样品图" json:"sample_display"`                                             // 样品图
}

// TableName SampleStock's table name
func (*SampleStock) TableName() string {
	return TableNameSampleStock
}
