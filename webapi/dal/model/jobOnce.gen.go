// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameJobOnce = "job_once"

// JobOnce mapped from table <job_once>
type JobOnce struct {
	ID         int32     `gorm:"column:id;type:int;primaryKey" json:"id"`
	Name       string    `gorm:"column:name;type:varchar(50)" json:"name"`
	ClassName  string    `gorm:"column:class_name;type:varchar(100)" json:"class_name"`
	Params     string    `gorm:"column:params;type:varchar(500)" json:"params"`
	State      string    `gorm:"column:state;type:varchar(11)" json:"state"`
	UpdateTime time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName JobOnce's table name
func (*JobOnce) TableName() string {
	return TableNameJobOnce
}
