// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameFreebieRule = "freebie_rule"

// FreebieRule 赠品规则表
type FreebieRule struct {
	ID          int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:规则ID，主键" json:"id"`                      // 规则ID，主键
	FreebieID   int64          `gorm:"column:freebie_id;type:bigint;not null;comment:赠品id" json:"freebie_id"`                              // 赠品id
	RuleName    string         `gorm:"column:rule_name;type:varchar(128);not null;comment:规则名称" json:"rule_name"`                          // 规则名称
	RuleType    string         `gorm:"column:rule_type;type:varchar(50);not null;comment:规则类型；fixedAmount满赠" json:"rule_type"`             // 规则类型；fixedAmount满赠
	Rule        string         `gorm:"column:rule;type:json;not null;comment:规则详情，JSON存储具体规则" json:"rule"`                                 // 规则详情，JSON存储具体规则
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy   int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	UpdatedBy   int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                           // 更新人
	FlagDeleted int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy   int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                           // 删除人
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
}

// TableName FreebieRule's table name
func (*FreebieRule) TableName() string {
	return TableNameFreebieRule
}
