// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdTiktokOrder = "third_tiktok_order"

// ThirdTiktokOrder mapped from table <third_tiktok_order>
type ThirdTiktokOrder struct {
	ID                                 string `gorm:"column:id;type:varchar(255);primaryKey" json:"id"`
	Status                             string `gorm:"column:status;type:varchar(255)" json:"status"`
	ShippingProvider                   string `gorm:"column:shipping_provider;type:varchar(255)" json:"shipping_provider"`
	ShippingProviderID                 string `gorm:"column:shipping_provider_id;type:varchar(255)" json:"shipping_provider_id"`
	CreateTime                         int32  `gorm:"column:create_time;type:int" json:"create_time"`
	PaidTime                           int32  `gorm:"column:paid_time;type:int" json:"paid_time"`
	BuyerMessage                       string `gorm:"column:buyer_message;type:varchar(255)" json:"buyer_message"`
	CancelReason                       string `gorm:"column:cancel_reason;type:varchar(255)" json:"cancel_reason"`
	CancellationInitiator              string `gorm:"column:cancellation_initiator;type:varchar(255)" json:"cancellation_initiator"`
	TrackingNumber                     string `gorm:"column:tracking_number;type:varchar(255)" json:"tracking_number"`
	RtsTime                            int32  `gorm:"column:rts_time;type:int" json:"rts_time"`
	RtsSLATime                         int32  `gorm:"column:rts_sla_time;type:int" json:"rts_sla_time"`
	TtsSLATime                         int32  `gorm:"column:tts_sla_time;type:int" json:"tts_sla_time"`
	CancelOrderSLATime                 int32  `gorm:"column:cancel_order_sla_time;type:int" json:"cancel_order_sla_time"`
	UpdateTime                         int32  `gorm:"column:update_time;type:int" json:"update_time"`
	HasUpdatedRecipientAddress         bool   `gorm:"column:has_updated_recipient_address;type:tinyint(1)" json:"has_updated_recipient_address"`
	IsCod                              bool   `gorm:"column:is_cod;type:tinyint(1)" json:"is_cod"`
	IsSampleOrder                      bool   `gorm:"column:is_sample_order;type:tinyint(1)" json:"is_sample_order"`
	NeedUploadInvoice                  string `gorm:"column:need_upload_invoice;type:varchar(255)" json:"need_upload_invoice"`
	BuyerEmail                         string `gorm:"column:buyer_email;type:varchar(255)" json:"buyer_email"`
	Cpf                                string `gorm:"column:cpf;type:varchar(255)" json:"cpf"`
	IsOnHoldOrder                      bool   `gorm:"column:is_on_hold_order;type:tinyint(1)" json:"is_on_hold_order"`
	IsBuyerRequestCancel               bool   `gorm:"column:is_buyer_request_cancel;type:tinyint(1)" json:"is_buyer_request_cancel"`
	RequestCancelTime                  int32  `gorm:"column:request_cancel_time;type:int" json:"request_cancel_time"`
	DeliveryOptionRequiredDeliveryTime int32  `gorm:"column:delivery_option_required_delivery_time;type:int" json:"delivery_option_required_delivery_time"`
	ShippingDueTime                    int32  `gorm:"column:shipping_due_time;type:int" json:"shipping_due_time"`
	CollectionDueTime                  int32  `gorm:"column:collection_due_time;type:int" json:"collection_due_time"`
	DeliveryDueTime                    int32  `gorm:"column:delivery_due_time;type:int" json:"delivery_due_time"`
	CollectionTime                     int32  `gorm:"column:collection_time;type:int" json:"collection_time"`
	DeliveryTime                       int32  `gorm:"column:delivery_time;type:int" json:"delivery_time"`
	CancelTime                         int32  `gorm:"column:cancel_time;type:int" json:"cancel_time"`
	UserID                             string `gorm:"column:user_id;type:varchar(255)" json:"user_id"`
	SplitOrCombineTag                  string `gorm:"column:split_or_combine_tag;type:varchar(255)" json:"split_or_combine_tag"`
	FulfillmentType                    string `gorm:"column:fulfillment_type;type:varchar(255)" json:"fulfillment_type"`
	SellerNote                         string `gorm:"column:seller_note;type:varchar(255)" json:"seller_note"`
	WarehouseID                        string `gorm:"column:warehouse_id;type:varchar(255)" json:"warehouse_id"`
	PaymentMethodName                  string `gorm:"column:payment_method_name;type:varchar(255)" json:"payment_method_name"`
	ShippingType                       string `gorm:"column:shipping_type;type:varchar(255)" json:"shipping_type"`
	DeliveryOptionName                 string `gorm:"column:delivery_option_name;type:varchar(255)" json:"delivery_option_name"`
	DeliveryOptionID                   string `gorm:"column:delivery_option_id;type:varchar(255)" json:"delivery_option_id"`
	DeliverySLATime                    int32  `gorm:"column:delivery_sla_time;type:int" json:"delivery_sla_time"`
	LineItems                          string `gorm:"column:line_items;type:json" json:"line_items"`
	Packages                           string `gorm:"column:packages;type:json" json:"packages"`
	Payment                            string `gorm:"column:payment;type:json" json:"payment"`
	RecipientAddress                   string `gorm:"column:recipient_address;type:json" json:"recipient_address"`
}

// TableName ThirdTiktokOrder's table name
func (*ThirdTiktokOrder) TableName() string {
	return TableNameThirdTiktokOrder
}
