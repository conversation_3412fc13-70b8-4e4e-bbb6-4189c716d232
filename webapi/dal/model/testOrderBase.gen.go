// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTestOrderBase = "test_order_base"

// TestOrderBase mapped from table <test_order_base>
type TestOrderBase struct {
	ID               string    `gorm:"column:id;type:varchar(255);primaryKey" json:"id"`
	ChannelOrderID   string    `gorm:"column:channel_order_id;type:varchar(255);not null" json:"channel_order_id"`
	State            string    `gorm:"column:state;type:varchar(32)" json:"state"`
	DisplayState     string    `gorm:"column:display_state;type:varchar(32)" json:"display_state"`
	StorePlatform    string    `gorm:"column:store_platform;type:varchar(255)" json:"store_platform"`
	ShopID           int32     `gorm:"column:shop_id;type:smallint" json:"shop_id"`
	StoreState       string    `gorm:"column:store_state;type:varchar(255)" json:"store_state"`
	WarehouseID      string    `gorm:"column:warehouse_id;type:varchar(255)" json:"warehouse_id"`
	ExpressID        string    `gorm:"column:express_id;type:varchar(255)" json:"express_id"`
	WarehouseState   string    `gorm:"column:warehouse_state;type:varchar(255)" json:"warehouse_state"`
	OrderTime        time.Time `gorm:"column:order_time;type:datetime" json:"order_time"`
	PayTime          time.Time `gorm:"column:pay_time;type:datetime" json:"pay_time"`
	PayWay           string    `gorm:"column:pay_way;type:varchar(50)" json:"pay_way"`
	OutOfStockReason string    `gorm:"column:out_of_stock_reason;type:varchar(50)" json:"out_of_stock_reason"`
	PreOrder         bool      `gorm:"column:pre_order;type:tinyint(1)" json:"pre_order"`
	DeliverFirst     bool      `gorm:"column:deliver_first;type:tinyint(1)" json:"deliver_first"`
	Remark           bool      `gorm:"column:remark;type:tinyint(1)" json:"remark"`
	Hold             bool      `gorm:"column:hold;type:tinyint(1)" json:"hold"`
	Exception        bool      `gorm:"column:exception;type:tinyint(1)" json:"exception"`
	Oversold         bool      `gorm:"column:oversold;type:tinyint(1)" json:"oversold"`
	Test008          int32     `gorm:"column:test008;type:int" json:"test008"`
	UpdateTime       time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName TestOrderBase's table name
func (*TestOrderBase) TableName() string {
	return TableNameTestOrderBase
}
