// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderCancel = "order_cancel"

// OrderCancel mapped from table <order_cancel>
type OrderCancel struct {
	ID            int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderID       string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	ThirdCancelID string    `gorm:"column:third_cancel_id;type:varchar(100)" json:"third_cancel_id"`
	Sponsor       string    `gorm:"column:sponsor;type:varchar(8)" json:"sponsor"`
	State         string    `gorm:"column:state;type:varchar(10)" json:"state"`
	CreatedTime   time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName OrderCancel's table name
func (*OrderCancel) TableName() string {
	return TableNameOrderCancel
}
