// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWarehouseOut = "warehouse_out"

// WarehouseOut mapped from table <warehouse_out>
type WarehouseOut struct {
	ID             int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderNo        string    `gorm:"column:order_no;type:varchar(100);not null" json:"order_no"`
	SourceNo       string    `gorm:"column:source_no;type:varchar(100)" json:"source_no"`
	Type           string    `gorm:"column:type;type:varchar(20)" json:"type"`
	CreatedTime    time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	SyncTime       time.Time `gorm:"column:sync_time;type:datetime" json:"sync_time"`
	State          string    `gorm:"column:state;type:varchar(20)" json:"state"`
	CreatedUser    string    `gorm:"column:created_user;type:varchar(50)" json:"created_user"`
	Remark         string    `gorm:"column:remark;type:varchar(500)" json:"remark"`
	PdfURL         string    `gorm:"column:pdf_url;type:varchar(200)" json:"pdf_url"`
	ExpressNo      string    `gorm:"column:express_no;type:varchar(50)" json:"express_no"`
	LogisticsCode  string    `gorm:"column:logistics_code;type:varchar(100)" json:"logistics_code"`
	Reason         string    `gorm:"column:reason;type:varchar(300)" json:"reason"`
	WarehouseState string    `gorm:"column:warehouse_state;type:varchar(20)" json:"warehouse_state"`
	WarehouseCode  string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName WarehouseOut's table name
func (*WarehouseOut) TableName() string {
	return TableNameWarehouseOut
}
