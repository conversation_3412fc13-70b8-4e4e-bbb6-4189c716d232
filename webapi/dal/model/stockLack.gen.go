// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockLack = "stock_lack"

// StockLack mapped from table <stock_lack>
type StockLack struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID      int32     `gorm:"column:shop_id;type:int" json:"shop_id"`
	OrderID     string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Sku         string    `gorm:"column:sku;type:varchar(200)" json:"sku"`
	Num         int32     `gorm:"column:num;type:int" json:"num"`
	CreatedTime time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	UpdateTime  time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName StockLack's table name
func (*StockLack) TableName() string {
	return TableNameStockLack
}
