// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderItem = "order_item"

// OrderItem mapped from table <order_item>
type OrderItem struct {
	ID             int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderID        string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	Image          string    `gorm:"column:image;type:varchar(500)" json:"image"`
	Sku            string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	ProductID      string    `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	SkuID          string    `gorm:"column:sku_id;type:varchar(100)" json:"sku_id"`
	Property       string    `gorm:"column:property;type:varchar(100)" json:"property"`
	Quantity       int32     `gorm:"column:quantity;type:int" json:"quantity"`
	Price          float64   `gorm:"column:price;type:decimal(10,2)" json:"price"`
	Amount         float64   `gorm:"column:amount;type:decimal(20,2)" json:"amount"`
	TaxRate        float32   `gorm:"column:tax_rate;type:float" json:"tax_rate"`
	TaxAmount      float64   `gorm:"column:tax_amount;type:decimal(10,2)" json:"tax_amount"`
	DiscountAmount float64   `gorm:"column:discount_amount;type:decimal(10,2)" json:"discount_amount"`
	PaidAmount     float64   `gorm:"column:paid_amount;type:decimal(20,2)" json:"paid_amount"`
	Currency       string    `gorm:"column:currency;type:varchar(10)" json:"currency"`
	Type           string    `gorm:"column:type;type:varchar(7)" json:"type"`
	CreatedTime    time.Time `gorm:"column:created_time;type:datetime;default:CURRENT_TIMESTAMP" json:"created_time"`
	ProductMark    string    `gorm:"column:product_mark;type:varchar(32);comment:商品标识" json:"product_mark"` // 商品标识
	CampaignID     string    `gorm:"column:campaign_id;type:varchar(64);comment:活动ID" json:"campaign_id"`   // 活动ID
}

// TableName OrderItem's table name
func (*OrderItem) TableName() string {
	return TableNameOrderItem
}
