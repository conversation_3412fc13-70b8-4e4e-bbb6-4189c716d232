// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSampleBorrowRequest = "sample_borrow_request"

// SampleBorrowRequest mapped from table <sample_borrow_request>
type SampleBorrowRequest struct {
	ID               int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	BorrowNo         string    `gorm:"column:borrow_no;type:varchar(50);not null;comment:借样单据号（唯一）" json:"borrow_no"`                               // 借样单据号（唯一）
	Status           int32     `gorm:"column:status;type:int;not null;comment:单据状态 (0, "待审核") (1, "待管理员确认"), (2, "已借出"), (3, "已作废")" json:"status"` // 单据状态 (0, "待审核") (1, "待管理员确认"), (2, "已借出"), (3, "已作废")
	CreatorEmail     string    `gorm:"column:creator_email;type:varchar(100);not null;comment:创建人邮箱" json:"creator_email"`                          // 创建人邮箱
	CreateTime       time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`         // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`         // 更新时间
	ThirdPartyStatus int32     `gorm:"column:third_party_status;type:tinyint;not null;comment:1-进行中 2-已通过 3-未通过 4-已取消" json:"third_party_status"`   // 1-进行中 2-已通过 3-未通过 4-已取消
}

// TableName SampleBorrowRequest's table name
func (*SampleBorrowRequest) TableName() string {
	return TableNameSampleBorrowRequest
}
