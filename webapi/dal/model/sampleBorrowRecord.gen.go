// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameSampleBorrowRecord = "sample_borrow_record"

// SampleBorrowRecord 样品借出记录表
type SampleBorrowRecord struct {
	ID            int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:借出记录ID，主键" json:"id"`                    // 借出记录ID，主键
	SampleStockID int64          `gorm:"column:sample_stock_id;type:bigint;not null;comment:样品库存id" json:"sample_stock_id"`                  // 样品库存id
	Borrower      string         `gorm:"column:borrower;type:varchar(64);not null;comment:借出人" json:"borrower"`                              // 借出人
	Quantity      int32          `gorm:"column:quantity;type:int;not null;comment:汇总样品库存借出数量。由于是汇总对冲逻辑，可能会存在负数，即归还数量>借出数量" json:"quantity"`  // 汇总样品库存借出数量。由于是汇总对冲逻辑，可能会存在负数，即归还数量>借出数量
	CreatedAt     time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy     int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdatedAt     time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	UpdatedBy     int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                           // 更新人
	FlagDeleted   int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy     int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                           // 删除人
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
}

// TableName SampleBorrowRecord's table name
func (*SampleBorrowRecord) TableName() string {
	return TableNameSampleBorrowRecord
}
