// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRmaRequestItem = "rma_request_item"

// RmaRequestItem RMA申请明细表
type RmaRequestItem struct {
	ID        int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`               // 主键ID
	RmaNo     string    `gorm:"column:rma_no;type:varchar(100);not null;comment:RMA编号" json:"rma_no"`                     // RMA编号
	Sku       string    `gorm:"column:sku;type:varchar(100);not null;comment:商品SKU" json:"sku"`                           // 商品SKU
	Quantity  int32     `gorm:"column:quantity;type:int;not null;comment:退货数量" json:"quantity"`                           // 退货数量
	CreatedBy string    `gorm:"column:created_by;type:varchar(50);comment:创建人" json:"created_by"`                         // 创建人
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy string    `gorm:"column:updated_by;type:varchar(50);comment:更新人" json:"updated_by"`                         // 更新人
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName RmaRequestItem's table name
func (*RmaRequestItem) TableName() string {
	return TableNameRmaRequestItem
}
