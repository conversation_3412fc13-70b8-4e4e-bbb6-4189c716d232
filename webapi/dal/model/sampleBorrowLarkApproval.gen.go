// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSampleBorrowLarkApproval = "sample_borrow_lark_approval"

// SampleBorrowLarkApproval mapped from table <sample_borrow_lark_approval>
type SampleBorrowLarkApproval struct {
	ID               int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	RequestID        int64     `gorm:"column:request_id;type:bigint;not null;comment:借样请求ID" json:"request_id"`                 // 借样请求ID
	InstanceID       string    `gorm:"column:instance_id;type:varchar(255);not null;comment:飞书审批实例 ID" json:"instance_id"`      // 飞书审批实例 ID
	ApplyUserMail    string    `gorm:"column:apply_user_mail;type:varchar(100);not null;comment:申请人" json:"apply_user_mail"`    // 申请人
	ApprovalStatus   string    `gorm:"column:approval_status;type:varchar(128);not null;comment:飞书审批状态" json:"approval_status"` // 飞书审批状态
	ApprovalURL      string    `gorm:"column:approval_url;type:varchar(200);not null" json:"approval_url"`
	FlagSyncShopping bool      `gorm:"column:flag_sync_shopping;type:tinyint(1);not null;comment:审批结果是否已通知 shopping 服务" json:"flag_sync_shopping"` // 审批结果是否已通知 shopping 服务
	FlagDeprecated   bool      `gorm:"column:flag_deprecated;type:tinyint(1);not null;comment:是否作废，终止审批" json:"flag_deprecated"`                   // 是否作废，终止审批
	CreatedAt        time.Time `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;type:datetime;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName SampleBorrowLarkApproval's table name
func (*SampleBorrowLarkApproval) TableName() string {
	return TableNameSampleBorrowLarkApproval
}
