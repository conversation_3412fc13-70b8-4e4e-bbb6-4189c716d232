// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameStockStrategyShop = "stock_strategy_shop"

// StockStrategyShop mapped from table <stock_strategy_shop>
type StockStrategyShop struct {
	ID         int32 `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	StrategyID int32 `gorm:"column:strategy_id;type:int" json:"strategy_id"`
	ShopID     int32 `gorm:"column:shop_id;type:int" json:"shop_id"`
}

// TableName StockStrategyShop's table name
func (*StockStrategyShop) TableName() string {
	return TableNameStockStrategyShop
}
