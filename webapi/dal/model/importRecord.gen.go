// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameImportRecord = "import_record"

// ImportRecord mapped from table <import_record>
type ImportRecord struct {
	ID             int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                           // 主键
	BatchNo        string    `gorm:"column:batch_no;type:varchar(20);comment:批次号（用于异步请求拉取结果）" json:"batch_no"`                           // 批次号（用于异步请求拉取结果）
	FileName       string    `gorm:"column:file_name;type:varchar(255);comment:导入模板名称" json:"file_name"`                                 // 导入模板名称
	TotalCount     int32     `gorm:"column:total_count;type:int;comment:总数" json:"total_count"`                                          // 总数
	Remark         string    `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`                                           // 备注
	SuccessCount   int32     `gorm:"column:success_count;type:int;comment:导入成功数" json:"success_count"`                                   // 导入成功数
	FailureCount   int32     `gorm:"column:failure_count;type:int;comment:导入失败数" json:"failure_count"`                                   // 导入失败数
	SourceURL      string    `gorm:"column:source_url;type:varchar(500);comment:源文件url" json:"source_url"`                               // 源文件url
	FailureFileURL string    `gorm:"column:failure_file_url;type:varchar(500);comment:失败文件url" json:"failure_file_url"`                  // 失败文件url
	FailReason     string    `gorm:"column:fail_reason;type:varchar(500);comment:失败原因" json:"fail_reason"`                               // 失败原因
	ImportStatus   int32     `gorm:"column:import_status;type:tinyint;comment:导入状态（0-失败，1-成功,，2-进行中）" json:"import_status"`              // 导入状态（0-失败，1-成功,，2-进行中）
	TaskType       int32     `gorm:"column:task_type;type:int;not null;comment:导入类型；1-手工单导入；2-样品入库导入；3-样品盘库导入" json:"task_type"`         // 导入类型；1-手工单导入；2-样品入库导入；3-样品盘库导入
	CreatedName    string    `gorm:"column:created_name;type:varchar(128);comment:创建人用户名" json:"created_name"`                           // 创建人用户名
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy      int32     `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdateAt       time.Time `gorm:"column:update_at;type:datetime;comment:更新时间" json:"update_at"`                                       // 更新时间
	UpdateBy       int32     `gorm:"column:update_by;type:int;comment:更新人" json:"update_by"`                                             // 更新人
}

// TableName ImportRecord's table name
func (*ImportRecord) TableName() string {
	return TableNameImportRecord
}
