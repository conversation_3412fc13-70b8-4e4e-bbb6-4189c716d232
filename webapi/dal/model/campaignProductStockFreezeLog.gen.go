// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCampaignProductStockFreezeLog = "campaign_product_stock_freeze_log"

// CampaignProductStockFreezeLog mapped from table <campaign_product_stock_freeze_log>
type CampaignProductStockFreezeLog struct {
	ID                      int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CampaignInsideID        int32     `gorm:"column:campaign_inside_id;type:int;not null;default:-1;comment:campaign表的id" json:"campaign_inside_id"`                            // campaign表的id
	CampaignProductStockID  int32     `gorm:"column:campaign_product_stock_id;type:int;not null;default:-1;comment:campaign_product_stock表id" json:"campaign_product_stock_id"` // campaign_product_stock表id
	ShopID                  int32     `gorm:"column:shop_id;type:int;not null;default:-(1);comment:店铺ID" json:"shop_id"`                                                        // 店铺ID
	Sku                     string    `gorm:"column:sku;type:varchar(100);not null;comment:活动商品" json:"sku"`                                                                    // 活动商品
	Adjustment              int32     `gorm:"column:adjustment;type:int;not null;comment:调整数量" json:"adjustment"`                                                               // 调整数量
	QuantityAfterAdjustment int32     `gorm:"column:quantity_after_adjustment;type:int;not null;comment:调整后的数量" json:"quantity_after_adjustment"`                               // 调整后的数量
	OrderID                 string    `gorm:"column:order_id;type:varchar(100);not null;comment:订单号" json:"order_id"`                                                           // 订单号
	OperateType             string    `gorm:"column:operate_type;type:varchar(20);not null;comment:操作类型" json:"operate_type"`                                                   // 操作类型
	OperateBy               string    `gorm:"column:operate_by;type:varchar(30);not null;comment:操作人" json:"operate_by"`                                                        // 操作人
	CreatedAt               time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                               // 创建时间
	CreatedBy               int32     `gorm:"column:created_by;type:int;comment:创建人ID" json:"created_by"`                                                                       // 创建人ID
	UpdatedAt               time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                               // 更新时间
	UpdatedBy               int32     `gorm:"column:updated_by;type:int;comment:更新人ID" json:"updated_by"`                                                                       // 更新人ID
	WarehouseCode           string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"`                                               // 仓库编码
}

// TableName CampaignProductStockFreezeLog's table name
func (*CampaignProductStockFreezeLog) TableName() string {
	return TableNameCampaignProductStockFreezeLog
}
