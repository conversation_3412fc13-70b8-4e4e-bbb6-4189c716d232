// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderRemark = "order_remark"

// OrderRemark mapped from table <order_remark>
type OrderRemark struct {
	ID            string `gorm:"column:id;type:varchar(100);primaryKey" json:"id"`
	Remark        string `gorm:"column:remark;type:varchar(500)" json:"remark"`
	PickingRemark string `gorm:"column:picking_remark;type:varchar(500)" json:"picking_remark"`
}

// TableName OrderRemark's table name
func (*OrderRemark) TableName() string {
	return TableNameOrderRemark
}
