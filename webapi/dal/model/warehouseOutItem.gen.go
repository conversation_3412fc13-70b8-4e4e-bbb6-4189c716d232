// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameWarehouseOutItem = "warehouse_out_item"

// WarehouseOutItem mapped from table <warehouse_out_item>
type WarehouseOutItem struct {
	ID                     int32   `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	ReceiptID              int32   `gorm:"column:receipt_id;type:int" json:"receipt_id"`
	Sku                    string  `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Name                   string  `gorm:"column:name;type:varchar(100)" json:"name"`
	Price                  float64 `gorm:"column:price;type:decimal(10,2)" json:"price"`
	Currency               string  `gorm:"column:currency;type:varchar(10)" json:"currency"`
	Quantity               int32   `gorm:"column:quantity;type:int" json:"quantity"`
	SalesTransactionMaster string  `gorm:"column:sales_transaction_master;type:json;comment:交易链路主体" json:"sales_transaction_master"` // 交易链路主体
}

// TableName WarehouseOutItem's table name
func (*WarehouseOutItem) TableName() string {
	return TableNameWarehouseOutItem
}
