// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSearchTermsConfig = "search_terms_config"

// SearchTermsConfig mapped from table <search_terms_config>
type SearchTermsConfig struct {
	Type    string `gorm:"column:type;type:varchar(20);primaryKey" json:"type"`
	TermsID string `gorm:"column:terms_id;type:varchar(50);primaryKey" json:"terms_id"`
	Name    string `gorm:"column:name;type:varchar(50)" json:"name"`
}

// TableName SearchTermsConfig's table name
func (*SearchTermsConfig) TableName() string {
	return TableNameSearchTermsConfig
}
