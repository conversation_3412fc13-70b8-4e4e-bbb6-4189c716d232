// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOrderReturned = "order_returned"

// OrderReturned mapped from table <order_returned>
type OrderReturned struct {
	ID          int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	OrderID     string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	State       string    `gorm:"column:state;type:varchar(10)" json:"state"`
	CreatedTime time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
}

// TableName OrderReturned's table name
func (*OrderReturned) TableName() string {
	return TableNameOrderReturned
}
