// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameFrontendCategory = "frontend_category"

// FrontendCategory 前台类目表
type FrontendCategory struct {
	ID                  int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 ID" json:"id"`               // 主键 ID
	ProjectCode         string    `gorm:"column:project_code;type:varchar(32);not null;comment:项目编码" json:"project_code"`                     // 项目编码
	CategoryCode        string    `gorm:"column:category_code;type:varchar(32);not null;comment:前台类目编码" json:"category_code"`                 // 前台类目编码
	CategoryName        string    `gorm:"column:category_name;type:varchar(64);not null;comment:前台类目名称" json:"category_name"`                 // 前台类目名称
	BackendCategoryCode string    `gorm:"column:backend_category_code;type:varchar(32);not null;comment:后台类目编码" json:"backend_category_code"` // 后台类目编码
	ParentCode          string    `gorm:"column:parent_code;type:varchar(32);not null;comment:父类目编码" json:"parent_code"`                      // 父类目编码
	Level               int32     `gorm:"column:level;type:int;not null;default:1;comment:类目层级(1、2、3、4)" json:"level"`                        // 类目层级(1、2、3、4)
	LevelPath           string    `gorm:"column:level_path;type:varchar(256);not null;comment:类目层级路径 (如: /A/B/C)" json:"level_path"`          // 类目层级路径 (如: /A/B/C)
	Description         string    `gorm:"column:description;type:varchar(128);not null;comment:类目描述" json:"description"`                      // 类目描述
	CreateBy            string    `gorm:"column:create_by;type:varchar(64);not null;comment:创建人 ID" json:"create_by"`                         // 创建人 ID
	UpdateBy            string    `gorm:"column:update_by;type:varchar(64);not null;comment:更改人 ID" json:"update_by"`                         // 更改人 ID
	CreateAt            time.Time `gorm:"column:create_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_at"`   // 创建时间
	UpdateAt            time.Time `gorm:"column:update_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_at"`   // 更新时间
}

// TableName FrontendCategory's table name
func (*FrontendCategory) TableName() string {
	return TableNameFrontendCategory
}
