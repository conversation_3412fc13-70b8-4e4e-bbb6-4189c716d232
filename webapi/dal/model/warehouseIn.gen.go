// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWarehouseIn = "warehouse_in"

// WarehouseIn mapped from table <warehouse_in>
type WarehouseIn struct {
	ID            int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	SourceNo      string    `gorm:"column:source_no;type:varchar(100)" json:"source_no"`
	OrderNo       string    `gorm:"column:order_no;type:varchar(100)" json:"order_no"`
	Type          string    `gorm:"column:type;type:varchar(20)" json:"type"`
	CreatedTime   time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	SyncTime      time.Time `gorm:"column:sync_time;type:datetime" json:"sync_time"`
	State         string    `gorm:"column:state;type:varchar(20)" json:"state"`
	CreatedUser   string    `gorm:"column:created_user;type:varchar(20)" json:"created_user"`
	Remark        string    `gorm:"column:remark;type:varchar(500)" json:"remark"`
	WarehouseCode string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName WarehouseIn's table name
func (*WarehouseIn) TableName() string {
	return TableNameWarehouseIn
}
