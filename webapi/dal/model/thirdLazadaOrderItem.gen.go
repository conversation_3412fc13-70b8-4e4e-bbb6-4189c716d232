// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameThirdLazadaOrderItem = "third_lazada_order_item"

// ThirdLazadaOrderItem mapped from table <third_lazada_order_item>
type ThirdLazadaOrderItem struct {
	OrderItemID                 int64     `gorm:"column:order_item_id;type:bigint;primaryKey" json:"order_item_id"`
	VoucherSeller               string    `gorm:"column:voucher_seller;type:varchar(255)" json:"voucher_seller"`
	OrderType                   string    `gorm:"column:order_type;type:varchar(255)" json:"order_type"`
	StagePayStatus              string    `gorm:"column:stage_pay_status;type:varchar(255)" json:"stage_pay_status"`
	WarehouseCode               string    `gorm:"column:warehouse_code;type:varchar(255)" json:"warehouse_code"`
	VoucherSellerLpi            string    `gorm:"column:voucher_seller_lpi;type:varchar(255)" json:"voucher_seller_lpi"`
	VoucherPlatformLpi          string    `gorm:"column:voucher_platform_lpi;type:varchar(255)" json:"voucher_platform_lpi"`
	BuyerID                     string    `gorm:"column:buyer_id;type:varchar(255)" json:"buyer_id"`
	ShippingFeeOriginal         string    `gorm:"column:shipping_fee_original;type:varchar(255)" json:"shipping_fee_original"`
	ShippingFeeDiscountSeller   string    `gorm:"column:shipping_fee_discount_seller;type:varchar(255)" json:"shipping_fee_discount_seller"`
	ShippingFeeDiscountPlatform string    `gorm:"column:shipping_fee_discount_platform;type:varchar(255)" json:"shipping_fee_discount_platform"`
	VoucherCodeSeller           string    `gorm:"column:voucher_code_seller;type:varchar(255)" json:"voucher_code_seller"`
	VoucherCodePlatform         string    `gorm:"column:voucher_code_platform;type:varchar(255)" json:"voucher_code_platform"`
	DeliveryOptionSof           string    `gorm:"column:delivery_option_sof;type:varchar(255)" json:"delivery_option_sof"`
	IsFbl                       string    `gorm:"column:is_fbl;type:varchar(255)" json:"is_fbl"`
	IsReroute                   string    `gorm:"column:is_reroute;type:varchar(255)" json:"is_reroute"`
	Reason                      string    `gorm:"column:reason;type:varchar(255)" json:"reason"`
	DigitalDeliveryInfo         string    `gorm:"column:digital_delivery_info;type:varchar(255)" json:"digital_delivery_info"`
	PromisedShippingTime        string    `gorm:"column:promised_shipping_time;type:varchar(255)" json:"promised_shipping_time"`
	OrderID                     string    `gorm:"column:order_id;type:varchar(255)" json:"order_id"`
	VoucherAmount               string    `gorm:"column:voucher_amount;type:varchar(255)" json:"voucher_amount"`
	ReturnStatus                string    `gorm:"column:return_status;type:varchar(255)" json:"return_status"`
	ShippingType                string    `gorm:"column:shipping_type;type:varchar(255)" json:"shipping_type"`
	ShipmentProvider            string    `gorm:"column:shipment_provider;type:varchar(255)" json:"shipment_provider"`
	Variation                   string    `gorm:"column:variation;type:varchar(255)" json:"variation"`
	CreatedAt                   time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
	InvoiceNumber               string    `gorm:"column:invoice_number;type:varchar(255)" json:"invoice_number"`
	ShippingAmount              string    `gorm:"column:shipping_amount;type:varchar(255)" json:"shipping_amount"`
	Currency                    string    `gorm:"column:currency;type:varchar(255)" json:"currency"`
	OrderFlag                   string    `gorm:"column:order_flag;type:varchar(255)" json:"order_flag"`
	ShopID                      string    `gorm:"column:shop_id;type:varchar(255)" json:"shop_id"`
	SLATimeStamp                string    `gorm:"column:sla_time_stamp;type:varchar(255)" json:"sla_time_stamp"`
	Sku                         string    `gorm:"column:sku;type:varchar(255)" json:"sku"`
	VoucherCode                 string    `gorm:"column:voucher_code;type:varchar(255)" json:"voucher_code"`
	WalletCredits               string    `gorm:"column:wallet_credits;type:varchar(255)" json:"wallet_credits"`
	UpdatedAt                   time.Time `gorm:"column:updated_at;type:datetime" json:"updated_at"`
	IsDigital                   int32     `gorm:"column:is_digital;type:int" json:"is_digital"`
	TrackingCodePre             string    `gorm:"column:tracking_code_pre;type:varchar(255)" json:"tracking_code_pre"`
	PackageID                   string    `gorm:"column:package_id;type:varchar(255)" json:"package_id"`
	TrackingCode                string    `gorm:"column:tracking_code;type:varchar(255)" json:"tracking_code"`
	ShippingServiceCost         float64   `gorm:"column:shipping_service_cost;type:double" json:"shipping_service_cost"`
	ExtraAttributes             string    `gorm:"column:extra_attributes;type:varchar(255)" json:"extra_attributes"`
	PaidPrice                   string    `gorm:"column:paid_price;type:varchar(255)" json:"paid_price"`
	ShippingProviderType        string    `gorm:"column:shipping_provider_type;type:varchar(255)" json:"shipping_provider_type"`
	ProductDetailURL            string    `gorm:"column:product_detail_url;type:varchar(255)" json:"product_detail_url"`
	ShopSku                     string    `gorm:"column:shop_sku;type:varchar(255)" json:"shop_sku"`
	ReasonDetail                string    `gorm:"column:reason_detail;type:varchar(255)" json:"reason_detail"`
	PurchaseOrderID             string    `gorm:"column:purchase_order_id;type:varchar(255)" json:"purchase_order_id"`
	SkuID                       string    `gorm:"column:sku_id;type:varchar(255)" json:"sku_id"`
	ProductID                   string    `gorm:"column:product_id;type:varchar(255)" json:"product_id"`
	FulfillmentSLA              string    `gorm:"column:fulfillment_sla;type:varchar(255)" json:"fulfillment_sla"`
	PriorityFulfillmentTag      string    `gorm:"column:priority_fulfillment_tag;type:varchar(255)" json:"priority_fulfillment_tag"`
	GiftWrapping                string    `gorm:"column:gift_wrapping;type:varchar(255)" json:"gift_wrapping"`
	ShowGiftwrappingTag         bool      `gorm:"column:show_giftwrapping_tag;type:tinyint(1)" json:"show_giftwrapping_tag"`
	Personalization             string    `gorm:"column:personalization;type:varchar(255)" json:"personalization"`
	ShowPersonalizationTag      bool      `gorm:"column:show_personalization_tag;type:tinyint(1)" json:"show_personalization_tag"`
	PaymentTime                 int64     `gorm:"column:payment_time;type:bigint" json:"payment_time"`
	PickUpStoreInfo             string    `gorm:"column:pick_up_store_info;type:json" json:"pick_up_store_info"`
	PurchaseOrderNumber         string    `gorm:"column:purchase_order_number;type:varchar(255)" json:"purchase_order_number"`
	Name                        string    `gorm:"column:name;type:varchar(255)" json:"name"`
	ProductMainImage            string    `gorm:"column:product_main_image;type:varchar(255)" json:"product_main_image"`
	ItemPrice                   string    `gorm:"column:item_price;type:varchar(255)" json:"item_price"`
	TaxAmount                   string    `gorm:"column:tax_amount;type:varchar(255)" json:"tax_amount"`
	Status                      string    `gorm:"column:status;type:varchar(255)" json:"status"`
	CancelReturnInitiator       string    `gorm:"column:cancel_return_initiator;type:varchar(255)" json:"cancel_return_initiator"`
	VoucherPlatform             string    `gorm:"column:voucher_platform;type:varchar(255)" json:"voucher_platform"`
}

// TableName ThirdLazadaOrderItem's table name
func (*ThirdLazadaOrderItem) TableName() string {
	return TableNameThirdLazadaOrderItem
}
