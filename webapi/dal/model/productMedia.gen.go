// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameProductMedium = "product_media"

// ProductMedium 产品媒体表，存储产品相关的媒体文件信息
type ProductMedium struct {
	ID           int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键，自增ID" json:"id"`                                                     // 主键，自增ID
	Sku          string         `gorm:"column:sku;type:varchar(50);not null;comment:产品SKU，唯一标识一个产品" json:"sku"`                                                            // 产品SKU，唯一标识一个产品
	ProductName  string         `gorm:"column:product_name;type:varchar(500);comment:产品名称" json:"product_name"`                                                            // 产品名称
	MediaType    string         `gorm:"column:media_type;type:varchar(64);not null;comment:媒体类型" json:"media_type"`                                                        // 媒体类型
	CoverMediaID string         `gorm:"column:cover_media_id;type:varchar(500);comment:视频封面链接" json:"cover_media_id"`                                                      // 视频封面链接
	MediaURL     string         `gorm:"column:media_url;type:text;not null;comment:文件的完整URL" json:"media_url"`                                                             // 文件的完整URL
	MediaKey     string         `gorm:"column:media_key;type:varchar(500);not null;comment:Key" json:"media_key"`                                                          // Key
	Position     int32          `gorm:"column:position;type:int;not null;comment:图片顺序" json:"position"`                                                                    // 图片顺序
	UploadStatus string         `gorm:"column:upload_status;type:enum('pending','completed');default:pending;comment:状态，pending表示待处理，completed表示已完成" json:"upload_status"` // 状态，pending表示待处理，completed表示已完成
	CreatedBy    int32          `gorm:"column:created_by;type:int;comment:创建人ID" json:"created_by"`                                                                        // 创建人ID
	UpdatedBy    int32          `gorm:"column:updated_by;type:int;comment:操作人ID" json:"updated_by"`                                                                        // 操作人ID
	CreatedAt    time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                // 创建时间
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:软删除时间" json:"deleted_at"`                                                                  // 软删除时间
	UpdatedAt    time.Time      `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                // 更新时间
	DeleteFlag   int32          `gorm:"column:delete_flag;type:int;not null;comment:删除标识;0-否;1-是;" json:"delete_flag"`                                                     // 删除标识;0-否;1-是;
	DeleteBy     int32          `gorm:"column:delete_by;type:int;comment:删除人" json:"delete_by"`                                                                            // 删除人
	MediaHash    string         `gorm:"column:media_hash;type:varchar(32);comment:商品图片hash唯一值" json:"media_hash"`                                                          // 商品图片hash唯一值
	ProjectCode  string         `gorm:"column:project_code;type:varchar(64);not null;default:E-COMMERCE;comment:适用项目编码;OHSOME；E-COMMERCE" json:"project_code"`             // 适用项目编码;OHSOME；E-COMMERCE
}

// TableName ProductMedium's table name
func (*ProductMedium) TableName() string {
	return TableNameProductMedium
}
