// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameStockShopSku = "stock_shop_sku"

// StockShopSku mapped from table <stock_shop_sku>
type StockShopSku struct {
	ID               int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID           int32  `gorm:"column:shop_id;type:int" json:"shop_id"`
	Sku              string `gorm:"column:sku;type:varchar(200)" json:"sku"`
	ProductID        string `gorm:"column:product_id;type:varchar(100)" json:"product_id"`
	SkuID            string `gorm:"column:sku_id;type:varchar(100)" json:"sku_id"`
	OnShelfStock     int32  `gorm:"column:on_shelf_stock;type:int" json:"on_shelf_stock"`
	Stock            int32  `gorm:"column:stock;type:int" json:"stock"`
	AlreadyOccupied  int32  `gorm:"column:already_occupied;type:int" json:"already_occupied"`
	OccupationFailed int32  `gorm:"column:occupation_failed;type:int" json:"occupation_failed"`
	WarehouseCode    string `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName StockShopSku's table name
func (*StockShopSku) TableName() string {
	return TableNameStockShopSku
}
