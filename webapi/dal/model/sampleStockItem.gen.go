// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameSampleStockItem = "sample_stock_item"

// SampleStockItem 样品库存流水表
type SampleStockItem struct {
	ID             int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:库存流水ID，主键" json:"id"`                    // 库存流水ID，主键
	SampleStockID  int64          `gorm:"column:sample_stock_id;type:bigint;not null;comment:样品仓库存ID" json:"sample_stock_id"`                 // 样品仓库存ID
	OperateType    string         `gorm:"column:operate_type;type:varchar(16);not null;comment:操作类型" json:"operate_type"`                     // 操作类型
	OperateEmail   string         `gorm:"column:operate_email;type:varchar(64);not null;comment:操作人邮箱" json:"operate_email"`                  // 操作人邮箱
	Owner          string         `gorm:"column:owner;type:varchar(64);comment:归属人" json:"owner"`                                             // 归属人
	Quantity       int32          `gorm:"column:quantity;type:int;not null;comment:样品库存数量" json:"quantity"`                                   // 样品库存数量
	BtnNo          string         `gorm:"column:btn_no;type:varchar(16);comment:批次号" json:"btn_no"`                                           // 批次号
	BorrowRecordID int64          `gorm:"column:borrow_record_id;type:bigint;comment:借出记录id，表示当前借出/归还会汇总借出记录表" json:"borrow_record_id"`       // 借出记录id，表示当前借出/归还会汇总借出记录表
	Remark         string         `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`                                           // 备注
	CreatedAt      time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy      int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdatedAt      time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	UpdatedBy      int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                           // 更新人
	FlagDeleted    int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy      int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                           // 删除人
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
}

// TableName SampleStockItem's table name
func (*SampleStockItem) TableName() string {
	return TableNameSampleStockItem
}
