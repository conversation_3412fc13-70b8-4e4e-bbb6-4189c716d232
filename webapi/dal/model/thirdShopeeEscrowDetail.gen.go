// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdShopeeEscrowDetail = "third_shopee_escrow_detail"

// ThirdShopeeEscrowDetail mapped from table <third_shopee_escrow_detail>
type ThirdShopeeEscrowDetail struct {
	OrderSn           string `gorm:"column:order_sn;type:varchar(100);primaryKey" json:"order_sn"`
	BuyerUserName     string `gorm:"column:buyer_user_name;type:varchar(100)" json:"buyer_user_name"`
	OrderIncome       string `gorm:"column:order_income;type:json" json:"order_income"`
	ReturnOrderSnList string `gorm:"column:return_order_sn_list;type:json" json:"return_order_sn_list"`
}

// TableName ThirdShopeeEscrowDetail's table name
func (*ThirdShopeeEscrowDetail) TableName() string {
	return TableNameThirdShopeeEscrowDetail
}
