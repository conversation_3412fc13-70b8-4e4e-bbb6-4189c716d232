// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProductBrief = "product_brief"

// ProductBrief mapped from table <product_brief>
type ProductBrief struct {
	Sku           string    `gorm:"column:sku;type:varchar(200);primaryKey" json:"sku"`
	Name          string    `gorm:"column:name;type:varchar(200)" json:"name"`
	Price         float64   `gorm:"column:price;type:decimal(10,2)" json:"price"`
	Currency      string    `gorm:"column:currency;type:varchar(5)" json:"currency"`
	Image         string    `gorm:"column:image;type:varchar(500)" json:"image"`
	StoreType     string    `gorm:"column:store_type;type:varchar(100)" json:"store_type"`
	SaleClassCode string    `gorm:"column:sale_class_code;type:varchar(50)" json:"sale_class_code"`
	CategoryA     string    `gorm:"column:category_a;type:varchar(50)" json:"category_a"`
	CategoryB     string    `gorm:"column:category_b;type:varchar(50)" json:"category_b"`
	CategoryC     string    `gorm:"column:category_c;type:varchar(50)" json:"category_c"`
	UsedStatus    string    `gorm:"column:used_status;type:varchar(50)" json:"used_status"`
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName ProductBrief's table name
func (*ProductBrief) TableName() string {
	return TableNameProductBrief
}
