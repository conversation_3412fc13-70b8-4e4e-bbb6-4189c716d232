// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameCmsUser = "cms_user"

// CmsUser mapped from table <cms_user>
type CmsUser struct {
	ID       int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Username string `gorm:"column:username;type:varchar(100)" json:"username"`
	Password string `gorm:"column:password;type:varchar(100)" json:"password"`
	Enabled  bool   `gorm:"column:enabled;type:tinyint(1)" json:"enabled"`
	Email    string `gorm:"column:email;type:varchar(255);comment:用户邮箱" json:"email"` // 用户邮箱
}

// TableName CmsUser's table name
func (*CmsUser) TableName() string {
	return TableNameCmsUser
}
