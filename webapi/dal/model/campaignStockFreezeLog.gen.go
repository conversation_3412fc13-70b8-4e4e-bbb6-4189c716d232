// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCampaignStockFreezeLog = "campaign_stock_freeze_log"

// CampaignStockFreezeLog mapped from table <campaign_stock_freeze_log>
type CampaignStockFreezeLog struct {
	ID                      int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ShopID                  int32     `gorm:"column:shop_id;type:int;not null" json:"shop_id"`
	Sku                     string    `gorm:"column:sku;type:varchar(100);not null" json:"sku"`
	CampaignType            string    `gorm:"column:campaign_type;type:varchar(255);not null" json:"campaign_type"`
	Adjustment              int32     `gorm:"column:adjustment;type:int;not null" json:"adjustment"`
	QuantityAfterAdjustment int32     `gorm:"column:quantity_after_adjustment;type:int;not null" json:"quantity_after_adjustment"`
	OrderID                 string    `gorm:"column:order_id;type:varchar(100);not null" json:"order_id"`
	CreatedAt               time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	CreatedBy               int32     `gorm:"column:created_by;type:int" json:"created_by"`
	WarehouseCode           string    `gorm:"column:warehouse_code;type:varchar(32);not null" json:"warehouse_code"`
}

// TableName CampaignStockFreezeLog's table name
func (*CampaignStockFreezeLog) TableName() string {
	return TableNameCampaignStockFreezeLog
}
