// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdShop = "third_shop"

// ThirdShop mapped from table <third_shop>
type ThirdShop struct {
	ID                 int32  `gorm:"column:id;type:int;primaryKey" json:"id"`
	Platform           string `gorm:"column:platform;type:varchar(20)" json:"platform"`
	ShopKey            string `gorm:"column:shop_key;type:varchar(50)" json:"shop_key"`
	ShopName           string `gorm:"column:shop_name;type:varchar(50)" json:"shop_name"`
	PlatformShopID     string `gorm:"column:platform_shop_id;type:varchar(50)" json:"platform_shop_id"`
	Currency           string `gorm:"column:currency;type:varchar(10)" json:"currency"`
	Pro                bool   `gorm:"column:pro;type:tinyint(1)" json:"pro"`
	MultiWarehouseFlag int32  `gorm:"column:multi_warehouse_flag;type:tinyint;not null;comment:是否开通多仓；0-否，1-是" json:"multi_warehouse_flag"` // 是否开通多仓；0-否，1-是
	AppKey             string `gorm:"column:app_key;type:varchar(255)" json:"app_key"`
	Status             string `gorm:"column:status;type:varchar(20);not null;default:enable;comment:店铺状态: enable/disable" json:"status"` // 店铺状态: enable/disable
}

// TableName ThirdShop's table name
func (*ThirdShop) TableName() string {
	return TableNameThirdShop
}
