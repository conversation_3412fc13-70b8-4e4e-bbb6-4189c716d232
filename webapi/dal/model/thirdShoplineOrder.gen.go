// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdShoplineOrder = "third_shopline_order"

// ThirdShoplineOrder mapped from table <third_shopline_order>
type ThirdShoplineOrder struct {
	ID                         string  `gorm:"column:id;type:varchar(200);primaryKey" json:"id"`
	AdjustPrice                string  `gorm:"column:adjust_price;type:json" json:"adjust_price"`
	AdjustPriceSet             string  `gorm:"column:adjust_price_set;type:json" json:"adjust_price_set"`
	BillingAddress             string  `gorm:"column:billing_address;type:json" json:"billing_address"`
	BrowserIP                  string  `gorm:"column:browser_ip;type:varchar(255)" json:"browser_ip"`
	BuyerNote                  string  `gorm:"column:buyer_note;type:varchar(255)" json:"buyer_note"`
	CancelReason               string  `gorm:"column:cancel_reason;type:varchar(255)" json:"cancel_reason"`
	CancelledAt                string  `gorm:"column:cancelled_at;type:varchar(30)" json:"cancelled_at"`
	CartToken                  string  `gorm:"column:cart_token;type:varchar(255)" json:"cart_token"`
	CheckoutID                 string  `gorm:"column:checkout_id;type:varchar(255)" json:"checkout_id"`
	CheckoutToken              string  `gorm:"column:checkout_token;type:varchar(255)" json:"checkout_token"`
	ClientDetails              string  `gorm:"column:client_details;type:json" json:"client_details"`
	ContractSeq                string  `gorm:"column:contract_seq;type:varchar(255)" json:"contract_seq"`
	CreatedAt                  string  `gorm:"column:created_at;type:varchar(30)" json:"created_at"`
	Currency                   string  `gorm:"column:currency;type:varchar(255)" json:"currency"`
	CurrentSubtotalPrice       string  `gorm:"column:current_subtotal_price;type:varchar(255)" json:"current_subtotal_price"`
	CurrentSubtotalPriceSet    string  `gorm:"column:current_subtotal_price_set;type:json" json:"current_subtotal_price_set"`
	CurrentTotalDiscounts      string  `gorm:"column:current_total_discounts;type:varchar(255)" json:"current_total_discounts"`
	CurrentTotalDiscountsSet   string  `gorm:"column:current_total_discounts_set;type:json" json:"current_total_discounts_set"`
	CurrentTotalDutiesSet      string  `gorm:"column:current_total_duties_set;type:json" json:"current_total_duties_set"`
	CurrentTotalPrice          string  `gorm:"column:current_total_price;type:varchar(255)" json:"current_total_price"`
	CurrentTotalPriceSet       string  `gorm:"column:current_total_price_set;type:json" json:"current_total_price_set"`
	CurrentTotalTax            string  `gorm:"column:current_total_tax;type:varchar(255)" json:"current_total_tax"`
	CurrentTotalTaxSet         string  `gorm:"column:current_total_tax_set;type:json" json:"current_total_tax_set"`
	Customer                   string  `gorm:"column:customer;type:json" json:"customer"`
	CustomerLocale             string  `gorm:"column:customer_locale;type:varchar(255)" json:"customer_locale"`
	DeductMemberPointAmount    string  `gorm:"column:deduct_member_point_amount;type:varchar(255)" json:"deduct_member_point_amount"`
	DeductMemberPointAmountSet string  `gorm:"column:deduct_member_point_amount_set;type:json" json:"deduct_member_point_amount_set"`
	DiscountApplications       string  `gorm:"column:discount_applications;type:json" json:"discount_applications"`
	DiscountCodes              string  `gorm:"column:discount_codes;type:json" json:"discount_codes"`
	Email                      string  `gorm:"column:email;type:varchar(255)" json:"email"`
	FinancialStatus            string  `gorm:"column:financial_status;type:varchar(255)" json:"financial_status"`
	FulfillmentStatus          string  `gorm:"column:fulfillment_status;type:varchar(255)" json:"fulfillment_status"`
	Fulfillments               string  `gorm:"column:fulfillments;type:json" json:"fulfillments"`
	HiddenOrder                bool    `gorm:"column:hidden_order;type:tinyint(1)" json:"hidden_order"`
	LandingSite                string  `gorm:"column:landing_site;type:varchar(1200)" json:"landing_site"`
	LineItems                  string  `gorm:"column:line_items;type:json" json:"line_items"`
	Locations                  string  `gorm:"column:locations;type:json" json:"locations"`
	Name                       string  `gorm:"column:name;type:varchar(255)" json:"name"`
	Note                       string  `gorm:"column:note;type:varchar(255)" json:"note"`
	NoteAttributes             string  `gorm:"column:note_attributes;type:json" json:"note_attributes"`
	OrderAt                    string  `gorm:"column:order_at;type:varchar(30)" json:"order_at"`
	OrderSource                string  `gorm:"column:order_source;type:varchar(255)" json:"order_source"`
	OrderStatusURL             string  `gorm:"column:order_status_url;type:varchar(255)" json:"order_status_url"`
	PaymentDetails             string  `gorm:"column:payment_details;type:json" json:"payment_details"`
	PaymentGatewayNames        string  `gorm:"column:payment_gateway_names;type:json" json:"payment_gateway_names"`
	Phone                      string  `gorm:"column:phone;type:varchar(255)" json:"phone"`
	PosLocationID              string  `gorm:"column:pos_location_id;type:varchar(255)" json:"pos_location_id"`
	PresentmentCurrency        string  `gorm:"column:presentment_currency;type:varchar(255)" json:"presentment_currency"`
	ProcessedAt                string  `gorm:"column:processed_at;type:varchar(30)" json:"processed_at"`
	ProcessedUserID            string  `gorm:"column:processed_user_id;type:varchar(255)" json:"processed_user_id"`
	ReferringSite              string  `gorm:"column:referring_site;type:varchar(255)" json:"referring_site"`
	Refunds                    string  `gorm:"column:refunds;type:json" json:"refunds"`
	Service                    string  `gorm:"column:service;type:varchar(255)" json:"service"`
	ShippingAddress            string  `gorm:"column:shipping_address;type:json" json:"shipping_address"`
	ShippingLines              string  `gorm:"column:shipping_lines;type:json" json:"shipping_lines"`
	SourceIdentifier           string  `gorm:"column:source_identifier;type:varchar(255)" json:"source_identifier"`
	SourceName                 string  `gorm:"column:source_name;type:varchar(255)" json:"source_name"`
	SourceURL                  string  `gorm:"column:source_url;type:varchar(255)" json:"source_url"`
	Status                     string  `gorm:"column:status;type:varchar(255)" json:"status"`
	StoreID                    string  `gorm:"column:store_id;type:varchar(255)" json:"store_id"`
	SubtotalPrice              string  `gorm:"column:subtotal_price;type:varchar(255)" json:"subtotal_price"`
	SubtotalPriceSet           string  `gorm:"column:subtotal_price_set;type:json" json:"subtotal_price_set"`
	Tags                       string  `gorm:"column:tags;type:varchar(255)" json:"tags"`
	TaxLines                   string  `gorm:"column:tax_lines;type:json" json:"tax_lines"`
	TaxNumber                  string  `gorm:"column:tax_number;type:varchar(255)" json:"tax_number"`
	TaxType                    string  `gorm:"column:tax_type;type:varchar(255)" json:"tax_type"`
	TaxesIncluded              bool    `gorm:"column:taxes_included;type:tinyint(1)" json:"taxes_included"`
	TotalDiscounts             string  `gorm:"column:total_discounts;type:varchar(255)" json:"total_discounts"`
	TotalDiscountsSet          string  `gorm:"column:total_discounts_set;type:json" json:"total_discounts_set"`
	TotalLineItemsPrice        string  `gorm:"column:total_line_items_price;type:varchar(255)" json:"total_line_items_price"`
	TotalLineItemsPriceSet     string  `gorm:"column:total_line_items_price_set;type:json" json:"total_line_items_price_set"`
	TotalOutstanding           string  `gorm:"column:total_outstanding;type:varchar(255)" json:"total_outstanding"`
	TotalShippingPriceSet      string  `gorm:"column:total_shipping_price_set;type:json" json:"total_shipping_price_set"`
	TotalTax                   string  `gorm:"column:total_tax;type:varchar(255)" json:"total_tax"`
	TotalTaxSet                string  `gorm:"column:total_tax_set;type:json" json:"total_tax_set"`
	TotalTipReceived           string  `gorm:"column:total_tip_received;type:varchar(255)" json:"total_tip_received"`
	TotalTipReceivedSet        string  `gorm:"column:total_tip_received_set;type:json" json:"total_tip_received_set"`
	TotalWeight                float64 `gorm:"column:total_weight;type:double" json:"total_weight"`
	UpdatedAt                  string  `gorm:"column:updated_at;type:varchar(30)" json:"updated_at"`
	UserID                     string  `gorm:"column:user_id;type:varchar(255)" json:"user_id"`
	UtmParameters              string  `gorm:"column:utm_parameters;type:varchar(255)" json:"utm_parameters"`
}

// TableName ThirdShoplineOrder's table name
func (*ThirdShoplineOrder) TableName() string {
	return TableNameThirdShoplineOrder
}
