// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderReturnedItem = "order_returned_item"

// OrderReturnedItem mapped from table <order_returned_item>
type OrderReturnedItem struct {
	ID         int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	ReturnedID int32  `gorm:"column:returned_id;type:int" json:"returned_id"`
	Sku        string `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Quantity   int32  `gorm:"column:quantity;type:int" json:"quantity"`
}

// TableName OrderReturnedItem's table name
func (*OrderReturnedItem) TableName() string {
	return TableNameOrderReturnedItem
}
