// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDeliver = "deliver"

// Deliver mapped from table <deliver>
type Deliver struct {
	ID                int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Type              string    `gorm:"column:type;type:varchar(10)" json:"type"`
	Code              string    `gorm:"column:code;type:varchar(50)" json:"code"`
	OriginID          string    `gorm:"column:origin_id;type:varchar(50)" json:"origin_id"`
	Platform          string    `gorm:"column:platform;type:varchar(16)" json:"platform"`
	ShopID            int32     `gorm:"column:shop_id;type:int" json:"shop_id"`
	LogisticsCode     string    `gorm:"column:logistics_code;type:varchar(100)" json:"logistics_code"`
	LogisticsName     string    `gorm:"column:logistics_name;type:varchar(100)" json:"logistics_name"`
	ReceiverAddressID int32     `gorm:"column:receiver_address_id;type:int" json:"receiver_address_id"`
	Remark            string    `gorm:"column:remark;type:varchar(500)" json:"remark"`
	ExpressDocURL     string    `gorm:"column:express_doc_url;type:varchar(500)" json:"express_doc_url"`
	State             string    `gorm:"column:state;type:varchar(10)" json:"state"`
	WmsCode           string    `gorm:"column:wms_code;type:varchar(100)" json:"wms_code"`
	CreatedTime       time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	WarehouseCode     string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
	RetryCount        int32     `gorm:"column:retry_count;type:int;not null;default:6;comment:重试次数" json:"retry_count"`     // 重试次数
}

// TableName Deliver's table name
func (*Deliver) TableName() string {
	return TableNameDeliver
}
