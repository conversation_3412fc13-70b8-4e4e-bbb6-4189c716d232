// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameLiveOrderProduct = "live_order_product"

// LiveOrderProduct 直播订单商品表
type LiveOrderProduct struct {
	ID          int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	Sku         string         `gorm:"column:sku;type:varchar(128);not null;comment:商品编码" json:"sku"`                                      // 商品编码
	ProductID   string         `gorm:"column:product_id;type:varchar(128);not null;comment:商品id" json:"product_id"`                        // 商品id
	Version     int32          `gorm:"column:version;type:int;not null;comment:版本号" json:"version"`                                        // 版本号
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy   int32          `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	UpdatedBy   int32          `gorm:"column:updated_by;type:int;comment:更新人" json:"updated_by"`                                           // 更新人
	FlagDeleted int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedBy   int32          `gorm:"column:deleted_by;type:int;comment:删除人" json:"deleted_by"`                                           // 删除人
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
}

// TableName LiveOrderProduct's table name
func (*LiveOrderProduct) TableName() string {
	return TableNameLiveOrderProduct
}
