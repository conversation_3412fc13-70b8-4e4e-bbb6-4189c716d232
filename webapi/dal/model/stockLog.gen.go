// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockLog = "stock_log"

// StockLog mapped from table <stock_log>
type StockLog struct {
	ID                  int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Sku                 string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	Num                 int32     `gorm:"column:num;type:int" json:"num"`
	QuantityAfterChange int32     `gorm:"column:quantity_after_change;type:int" json:"quantity_after_change"`
	CreatedTime         time.Time `gorm:"column:created_time;type:datetime" json:"created_time"`
	WarehouseCode       string    `gorm:"column:warehouse_code;type:varchar(32);not null;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName StockLog's table name
func (*StockLog) TableName() string {
	return TableNameStockLog
}
