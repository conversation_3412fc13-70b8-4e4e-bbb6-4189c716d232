// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameImportTask = "import_task"

// ImportTask mapped from table <import_task>
type ImportTask struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                           // 主键
	BatchNo      string    `gorm:"column:batch_no;type:varchar(20);comment:批次号（用于异步请求拉取结果）" json:"batch_no"`                           // 批次号（用于异步请求拉取结果）
	FileID       string    `gorm:"column:file_id;type:varchar(512);not null;comment:文件id" json:"file_id"`                              // 文件id
	TaskStatus   int32     `gorm:"column:task_status;type:tinyint;comment:任务状态（0-未开始，1-成功， 2-失败）" json:"task_status"`                  // 任务状态（0-未开始，1-成功， 2-失败）
	FinishedTime time.Time `gorm:"column:finished_time;type:timestamp;comment:完成时间" json:"finished_time"`                              // 完成时间
	TaskType     int32     `gorm:"column:task_type;type:int;comment:任务类型" json:"task_type"`                                            // 任务类型
	CreatedName  string    `gorm:"column:created_name;type:varchar(128);not null;comment:创建人" json:"created_name"`                     // 创建人
	FileName     string    `gorm:"column:file_name;type:varchar(255);comment:文件名称" json:"file_name"`                                   // 文件名称
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	CreatedBy    int32     `gorm:"column:created_by;type:int;comment:创建人" json:"created_by"`                                           // 创建人
	UpdateBy     int32     `gorm:"column:update_by;type:int;comment:更新人" json:"update_by"`                                             // 更新人
	UpdateAt     time.Time `gorm:"column:update_at;type:datetime;comment:更新时间" json:"update_at"`                                       // 更新时间
}

// TableName ImportTask's table name
func (*ImportTask) TableName() string {
	return TableNameImportTask
}
