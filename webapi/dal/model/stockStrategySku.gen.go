// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameStockStrategySku = "stock_strategy_sku"

// StockStrategySku mapped from table <stock_strategy_sku>
type StockStrategySku struct {
	ID         int32  `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	StrategyID int32  `gorm:"column:strategy_id;type:int" json:"strategy_id"`
	Sku        string `gorm:"column:sku;type:varchar(100)" json:"sku"`
}

// TableName StockStrategySku's table name
func (*StockStrategySku) TableName() string {
	return TableNameStockStrategySku
}
