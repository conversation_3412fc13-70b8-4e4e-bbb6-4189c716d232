// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameWarehouseStockSync = "warehouse_stock_sync"

// WarehouseStockSync mapped from table <warehouse_stock_sync>
type WarehouseStockSync struct {
	ID            int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	WarehouseCode string    `gorm:"column:warehouse_code;type:varchar(255);not null;comment:仓库编码" json:"warehouse_code"`                 // 仓库编码
	NotifyRegion  string    `gorm:"column:notify_region;type:varchar(255);not null;comment:通知地区" json:"notify_region"`                   // 通知地区
	CreateTime    time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"create_time"` // 更新时间
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName WarehouseStockSync's table name
func (*WarehouseStockSync) TableName() string {
	return TableNameWarehouseStockSync
}
