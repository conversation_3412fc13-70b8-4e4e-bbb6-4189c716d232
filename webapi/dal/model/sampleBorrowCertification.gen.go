// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSampleBorrowCertification = "sample_borrow_certification"

// SampleBorrowCertification mapped from table <sample_borrow_certification>
type SampleBorrowCertification struct {
	ID                        int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	RequestID                 int32     `gorm:"column:request_id;type:int;not null;default:-1;comment:借样申请id" json:"request_id"`                               // 借样申请id
	CertificationChecklistPdf string    `gorm:"column:certification_checklist_pdf;type:varchar(255);not null;comment:借样清单" json:"certification_checklist_pdf"` // 借样清单
	CertificationURLPic       string    `gorm:"column:certification_url_pic;type:varchar(255);not null;comment:借样凭证" json:"certification_url_pic"`             // 借样凭证
	CreateTime                time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`           // 创建时间
	UpdateTime                time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`           // 更新时间
}

// TableName SampleBorrowCertification's table name
func (*SampleBorrowCertification) TableName() string {
	return TableNameSampleBorrowCertification
}
