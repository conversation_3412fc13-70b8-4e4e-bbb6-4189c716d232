// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockNew = "stock_new"

// StockNew mapped from table <stock_new>
type StockNew struct {
	Sku            string    `gorm:"column:sku;type:varchar(100);primaryKey" json:"sku"`
	Quantity       int32     `gorm:"column:quantity;type:int" json:"quantity"`
	FreezeQuantity int32     `gorm:"column:freeze_quantity;type:int" json:"freeze_quantity"`
	Safety         int32     `gorm:"column:safety;type:int" json:"safety"`
	UpdateTime     time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
	WarehouseCode  string    `gorm:"column:warehouse_code;type:varchar(32);primaryKey;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName StockNew's table name
func (*StockNew) TableName() string {
	return TableNameStockNew
}
