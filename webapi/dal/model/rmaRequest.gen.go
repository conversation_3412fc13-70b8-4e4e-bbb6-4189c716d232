// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRmaRequest = "rma_request"

// RmaRequest RMA申请主表
type RmaRequest struct {
	ID        int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`               // 主键ID
	RmaNo     string    `gorm:"column:rma_no;type:varchar(100);not null;comment:RMA编号" json:"rma_no"`                     // RMA编号
	OrderID   string    `gorm:"column:order_id;type:varchar(100);not null;comment:关联订单ID" json:"order_id"`                // 关联订单ID
	CreatedBy string    `gorm:"column:created_by;type:varchar(50);comment:创建人" json:"created_by"`                         // 创建人
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy string    `gorm:"column:updated_by;type:varchar(50);comment:更新人" json:"updated_by"`                         // 更新人
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName RmaRequest's table name
func (*RmaRequest) TableName() string {
	return TableNameRmaRequest
}
