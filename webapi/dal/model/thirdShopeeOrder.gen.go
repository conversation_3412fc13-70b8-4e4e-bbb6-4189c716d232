// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdShopeeOrder = "third_shopee_order"

// ThirdShopeeOrder mapped from table <third_shopee_order>
type ThirdShopeeOrder struct {
	OrderSn                    string  `gorm:"column:order_sn;type:varchar(100);primaryKey" json:"order_sn"`
	ActualShippingFee          float64 `gorm:"column:actual_shipping_fee;type:decimal(10,2)" json:"actual_shipping_fee"`
	Region                     string  `gorm:"column:region;type:varchar(255)" json:"region"`
	Currency                   string  `gorm:"column:currency;type:varchar(255)" json:"currency"`
	Cod                        bool    `gorm:"column:cod;type:tinyint(1)" json:"cod"`
	TotalAmount                float64 `gorm:"column:total_amount;type:decimal(10,2)" json:"total_amount"`
	OrderStatus                string  `gorm:"column:order_status;type:varchar(255)" json:"order_status"`
	PendingTerms               string  `gorm:"column:pending_terms;type:json" json:"pending_terms"`
	ShippingCarrier            string  `gorm:"column:shipping_carrier;type:varchar(255)" json:"shipping_carrier"`
	PaymentMethod              string  `gorm:"column:payment_method;type:varchar(255)" json:"payment_method"`
	EstimatedShippingFee       float64 `gorm:"column:estimated_shipping_fee;type:decimal(10,2)" json:"estimated_shipping_fee"`
	MessageToSeller            string  `gorm:"column:message_to_seller;type:varchar(2000)" json:"message_to_seller"`
	CreateTime                 int64   `gorm:"column:create_time;type:bigint" json:"create_time"`
	UpdateTime                 int64   `gorm:"column:update_time;type:bigint" json:"update_time"`
	DaysToShip                 int32   `gorm:"column:days_to_ship;type:int" json:"days_to_ship"`
	ShipByDate                 int64   `gorm:"column:ship_by_date;type:bigint" json:"ship_by_date"`
	BuyerUserID                int32   `gorm:"column:buyer_user_id;type:int" json:"buyer_user_id"`
	BuyerUsername              string  `gorm:"column:buyer_username;type:varchar(255)" json:"buyer_username"`
	RecipientAddress           string  `gorm:"column:recipient_address;type:json" json:"recipient_address"`
	GoodsToDeclare             bool    `gorm:"column:goods_to_declare;type:tinyint(1)" json:"goods_to_declare"`
	Note                       string  `gorm:"column:note;type:varchar(255)" json:"note"`
	NoteUpdateTime             int64   `gorm:"column:note_update_time;type:bigint" json:"note_update_time"`
	ItemList                   string  `gorm:"column:item_list;type:json" json:"item_list"`
	PayTime                    int64   `gorm:"column:pay_time;type:bigint" json:"pay_time"`
	Dropshipper                string  `gorm:"column:dropshipper;type:varchar(255)" json:"dropshipper"`
	DropshipperPhone           string  `gorm:"column:dropshipper_phone;type:varchar(255)" json:"dropshipper_phone"`
	SplitUp                    bool    `gorm:"column:split_up;type:tinyint(1)" json:"split_up"`
	BuyerCancelReason          string  `gorm:"column:buyer_cancel_reason;type:varchar(255)" json:"buyer_cancel_reason"`
	CancelBy                   string  `gorm:"column:cancel_by;type:varchar(255)" json:"cancel_by"`
	CancelReason               string  `gorm:"column:cancel_reason;type:varchar(255)" json:"cancel_reason"`
	ActualShippingFeeConfirmed bool    `gorm:"column:actual_shipping_fee_confirmed;type:tinyint(1)" json:"actual_shipping_fee_confirmed"`
	BuyerCpfID                 string  `gorm:"column:buyer_cpf_id;type:varchar(255)" json:"buyer_cpf_id"`
	FulfillmentFlag            string  `gorm:"column:fulfillment_flag;type:varchar(255)" json:"fulfillment_flag"`
	PickupDoneTime             int64   `gorm:"column:pickup_done_time;type:bigint" json:"pickup_done_time"`
	PackageList                string  `gorm:"column:package_list;type:json" json:"package_list"`
	InvoiceData                string  `gorm:"column:invoice_data;type:json" json:"invoice_data"`
	CheckoutShippingCarrier    string  `gorm:"column:checkout_shipping_carrier;type:varchar(255)" json:"checkout_shipping_carrier"`
	ReverseShippingFee         float64 `gorm:"column:reverse_shipping_fee;type:decimal(10,2)" json:"reverse_shipping_fee"`
	OrderChargeableWeightGram  int32   `gorm:"column:order_chargeable_weight_gram;type:int" json:"order_chargeable_weight_gram"`
	EdtFrom                    int64   `gorm:"column:edt_from;type:bigint" json:"edt_from"`
	EdtTo                      int64   `gorm:"column:edt_to;type:bigint" json:"edt_to"`
	PrescriptionImages         string  `gorm:"column:prescription_images;type:json" json:"prescription_images"`
	PrescriptionCheckStatus    int32   `gorm:"column:prescription_check_status;type:int" json:"prescription_check_status"`
}

// TableName ThirdShopeeOrder's table name
func (*ThirdShopeeOrder) TableName() string {
	return TableNameThirdShopeeOrder
}
