// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePendingDeliverRetry = "pending_deliver_retry"

// PendingDeliverRetry 待发货重试表
type PendingDeliverRetry struct {
	ID          int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                         // 主键id
	DeliverID   int32          `gorm:"column:deliver_id;type:int;not null;comment:待发货表id" json:"deliver_id"`                               // 待发货表id
	RetryCount  int32          `gorm:"column:retry_count;type:int;not null;default:6;comment:重试次数" json:"retry_count"`                     // 重试次数
	NextTime    time.Time      `gorm:"column:next_time;type:datetime;not null;comment:下一次执行时间" json:"next_time"`                           // 下一次执行时间
	CreatedAt   time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt   time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间，更新时自动更新为当前时间" json:"updated_at"`                       // 更新时间，更新时自动更新为当前时间
	FlagDeleted int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志，0: 未删除，1: 已删除" json:"flag_deleted"`         // 逻辑删除标志，0: 未删除，1: 已删除
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                    // 删除时间
}

// TableName PendingDeliverRetry's table name
func (*PendingDeliverRetry) TableName() string {
	return TableNamePendingDeliverRetry
}
