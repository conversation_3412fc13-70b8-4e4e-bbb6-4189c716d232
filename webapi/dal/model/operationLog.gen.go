// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOperationLog = "operation_log"

// OperationLog mapped from table <operation_log>
type OperationLog struct {
	ID                   int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:操作记录ID" json:"id"`                               // 操作记录ID
	UserID               int64     `gorm:"column:user_id;type:bigint;not null;comment:操作用户ID" json:"user_id"`                                          // 操作用户ID
	UserName             string    `gorm:"column:user_name;type:varchar(255);not null;comment:操作用户名" json:"user_name"`                                 // 操作用户名
	ModuleName           string    `gorm:"column:module_name;type:varchar(255);not null;comment:功能模块名称" json:"module_name"`                            // 功能模块名称
	DataID               string    `gorm:"column:data_id;type:varchar(128);not null;comment:被操作数据的ID" json:"data_id"`                                  // 被操作数据的ID
	OperationType        string    `gorm:"column:operation_type;type:varchar(255);not null;comment:操作类型（ADD-新增，EDIT-编辑）" json:"operation_type"`        // 操作类型（ADD-新增，EDIT-编辑）
	OperationDescription string    `gorm:"column:operation_description;type:text;comment:操作描述" json:"operation_description"`                           // 操作描述
	OperationTime        time.Time `gorm:"column:operation_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:操作时间" json:"operation_time"` // 操作时间
	CreatedAt            time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`       // 记录创建时间
	UpdatedAt            time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:记录更新时间" json:"updated_at"`       // 记录更新时间
}

// TableName OperationLog's table name
func (*OperationLog) TableName() string {
	return TableNameOperationLog
}
