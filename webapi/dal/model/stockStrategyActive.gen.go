// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStockStrategyActive = "stock_strategy_active"

// StockStrategyActive mapped from table <stock_strategy_active>
type StockStrategyActive struct {
	ID            int32     `gorm:"column:id;type:int;primaryKey;autoIncrement:true" json:"id"`
	Sku           string    `gorm:"column:sku;type:varchar(100)" json:"sku"`
	ShopID        int32     `gorm:"column:shop_id;type:int" json:"shop_id"`
	StrategyID    int32     `gorm:"column:strategy_id;type:int" json:"strategy_id"`
	StrategyLevel int64     `gorm:"column:strategy_level;type:bigint" json:"strategy_level"`
	UpdateTime    time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
}

// TableName StockStrategyActive's table name
func (*StockStrategyActive) TableName() string {
	return TableNameStockStrategyActive
}
