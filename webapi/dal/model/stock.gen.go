// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStock = "stock"

// Stock mapped from table <stock>
type Stock struct {
	Sku            string    `gorm:"column:sku;type:varchar(100);primaryKey" json:"sku"`
	Quantity       int32     `gorm:"column:quantity;type:int" json:"quantity"`
	FreezeQuantity int32     `gorm:"column:freeze_quantity;type:int" json:"freeze_quantity"`
	Safety         int32     `gorm:"column:safety;type:int" json:"safety"`
	UpdateTime     time.Time `gorm:"column:update_time;type:datetime" json:"update_time"`
	WarehouseCode  string    `gorm:"column:warehouse_code;type:varchar(32);primaryKey;comment:仓库编码" json:"warehouse_code"` // 仓库编码
}

// TableName Stock's table name
func (*Stock) TableName() string {
	return TableNameStock
}
