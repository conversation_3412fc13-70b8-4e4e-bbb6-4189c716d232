// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThirdTokopediaOrder = "third_tokopedia_order"

// ThirdTokopediaOrder mapped from table <third_tokopedia_order>
type ThirdTokopediaOrder struct {
	OrderID             int64  `gorm:"column:order_id;type:bigint;primaryKey" json:"order_id"`
	BuyerID             int64  `gorm:"column:buyer_id;type:bigint" json:"buyer_id"`
	SellerID            int64  `gorm:"column:seller_id;type:bigint" json:"seller_id"`
	PaymentID           int64  `gorm:"column:payment_id;type:bigint" json:"payment_id"`
	IsAffiliate         bool   `gorm:"column:is_affiliate;type:tinyint(1)" json:"is_affiliate"`
	IsFulfillment       bool   `gorm:"column:is_fulfillment;type:tinyint(1)" json:"is_fulfillment"`
	OrderWarehouse      string `gorm:"column:order_warehouse;type:json" json:"order_warehouse"`
	OrderStatus         int32  `gorm:"column:order_status;type:int" json:"order_status"`
	InvoiceNumber       string `gorm:"column:invoice_number;type:varchar(255)" json:"invoice_number"`
	InvoicePdf          string `gorm:"column:invoice_pdf;type:varchar(255)" json:"invoice_pdf"`
	InvoiceURL          string `gorm:"column:invoice_url;type:varchar(1000)" json:"invoice_url"`
	OpenAmt             int32  `gorm:"column:open_amt;type:int" json:"open_amt"`
	LpAmt               int32  `gorm:"column:lp_amt;type:int" json:"lp_amt"`
	CashbackAmt         int32  `gorm:"column:cashback_amt;type:int" json:"cashback_amt"`
	Info                string `gorm:"column:info;type:varchar(255)" json:"info"`
	Comment             string `gorm:"column:comment;type:varchar(1000)" json:"comment"`
	ItemPrice           int32  `gorm:"column:item_price;type:int" json:"item_price"`
	BuyerInfo           string `gorm:"column:buyer_info;type:json" json:"buyer_info"`
	ShopInfo            string `gorm:"column:shop_info;type:json" json:"shop_info"`
	ShipmentFulfillment string `gorm:"column:shipment_fulfillment;type:json" json:"shipment_fulfillment"`
	Preorder            string `gorm:"column:preorder;type:json" json:"preorder"`
	OrderInfo           string `gorm:"column:order_info;type:json" json:"order_info"`
	OriginInfo          string `gorm:"column:origin_info;type:json" json:"origin_info"`
	PaymentInfo         string `gorm:"column:payment_info;type:json" json:"payment_info"`
	InsuranceInfo       string `gorm:"column:insurance_info;type:json" json:"insurance_info"`
	CreateTime          string `gorm:"column:create_time;type:varchar(255)" json:"create_time"`
	ShippingDate        string `gorm:"column:shipping_date;type:varchar(255)" json:"shipping_date"`
	UpdateTime          string `gorm:"column:update_time;type:varchar(255)" json:"update_time"`
	PaymentDate         string `gorm:"column:payment_date;type:varchar(255)" json:"payment_date"`
	DeliveredDate       string `gorm:"column:delivered_date;type:varchar(255)" json:"delivered_date"`
	EstShippingDate     string `gorm:"column:est_shipping_date;type:varchar(255)" json:"est_shipping_date"`
	EstDeliveryDate     string `gorm:"column:est_delivery_date;type:varchar(255)" json:"est_delivery_date"`
	PromoOrderDetail    string `gorm:"column:promo_order_detail;type:json" json:"promo_order_detail"`
	PofInfo             string `gorm:"column:pof_info;type:json" json:"pof_info"`
}

// TableName ThirdTokopediaOrder's table name
func (*ThirdTokopediaOrder) TableName() string {
	return TableNameThirdTokopediaOrder
}
