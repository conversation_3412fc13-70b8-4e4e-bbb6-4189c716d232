// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameOrderExtra = "order_extra"

// OrderExtra mapped from table <order_extra>
type OrderExtra struct {
	ID                   string  `gorm:"column:id;type:varchar(100);primaryKey" json:"id"`
	TrackingNumber       string  `gorm:"column:tracking_number;type:varchar(150)" json:"tracking_number"`
	TotalPrice           float64 `gorm:"column:total_price;type:decimal(20,2)" json:"total_price"`
	Name                 string  `gorm:"column:name;type:varchar(150)" json:"name"`
	Freight              float64 `gorm:"column:freight;type:decimal(10,2)" json:"freight"`
	LoadBearing          string  `gorm:"column:load_bearing;type:varchar(30)" json:"load_bearing"`
	BuyerMsg             string  `gorm:"column:buyer_msg;type:varchar(500)" json:"buyer_msg"`
	BuyerPreferExpressID string  `gorm:"column:buyer_prefer_express_id;type:varchar(50)" json:"buyer_prefer_express_id"`
	PdfURL               string  `gorm:"column:pdf_url;type:varchar(500)" json:"pdf_url"`
	PackageID            string  `gorm:"column:package_id;type:varchar(100)" json:"package_id"`
	ShipName             string  `gorm:"column:ship_name;type:varchar(100)" json:"ship_name"`
	ShipPhone            string  `gorm:"column:ship_phone;type:varchar(48)" json:"ship_phone"`
	ShipNation           string  `gorm:"column:ship_nation;type:varchar(48)" json:"ship_nation"`
	ShipState            string  `gorm:"column:ship_state;type:varchar(48)" json:"ship_state"`
	ShipCity             string  `gorm:"column:ship_city;type:varchar(48)" json:"ship_city"`
	ShipZipCode          string  `gorm:"column:ship_zip_code;type:varchar(48)" json:"ship_zip_code"`
	ShipAddress          string  `gorm:"column:ship_address;type:varchar(500)" json:"ship_address"`
	MasterOrderNo        string  `gorm:"column:master_order_no;type:varchar(32);comment:主订单编码" json:"master_order_no"` // 主订单编码
	PayAmount            float64 `gorm:"column:pay_amount;type:decimal(18,2);comment:实付金额" json:"pay_amount"`          // 实付金额
}

// TableName OrderExtra's table name
func (*OrderExtra) TableName() string {
	return TableNameOrderExtra
}
