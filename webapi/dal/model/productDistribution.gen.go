// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameProductDistribution = "product_distribution"

// ProductDistribution 分销商品配置表
type ProductDistribution struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`             // 主键ID
	ShopID      int32     `gorm:"column:shop_id;type:int;not null;comment:店铺ID" json:"shop_id"`                           // 店铺ID
	Sku         string    `gorm:"column:sku;type:varchar(255);not null;comment:商品编码" json:"sku"`                          // 商品编码
	ProductID   string    `gorm:"column:product_id;type:varchar(255);not null;comment:商品ID" json:"product_id"`            // 商品ID
	Remark      string    `gorm:"column:remark;type:varchar(500);comment:备注" json:"remark"`                               // 备注
	FlagDeleted int32     `gorm:"column:flag_deleted;type:tinyint;not null;comment:是否删除；0-否；1-是" json:"flag_deleted"`     // 是否删除；0-否；1-是
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;not null;default:now();comment:创建时间" json:"created_at"`  // 创建时间
	CreatedBy   int32     `gorm:"column:created_by;type:int;comment:创建人ID" json:"created_by"`                             // 创建人ID
	UpdateAt    time.Time `gorm:"column:update_at;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_at"` // 更新时间
	UpdateBy    int32     `gorm:"column:update_by;type:int;comment:更新人" json:"update_by"`                                 // 更新人
}

// TableName ProductDistribution's table name
func (*ProductDistribution) TableName() string {
	return TableNameProductDistribution
}
