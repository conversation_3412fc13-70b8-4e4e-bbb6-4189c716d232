// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameInventoryItemOld = "inventory_item_old"

// InventoryItemOld 商品详情表，存储商品的详细信息
type InventoryItemOld struct {
	ID             int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:商品ID，主键，自增" json:"id"`                                                    // 商品ID，主键，自增
	Sku            string         `gorm:"column:sku;type:varchar(64);not null;comment:SKU编码，唯一标识商品" json:"sku"`                                                                // SKU编码，唯一标识商品
	SkuName        string         `gorm:"column:sku_name;type:varchar(255);not null;comment:SKU名称（本地语言）" json:"sku_name"`                                                      // SKU名称（本地语言）
	SkuNameEn      string         `gorm:"column:sku_name_en;type:varchar(255);comment:SKU名称（英文）" json:"sku_name_en"`                                                           // SKU名称（英文）
	SkuNameZh      string         `gorm:"column:sku_name_zh;type:varchar(255);comment:SKU名称（中文）" json:"sku_name_zh"`                                                           // SKU名称（中文）
	Barcode        string         `gorm:"column:barcode;type:varchar(64);not null;comment:商品条形码" json:"barcode"`                                                               // 商品条形码
	SpuID          string         `gorm:"column:spu_id;type:varchar(32);not null;comment:SPU编码，商品编码" json:"spu_id"`                                                            // SPU编码，商品编码
	SpuName        string         `gorm:"column:spu_name;type:varchar(255);comment:SPU名称（本地语言）" json:"spu_name"`                                                               // SPU名称（本地语言）
	SpuNameEn      string         `gorm:"column:spu_name_en;type:varchar(255);comment:SPU名称（英文）" json:"spu_name_en"`                                                           // SPU名称（英文）
	SpuNameZh      string         `gorm:"column:spu_name_zh;type:varchar(255);comment:SPU名称（中文）" json:"spu_name_zh"`                                                           // SPU名称（中文）
	Brand          string         `gorm:"column:brand;type:varchar(64);not null;comment:品牌名称" json:"brand"`                                                                    // 品牌名称
	UnitPrice      float64        `gorm:"column:unit_price;type:decimal(18,2);default:0.00;comment:商品零售价" json:"unit_price"`                                                   // 商品零售价
	Image          string         `gorm:"column:image;type:json;comment:零售基础物料图" json:"image"`                                                                                 // 零售基础物料图
	Attribute      string         `gorm:"column:attribute;type:json;comment:商品规格属性（JSON格式）" json:"attribute"`                                                                  // 商品规格属性（JSON格式）
	CategoryLevel1 string         `gorm:"column:category_level1;type:varchar(32);not null;comment:商品一级分类" json:"category_level1"`                                              // 商品一级分类
	CategoryLevel2 string         `gorm:"column:category_level2;type:varchar(32);not null;comment:商品二级分类" json:"category_level2"`                                              // 商品二级分类
	CategoryLevel3 string         `gorm:"column:category_level3;type:varchar(32);not null;comment:商品三级分类" json:"category_level3"`                                              // 商品三级分类
	CategoryLevel4 string         `gorm:"column:category_level4;type:varchar(32);not null;comment:商品四级分类" json:"category_level4"`                                              // 商品四级分类
	SaleCountry    string         `gorm:"column:sale_country;type:json;comment:销售国家（JSON格式，数组）" json:"sale_country"`                                                           // 销售国家（JSON格式，数组）
	Supplier       string         `gorm:"column:supplier;type:json;comment:供应商信息（JSON格式）" json:"supplier"`                                                                     // 供应商信息（JSON格式）
	CreatedAt      time.Time      `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                  // 创建时间
	CreatedBy      int32          `gorm:"column:created_by;type:int;comment:创建人ID" json:"created_by"`                                                                          // 创建人ID
	UpdatedAt      time.Time      `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                  // 更新时间
	UpdatedBy      int32          `gorm:"column:updated_by;type:int;comment:更新人ID" json:"updated_by"`                                                                          // 更新人ID
	FlagDeleted    int32          `gorm:"column:flag_deleted;type:tinyint;not null;comment:逻辑删除标志（0:未删除，1:已删除）" json:"flag_deleted"`                                           // 逻辑删除标志（0:未删除，1:已删除）
	DeletedBy      int32          `gorm:"column:deleted_by;type:int;comment:删除人ID" json:"deleted_by"`                                                                          // 删除人ID
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`                                                                     // 删除时间
	ProjectCode    string         `gorm:"column:project_code;type:varchar(64);comment:适用项目编码;OHSOME；E-COMMERCE" json:"project_code"`                                           // 适用项目编码;OHSOME；E-COMMERCE
	GoodsStatus    string         `gorm:"column:goods_status;type:varchar(32);comment:商品状态;NEW:新品;NORMAL:正常品;LIMITED:限量热销品;ELIMINATED:淘汰品;UNSALEABLE:滞销品" json:"goods_status"` // 商品状态;NEW:新品;NORMAL:正常品;LIMITED:限量热销品;ELIMINATED:淘汰品;UNSALEABLE:滞销品
}

// TableName InventoryItemOld's table name
func (*InventoryItemOld) TableName() string {
	return TableNameInventoryItemOld
}
