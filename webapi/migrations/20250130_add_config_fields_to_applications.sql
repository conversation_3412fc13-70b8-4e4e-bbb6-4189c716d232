-- 为 applications 表添加配置文件相关字段
-- 执行时间：2025-01-30
-- 功能：支持配置文件对比功能

-- 添加配置文件相关字段
ALTER TABLE applications 
ADD COLUMN config_file_path VARCHAR(500) DEFAULT '' COMMENT 'Git 分支中的 YAML 配置文件路径，通常是 ${service_name}/etc/${service_name}.yaml',
ADD COLUMN configmap VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的名称',
ADD COLUMN configmap_key VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的 key';

-- 添加索引以提高查询性能
CREATE INDEX idx_applications_configmap ON applications(configmap);
CREATE INDEX idx_applications_configmap_key ON applications(configmap_key);

-- 验证字段添加成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'applications' 
    AND COLUMN_NAME IN ('config_file_path', 'configmap', 'configmap_key');
