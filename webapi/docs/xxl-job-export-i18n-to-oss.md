# XXL-Job 导出语言包到 OSS 任务

## 任务概述

`exportI18nToOSS` 是一个 xxl-job 任务，用于将 Android 和 iOS 的多语言包导出并上传到阿里云 OSS 存储中。

## 功能特性

- 支持导出 Android XML 格式和 iOS strings 格式的语言包
- 支持多种语言：zh-CN, zh-TW, en-US, th-TH, vi-VN, id-ID, km-KH
- 自动上传到 OSS 并生成版本化的文件路径
- 发送详细的飞书通知报告
- 支持自定义参数配置

## 任务参数

任务支持 JSON 格式的参数配置：

```json
{
  "tag": "v20240315120000",
  "namespace": "ohsome-app", 
  "languages": ["zh-CN", "zh-TW", "en-US", "th-TH", "vi-VN", "id-ID", "km-KH"]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| tag | string | 否 | 当前时间戳 | 版本标签，用于文件路径和版本管理 |
| namespace | string | 否 | "ohsome-app" | 语言包的命名空间 |
| languages | []string | 否 | 默认7种语言 | 需要导出的语言列表 |

## 输出文件

### 文件路径结构
```
i18n/{tag}/
├── strings_zh-CN.xml          # Android 中文简体
├── strings_zh-TW.xml          # Android 中文繁体
├── strings_en-US.xml          # Android 英文
├── strings_th-TH.xml          # Android 泰文
├── strings_vi-VN.xml          # Android 越南文
├── strings_id-ID.xml          # Android 印尼文
├── strings_km-KH.xml          # Android 柬埔寨文
├── Localizable_zh-CN.strings  # iOS 中文简体
├── Localizable_zh-TW.strings  # iOS 中文繁体
├── Localizable_en-US.strings  # iOS 英文
├── Localizable_th-TH.strings  # iOS 泰文
├── Localizable_vi-VN.strings  # iOS 越南文
├── Localizable_id-ID.strings  # iOS 印尼文
└── Localizable_km-KH.strings  # iOS 柬埔寨文
```

### 文件格式

**Android XML 格式示例：**
```xml
<resources>
  <string name="app_name">应用名称</string>
  <string name="welcome_message">欢迎使用</string>
</resources>
```

**iOS Strings 格式示例：**
```
"app_name" = "应用名称";
"welcome_message" = "欢迎使用";
```

## 使用方法

### 1. 在 XXL-Job 管理后台创建任务

- **任务名称：** exportI18nToOSS
- **执行器：** ec-ops
- **任务描述：** 导出语言包到OSS
- **负责人：** 运维团队
- **调度类型：** 手动触发 或 CRON表达式

### 2. 使用默认参数执行

直接执行任务，不传入任何参数，将使用默认配置：
- 版本标签：当前时间戳（如 v20240315120000）
- 命名空间：ohsome-app
- 语言：全部7种支持的语言

### 3. 使用自定义参数执行

在任务参数中输入 JSON 配置：

**示例1：指定版本标签**
```json
{
  "tag": "v1.2.0"
}
```

**示例2：只导出部分语言**
```json
{
  "tag": "v1.2.0",
  "languages": ["zh-CN", "en-US", "id-ID"]
}
```

**示例3：自定义命名空间**
```json
{
  "tag": "v1.2.0",
  "namespace": "custom-app",
  "languages": ["zh-CN", "en-US"]
}
```

## 通知报告

任务执行完成后会发送飞书通知，包含以下信息：

- 版本标签和命名空间
- 导出统计（成功/失败数量）
- 成功导出的文件列表和大小
- 失败的导出及错误信息
- OSS 访问路径和文件命名规则

## 注意事项

1. **权限要求：** 确保服务有 OSS 的读写权限
2. **网络连接：** 需要能够访问阿里云 OSS 服务
3. **存储空间：** 确保 OSS bucket 有足够的存储空间
4. **语言数据：** 确保数据库中有对应语言的翻译数据
5. **飞书通知：** 确保飞书 webhook 配置正确

## 故障排查

### 常见错误

1. **OSS 连接失败**
   - 检查 OSS 配置（AccessKey、SecretKey、Endpoint）
   - 确认网络连接正常

2. **语言包导出失败**
   - 检查数据库连接
   - 确认指定的 namespace 和 language 存在翻译数据

3. **飞书通知失败**
   - 检查飞书 webhook 地址配置
   - 确认网络能访问飞书 API

### 日志查看

任务执行日志可在 XXL-Job 管理后台的"调度日志"中查看，包含详细的执行过程和错误信息。

## 技术实现

- **基础框架：** go-zero + xxl-job-executor-go
- **存储服务：** 阿里云 OSS
- **通知服务：** 飞书机器人
- **数据源：** MySQL 翻译数据表
- **文件格式：** Android XML / iOS Strings
