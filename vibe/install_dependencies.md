# 配置文件对比功能依赖安装指南

## 新增依赖包

为了支持 YAML 字段排序和专业差异对比功能，需要安装以下依赖包：

### 运行时依赖
```json
{
  "js-yaml": "^4.1.0",
  "react-diff-viewer": "^3.1.1"
}
```

### 开发时依赖
```json
{
  "@types/js-yaml": "^4.0.9"
}
```

## 安装步骤

### 方法 1: 使用 npm
```bash
cd web
npm install js-yaml@^4.1.0 react-diff-viewer@^3.1.1
npm install --save-dev @types/js-yaml@^4.0.9
```

### 方法 2: 使用 yarn
```bash
cd web
yarn add js-yaml@^4.1.0 react-diff-viewer@^3.1.1
yarn add --dev @types/js-yaml@^4.0.9
```

### 方法 3: 使用 pnpm
```bash
cd web
pnpm add js-yaml@^4.1.0 react-diff-viewer@^3.1.1
pnpm add --save-dev @types/js-yaml@^4.0.9
```

## 依赖包说明

### js-yaml
- **用途**: YAML 文件解析和序列化
- **功能**: 
  - 解析 YAML 字符串为 JavaScript 对象
  - 将 JavaScript 对象序列化为 YAML 字符串
  - 支持自定义排序和格式化选项
- **文档**: https://github.com/nodeca/js-yaml

### react-diff-viewer
- **用途**: React 差异对比组件
- **功能**:
  - 并排差异显示
  - 语法高亮
  - 行号显示
  - 自定义样式
- **文档**: https://github.com/praneshr/react-diff-viewer

### @types/js-yaml
- **用途**: js-yaml 的 TypeScript 类型定义
- **功能**: 提供完整的类型支持和 IDE 智能提示

## 验证安装

### 检查 package.json
安装完成后，检查 `web/package.json` 文件是否包含以下依赖：

```json
{
  "dependencies": {
    "js-yaml": "^4.1.0",
    "react-diff-viewer": "^3.1.1",
    // ... 其他依赖
  },
  "devDependencies": {
    "@types/js-yaml": "^4.0.9",
    // ... 其他开发依赖
  }
}
```

### 测试导入
创建一个测试文件验证依赖是否正确安装：

```typescript
// test-imports.ts
import * as yaml from 'js-yaml';
import ReactDiffViewer from 'react-diff-viewer';

// 测试 js-yaml
const testYaml = `
app:
  name: test
  version: 1.0.0
`;

try {
  const parsed = yaml.load(testYaml);
  console.log('js-yaml 安装成功:', parsed);
} catch (error) {
  console.error('js-yaml 安装失败:', error);
}

// 测试 react-diff-viewer
console.log('react-diff-viewer 导入成功:', typeof ReactDiffViewer);
```

### 编译测试
运行前端项目，确保没有编译错误：

```bash
cd web
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

## 可能遇到的问题

### 1. 版本冲突
**问题**: 依赖版本与现有包冲突
**解决方案**:
```bash
# 清理 node_modules 和 lock 文件
rm -rf node_modules package-lock.json
# 或 yarn.lock / pnpm-lock.yaml

# 重新安装
npm install
```

### 2. TypeScript 类型错误
**问题**: TypeScript 编译时类型错误
**解决方案**:
```bash
# 确保安装了类型定义
npm install --save-dev @types/js-yaml

# 重启 TypeScript 服务
# 在 VSCode 中: Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

### 3. 构建错误
**问题**: 前端构建时出现错误
**解决方案**:
```bash
# 清理构建缓存
npm run clean
# 或删除 .umi 目录
rm -rf .umi

# 重新构建
npm run build
```

### 4. 运行时错误
**问题**: 浏览器中出现运行时错误
**解决方案**:
1. 检查浏览器控制台错误信息
2. 确认依赖版本兼容性
3. 检查导入语句是否正确

## 依赖更新

### 定期更新
建议定期检查和更新依赖包：

```bash
# 检查过时的包
npm outdated

# 更新到最新版本
npm update js-yaml react-diff-viewer
npm update --save-dev @types/js-yaml
```

### 安全更新
定期运行安全审计：

```bash
# 检查安全漏洞
npm audit

# 自动修复
npm audit fix
```

## 生产环境注意事项

### 1. 包大小优化
- `js-yaml` 和 `react-diff-viewer` 会增加打包体积
- 考虑使用代码分割（Code Splitting）延迟加载
- 在不需要这些功能的页面中避免导入

### 2. 性能考虑
- 大型 YAML 文件的解析可能影响性能
- 考虑在 Web Worker 中进行 YAML 处理
- 实现适当的错误边界和降级策略

### 3. 浏览器兼容性
- 确保目标浏览器支持所使用的 JavaScript 特性
- 测试在不同浏览器中的表现

## 回滚方案

如果新功能出现问题，可以快速回滚：

### 1. 移除依赖
```bash
npm uninstall js-yaml react-diff-viewer @types/js-yaml
```

### 2. 恢复代码
```bash
# 使用 git 恢复到之前的版本
git checkout HEAD~1 -- web/src/pages/Release/ConfigCompare/
```

### 3. 重新构建
```bash
npm run build
```

这样可以快速恢复到功能添加前的状态。
