# Application 配置文件字段使用指南

## 新增字段说明

在 Application 创建和编辑页面中，新增了以下 3 个配置文件相关字段：

### 1. 配置文件路径 (config_file_path)
- **用途**: 指定 Git 分支中 YAML 配置文件的路径
- **格式**: `service-name/etc/service-name.yaml`
- **示例**: 
  - `user-service/etc/user-service.yaml`
  - `order-service/config/application.yaml`
  - `payment-service/src/main/resources/application.yml`
- **说明**: 这个路径是相对于 Git 仓库根目录的路径

### 2. ConfigMap 名称 (configmap)
- **用途**: 指定生产环境中对应的 ConfigMap 名称
- **格式**: 通常为 `service-name-config` 或 `service-name-configmap`
- **示例**:
  - `user-service-config`
  - `order-service-configmap`
  - `payment-service-config`
- **说明**: 这是 Kubernetes 集群中实际的 ConfigMap 资源名称

### 3. ConfigMap Key (configmap_key)
- **用途**: 指定 ConfigMap 中存储配置内容的 key
- **格式**: 通常为配置文件名
- **示例**:
  - `application.yaml`
  - `config.yml`
  - `service-config.yaml`
- **说明**: 这是 ConfigMap 的 data 字段中的具体 key

## 使用场景

这些字段主要用于**配置文件对比功能**，帮助开发团队：

1. **发布前对比**: 对比 Git 分支中的配置与生产环境的差异
2. **配置同步**: 确保配置文件的一致性
3. **变更追踪**: 识别配置变更对生产环境的影响

## 配置示例

### 示例 1: 用户服务
```
应用名称: user-service
配置文件路径: user-service/etc/application.yaml
ConfigMap 名称: user-service-config
ConfigMap Key: application.yaml
```

### 示例 2: 订单服务
```
应用名称: order-service
配置文件路径: order-service/config/order-service.yml
ConfigMap 名称: order-service-configmap
ConfigMap Key: config.yml
```

## 页面功能

### 创建/编辑应用页面
- 在"配置文件对比设置"部分填写这 3 个字段
- 所有字段都是可选的，但建议填写完整以使用配置对比功能
- 每个字段都有相应的提示信息和字符长度限制

### 应用列表页面
- 新增了 3 列显示配置文件相关信息
- 支持复制功能，方便查看和使用
- 未配置的字段显示为 "-"

## 注意事项

1. **路径格式**: 配置文件路径使用 Unix 风格的路径分隔符 "/"
2. **命名规范**: 建议 ConfigMap 名称遵循 Kubernetes 命名规范
3. **权限要求**: 确保系统有权限访问指定的 Git 仓库和 Kubernetes ConfigMap
4. **配置验证**: 在使用配置对比功能前，建议先验证配置的正确性

## 故障排除

### 常见问题
1. **配置文件路径错误**: 检查路径是否相对于 Git 仓库根目录
2. **ConfigMap 不存在**: 确认 ConfigMap 在 ecteam 命名空间中存在
3. **ConfigMap Key 错误**: 检查 key 是否在 ConfigMap 的 data 字段中存在

### 验证方法
```bash
# 验证 ConfigMap 是否存在
kubectl get configmap <configmap-name> -n ecteam

# 查看 ConfigMap 内容
kubectl describe configmap <configmap-name> -n ecteam

# 获取特定 key 的内容
kubectl get configmap <configmap-name> -n ecteam -o jsonpath='{.data.<configmap-key>}'
```
