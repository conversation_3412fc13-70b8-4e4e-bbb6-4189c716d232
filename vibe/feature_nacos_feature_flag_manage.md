我有这样一个 nacos 配置
- data-id : feature_control.yaml
- group: common
- namespace : ec

它的内容如下
```yaml
feature_toggle:
  # 全局启用开关（熔断开关）
  enabled: true

  # 所有特性按 namespace 分组
  namespaces:
    # —————————— 独立站项目 ——————————
    ohsome:
      description: 独立站 App 功能开关
      enabled: true  # 可用于快速关闭整个项目的特性
      features:
        funifuni:
          enabled: true
          retail_service:
            is_ready: false
          fun_hint_visible: true


    # —————————— 会员系统 ——————————
    membership:
      description: 会员 App 相关功能控制
      enabled: true
      features:
```
step 1 修改 webapi/internal/svc/ServiceContext.go 增加 feature_control.yaml 的 ConfigManager
step 2 我需要在 webapi/desc/ops.api 上加入它的配置管理需要的 api 定义
step 3 运行 `goctl api go --api=webapi.api --dir=./ --style=goZero` 生成代码
step 4 修改 webapi/internal/logic/ops 目录中新建立的 logic 完成配置管理的业务逻辑
step 4 修改 web/config/routes.ts 加入菜单
step 5 在 web/src/pages/ops 目录下开发配置管理的页面
