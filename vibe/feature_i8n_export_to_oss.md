# xxl-job 导出 android & ios 语言包到 oss 中

1. 只需要写一个 xxl-job 任务就好了。（不是实现下面的完整的技术方案）
2. 语言的导出参考 webapi/internal/logic/i18n/exportTranslationsLogic.go 中的 ExportTranslationsLogic
3. 上传文档到 OSS 可以参考 webapi/internal/logic/ops/getPresignedUrlLogic.go 和 















## 技术方案


Context
目前，客户端（iOS 和 Android）的多语言文本均通过原生资源文件（如 strings.xml、.strings）进行管理。每当需要更新翻译内容（如修正文案、新增语言、调整表达），必须：
- 修改资源文件；
- 重新打包发布 App；
- 用户升级版本后才能看到更新。
存在的问题：
- 发布周期长：一次小文案修改也需要走完整发布流程。
- 响应不及时：紧急文案修改（如合规提示、运营活动）无法快速上线。
- 用户体验差：用户未升级则无法看到最新内容。
- 开发运维成本高：频繁发版增加测试、审核、分发负担。
目标
实现 多语言内容的动态更新能力，使得：
- 翻译内容可独立于 App 版本进行更新
- 客户端无需升级即可获取最新语言包
- 兼容 iOS 与 Android 平台
- 对用户体验无感或低感知
一、技术方案
1.1 整体流程设计
暂时无法在飞书文档外展示此内容

1. App 启动时调用 app/load API。
2. 服务端返回当前语言包版本信息（如：{"lang_version": "2024-04-01", "lang_url": "https://cdn.example.com/langs/v20240401.json"}）。
3. 客户端对比本地缓存的语言包版本。
4. 如果服务端版本更新，则下载新语言包并缓存。
5. 使用最新的语言包渲染 UI。
  

---

1.2  iOS 实现思路

技术方案：
- 使用 Bundle 加载 .strings 文件是传统方式，但不够灵活。
- 推荐使用 **自定义 JSON 语言包 + 本地缓存**，便于动态更新。
  
实现步骤：

1. 首次启动或检查更新时请求 app/load
URLSession.shared.dataTask(with: URL(string: "https://your-api.com/app/load")!) { data, _, _ in
    if let data = data,
       let json = try? JSONDecoder().decode(AppLoadResponse.self, from: data),
       json.langVersion != UserDefaults.standard.string(forKey: "LocalLangVersion") {
        
        // 版本不同，下载新语言包
        self.downloadLanguagePack(from: json.langURL)
    }
}.resume()
  
2. 下载语言包并保存到 Documents 目录
  - 下载 JSON 格式的语言包（如：{"en": {"hello": "Hello"}, "zh": {"hello": "你好"}}）
  - 保存为本地文件（如：LanguagePack.json）
    
3. 读取本地语言包并提供翻译服务
  - 封装一个 LocalizationManager，从 JSON 中读取对应语言的字符串。
    
4. 替换系统 NSLocalizedString（可选）
  - 可以重写 NSLocalizedString 宏或封装一个 L10n.tr("key") 方法。
    
注意事项：
- 权限：iOS 沙盒环境下，可写入 Documents 或 Caches 目录。
- 网络：建议在后台线程下载，避免阻塞启动。
- 更新时机：可在启动时异步检查，不影响首屏渲染。
  

---

1.3 Android 实现思路

技术方案：
- Android 原生使用 res/values-xx/strings.xml，但无法动态更新。
- 推荐：**动态加载 JSON 语言包，存储在 SharedPreferences 或 files 目录
  
实现步骤：

1. 启动时调用 app/load API
CoroutineScope(Dispatchers.IO).launch {
    val response = apiService.loadAppConfig()
    val localVersion = getSharedPreferences("lang", Context.MODE_PRIVATE)
        .getString("version", "")
    
    if (response.langVersion != localVersion) {
        downloadLanguagePack(response.langUrl)
    }
}
  
2. 下载语言包并保存
  - 使用 OkHttp 或 Retrofit 下载 JSON。
  - 保存到 context.filesDir 或 SharedPreferences（小文件可用）。
    
3. 自定义 getString() 方法
object DynamicL10n {
    private var langMap: Map<String, String> = emptyMap()
    
    fun getString(key: String): String {
        return langMap[key] ?: key
    }
    
    fun loadLanguage(context: Context, langCode: String) {
        val json = context.readFile("langs/${langCode}.json")
        langMap = parseJsonToMap(json)
    }
}
  
4. 使用自定义方法替代 @string/xxx
  - 在代码中使用 DynamicL10n.getString("welcome")
  - 布局中无法动态替换，建议复杂场景结合运行时更新文本。
    
注意事项：
- 存储位置：建议用 context.filesDir 或 getExternalFilesDir()。
- 多语言切换：可配合 Configuration 和 Resources 实现运行时语言切换（需重启 Activity）。
- 性能：JSON 解析建议用 Moshi 或 Gson。
  

二、详细设计