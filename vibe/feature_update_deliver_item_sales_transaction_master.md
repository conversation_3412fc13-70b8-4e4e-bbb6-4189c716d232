
## 需求描述
我有这样一个 csv
rel_order_no	sku	subject_code	change_quantity	create_time
sh2505295Y4KBKFT	15040100110018	PT001	-1	2025-05-30 10:30:57

我需要依据它找到 deliver_item 表中对应的记录，并更新其 sales_transaction_master 字段。
sales_transaction_master 的内容是 json 文本

[{"sku": "23070800210003", "quantity": 1, "subjectCode": "109", "transactionLinkCode": "TC2025032500000010-6"}]

如果有重复的 rel_order_no & sku 的记录需要合并处理 sales_transaction_master 然后写入到 deliver_item 表中。(后端 API 进行去重处理。前端不用处理)

## 功能实现
1. 修改 webapi/desc/fulfillment.go 增加一个 maintenance api 接口，接收参数。(对应 csv 单行) 
2. 使用 goctl api go --api=webapi.api --dir=./ --style=goZero 生成后端代码。 然后编辑代码完成 API 逻辑
3. 增加一个前端页面 (web/src/pages/FulfillmentCenter/Maintenance)，提供一个 csv 上传功能(参照 src/pages/i18n/Translation/Lists/index.tsx)。上传后调用后端 API ，每行调用一次 API 接口。
4. 后端处理需要注意重复记录需要合并处理 sales_transaction_master 字段。合并的时候可以使用 map 或者 hash 等等方式来去重。


CREATE TABLE `deliver_item` (
`id` int NOT NULL AUTO_INCREMENT,
`deliver_id` int DEFAULT NULL,
`line_num` smallint DEFAULT NULL,
`sku` varchar(50) DEFAULT NULL,
`quantity` int DEFAULT NULL,
`price` decimal(10,2) DEFAULT NULL,
`tax_rate` float DEFAULT NULL,
`amount` decimal(10,2) DEFAULT NULL,
`tax_amount` decimal(10,2) DEFAULT NULL,
`discount_amount` decimal(10,2) DEFAULT NULL,
`paid_amount` decimal(10,2) DEFAULT NULL,
`currency` varchar(10) DEFAULT NULL,
`product_mark` varchar(32) DEFAULT NULL COMMENT '商品标识',
`sales_transaction_master` json DEFAULT NULL COMMENT '交易链路主体',
`campaign_id` varchar(64) DEFAULT NULL COMMENT '活动ID',
PRIMARY KEY (`id`),
UNIQUE KEY `deliver_item_di_ln` (`deliver_id`,`line_num`)
) ENGINE=InnoDB AUTO_INCREMENT=899932 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci

