# 配置文件对比功能测试指南

## 功能概述

配置文件对比功能用于对比 Git 分支中的 YAML 配置文件与生产环境 ConfigMap 的实际内容，并通过 AI 自动识别差异，防止发布遗漏。

## 测试步骤

### 1. 数据库准备

首先执行数据库迁移脚本：

```sql
-- 执行 webapi/migrations/20250130_add_config_fields_to_applications.sql
ALTER TABLE applications 
ADD COLUMN config_file_path VARCHAR(500) DEFAULT '' COMMENT 'Git 分支中的 YAML 配置文件路径',
ADD COLUMN configmap VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的名称',
ADD COLUMN configmap_key VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的 key';
```

### 2. 配置应用信息

在应用管理页面，为现有应用添加配置文件相关信息：

- **config_file_path**: Git 分支中的配置文件路径，例如：`service-name/etc/service-name.yaml`
- **configmap**: 生产环境 ConfigMap 名称，例如：`service-name-config`
- **configmap_key**: ConfigMap 中的 key，例如：`application.yaml`

### 3. 后端 API 测试

#### 3.1 获取 Git 分支配置文件内容

```bash
curl -X GET "http://localhost:8888/api/release/applications/1/config-file-content?branch=main" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.2 获取 ConfigMap 内容

```bash
curl -X GET "http://localhost:8888/api/release/applications/1/configmap-content" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.3 AI 配置对比

```bash
curl -X POST "http://localhost:8888/api/release/ai-config-compare" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "git_content": "server:\n  port: 8080\ndatabase:\n  host: localhost",
    "configmap_content": "server:\n  port: 9090\ndatabase:\n  host: prod-db",
    "application_name": "test-service"
  }'
```

### 4. 前端页面测试

1. 访问 `/release/config-compare` 页面
2. 选择一个已配置的应用
3. 选择要对比的分支
4. 点击"获取配置内容"按钮
5. 查看左右两侧的配置文件内容
6. 点击"AI 智能对比"按钮
7. 查看 AI 分析结果

### 5. 预期结果

- 左侧显示 Git 分支中的配置文件内容
- 右侧显示生产环境 ConfigMap 的内容
- AI 分析结果包含：
  - 主要差异点列表
  - 总结说明
  - 更新建议

## 故障排除

### 常见问题

1. **获取配置文件内容失败**
   - 检查应用的 config_file_path 是否正确
   - 确认 GitLab 项目 ID 和分支名称正确
   - 验证 GitLab 访问权限

2. **获取 ConfigMap 内容失败**
   - 检查 configmap 和 configmap_key 配置
   - 确认 Kubernetes 集群连接正常
   - 验证 ConfigMap 是否存在于 ecteam 命名空间

3. **AI 对比失败**
   - 检查 DeepSeek API 配置
   - 确认 API Key 有效
   - 验证网络连接

### 日志查看

查看后端日志以获取详细错误信息：

```bash
tail -f webapi/logs/webapi.log
```

## 功能扩展

未来可以考虑的功能扩展：

1. 支持多种配置文件格式（JSON、TOML 等）
2. 配置文件版本历史对比
3. 批量应用配置对比
4. 配置差异可视化图表
5. 自动生成配置更新脚本
