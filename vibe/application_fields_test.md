# Application 配置字段功能测试

## 测试目标
验证 Edit Application 和 Create Application 页面中新增的 3 个配置文件字段功能正常。

## 测试步骤

### 1. 数据库准备
确保已执行数据库迁移脚本：
```sql
-- 检查字段是否存在
DESCRIBE applications;

-- 如果字段不存在，执行迁移
ALTER TABLE applications 
ADD COLUMN config_file_path VARCHAR(500) DEFAULT '' COMMENT 'Git 分支中的 YAML 配置文件路径',
ADD COLUMN configmap VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的名称',
ADD COLUMN configmap_key VARCHAR(255) DEFAULT '' COMMENT '生产环境 ConfigMap 的 key';
```

### 2. 创建应用测试

#### 2.1 访问创建页面
- 访问 `/release/applications/create`
- 确认页面正常加载

#### 2.2 填写表单
填写以下测试数据：
```
基本信息:
- 应用名称: test-config-app
- 应用描述: 测试配置文件字段的应用

GitLab 仓库配置:
- 选择任意可用的 GitLab 仓库

Jenkins 任务配置:
- 选择任意可用的 Jenkins Job

配置文件对比设置:
- 配置文件路径: test-service/etc/application.yaml
- ConfigMap 名称: test-service-config
- ConfigMap Key: application.yaml

其他配置:
- 是否激活: 启用
```

#### 2.3 提交验证
- 点击"创建"按钮
- 确认创建成功并跳转到列表页面

### 3. 编辑应用测试

#### 3.1 访问编辑页面
- 在应用列表中点击刚创建的应用的"编辑"按钮
- 确认页面正常加载且表单已填充现有数据

#### 3.2 修改配置字段
修改配置文件相关字段：
```
配置文件对比设置:
- 配置文件路径: test-service/config/test-config.yml
- ConfigMap 名称: test-service-configmap
- ConfigMap Key: config.yml
```

#### 3.3 保存验证
- 点击"更新"按钮
- 确认更新成功并跳转到列表页面

### 4. 列表页面验证

#### 4.1 检查新列显示
在应用列表页面确认：
- "配置文件路径"列正确显示
- "ConfigMap"列正确显示
- "ConfigMap Key"列正确显示
- 未配置的应用显示"-"

#### 4.2 复制功能测试
- 点击配置字段的复制按钮
- 确认内容正确复制到剪贴板

### 5. API 测试

#### 5.1 获取应用详情
```bash
curl -X GET "http://localhost:8888/api/release/applications/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

验证响应包含新字段：
```json
{
  "data": {
    "id": 1,
    "name": "test-config-app",
    "config_file_path": "test-service/config/test-config.yml",
    "configmap": "test-service-configmap",
    "configmap_key": "config.yml",
    ...
  }
}
```

#### 5.2 创建应用 API
```bash
curl -X POST "http://localhost:8888/api/release/applications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "api-test-app",
    "description": "API 测试应用",
    "gitlab_project_id": 1,
    "gitlab_repo_name": "test-repo",
    "gitlab_repo_url": "https://gitlab.example.com/test-repo",
    "*********************": "test-job",
    "config_file_path": "api-test/etc/config.yaml",
    "configmap": "api-test-config",
    "configmap_key": "config.yaml",
    "is_active": true
  }'
```

#### 5.3 更新应用 API
```bash
curl -X PUT "http://localhost:8888/api/release/applications/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "updated-app",
    "config_file_path": "updated-service/config/app.yml",
    "configmap": "updated-service-config",
    "configmap_key": "app.yml"
  }'
```

## 预期结果

### 前端页面
- ✅ 创建页面显示配置文件字段
- ✅ 编辑页面正确加载和保存配置字段
- ✅ 列表页面显示新的配置列
- ✅ 表单验证正常工作
- ✅ 字段提示信息正确显示

### 后端 API
- ✅ 创建应用时保存配置字段
- ✅ 更新应用时修改配置字段
- ✅ 获取应用时返回配置字段
- ✅ 字段长度验证正常

### 数据库
- ✅ 新字段正确存储
- ✅ 默认值处理正确
- ✅ 字符编码正常

## 故障排除

如果遇到问题，检查以下方面：

1. **数据库字段**: 确认新字段已正确添加
2. **前端类型**: 确认 TypeScript 类型定义已更新
3. **API 接口**: 确认后端 API 正确处理新字段
4. **表单验证**: 检查字段长度限制和必填验证

## 后续测试

完成基本功能测试后，可以进行：
1. 配置文件对比功能的端到端测试
2. 与 GitLab 和 Kubernetes 的集成测试
3. 性能和并发测试
