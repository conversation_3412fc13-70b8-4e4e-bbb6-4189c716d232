对比 Git 分支中的 YAML 配置文件 与 生产环境 ConfigMap 的实际内容，自动识别差异，防止发布遗漏。

## 后端

1. 修改 webapi/repository/application.go model 增加 config_file_path 字段和 configmap, configmap_key 字段
- config_file_path 是 Git 分支中的 YAML 配置文件路径, 通常是 ${service_name}$/etc/${service_name}$.yaml
- configmap 是生产环境 ConfigMap 的名称
- configmap_key 是生产环境 ConfigMap 的 key

2. 修改 webapi/desc/release.api 中增加获取 git 分支中的 YAML 配置文件的 API 定义
- 新增 applications/:id/configmap-content  接口，获取 git 分支中的 YAML 配置文件的内容
- 新增 applications/:id/config-file-content 接口，获取特定发布分支的 YAML 配置文件的内容
- 修改 applications 相关的其他 API 增加新的字段: config_file_path, configmap, configmap_key

3. 如何获取 git 分支中的 YAML 配置文件的内容？
- 在 webapi/internal/third/gitlab/gitlab_service.go 增加对应的函数来实现

4. 如何获取 configmap 的实际内容？
- 参考 webapi/internal/logic/release/getConfigMapContentLogic.go 的实现


## 后端 AI 
1. 修改 webapi/desc/release.api 中增加 AI 的 API 定义
- 新增 /ai-conifig-compare 接口，对比 git 分支中的 YAML 配置文件 与 生产环境 ConfigMap 的实际内容，自动识别差异，防止发布遗漏。

## 前端

在 web/config/routes.ts 中增加对应的菜单. 作为 release 的子菜单. 菜单名称为 "配置文件对比"
1. 列出所有的 applications 调用 get /applications  接口
2. 点击 application 让用户选择它对应的 gitlab branch 
3. 调用 applications/:id/config-file-content 接口，获取 git 分支中的 YAML 配置文件的内容
4. 调用 applications/:id/configmap-content 接口，获取 configmap 的实际内容
5. 对比 git 分支中的 YAML 配置文件 与 生产环境 ConfigMap 的实际内容，自动识别差异，防止发布遗漏。
6. 调用 API 接口 /ai-conifig-compare (AI) 它会给出一个差异报告，告知用户本次版本发布，可能需要更新哪些生产环境 ConfigMap 的实际内容。
