# 配置文件对比功能测试示例

## 测试数据准备

为了测试新的排序和 diff 功能，可以使用以下示例配置：

### 示例 1: Git 分支配置文件
```yaml
# 模拟 Git 分支中的配置文件内容
server:
  port: 8080
  name: user-service
  timeout: 30s

database:
  host: localhost
  port: 3306
  username: admin
  password: secret
  pool:
    max: 10
    min: 2

redis:
  host: redis-server
  port: 6379
  database: 0

logging:
  level: INFO
  file: /var/log/app.log

features:
  enable_cache: true
  enable_metrics: true
  new_feature: true

app:
  version: 2.0.0
  debug: false
```

### 示例 2: 生产环境 ConfigMap
```yaml
# 模拟生产环境 ConfigMap 中的配置内容
app:
  version: 1.9.0
  debug: false

database:
  host: prod-db-server
  port: 3306
  username: admin
  password: prod-secret
  pool:
    max: 20
    min: 5

features:
  enable_cache: true
  enable_metrics: false

logging:
  level: WARN
  file: /var/log/app.log

redis:
  host: redis-cluster
  port: 6379
  database: 1

server:
  port: 8080
  name: user-service
  timeout: 60s
```

## 预期测试结果

### 字段排序功能测试

#### 排序前（原始顺序）
- Git 配置: server → database → redis → logging → features → app
- ConfigMap: app → database → features → logging → redis → server

#### 排序后（字母顺序）
两个配置文件都会按以下顺序排列：
- app → database → features → logging → redis → server

### 差异识别测试

使用上述示例数据，系统应该能识别出以下差异：

#### 🟢 新增项（Git 中有，ConfigMap 中没有）
```yaml
features:
  new_feature: true  # 新功能开关
```

#### 🔴 删除项（ConfigMap 中有，Git 中没有）
暂无（两个配置结构相同）

#### 🟡 修改项（值不同）
```yaml
app:
  version: 2.0.0 vs 1.9.0  # 版本升级

database:
  host: localhost vs prod-db-server  # 数据库主机
  password: secret vs prod-secret    # 数据库密码
  pool:
    max: 10 vs 20  # 连接池最大值
    min: 2 vs 5    # 连接池最小值

features:
  enable_metrics: true vs false  # 指标开关

logging:
  level: INFO vs WARN  # 日志级别

redis:
  host: redis-server vs redis-cluster  # Redis 主机
  database: 0 vs 1  # Redis 数据库

server:
  timeout: 30s vs 60s  # 超时时间
```

## 测试步骤

### 1. 准备测试环境

#### 1.1 创建测试应用
在应用管理中创建一个测试应用，配置：
```
应用名称: config-test-app
配置文件路径: test-app/config/application.yaml
ConfigMap 名称: test-app-config
ConfigMap Key: application.yaml
```

#### 1.2 准备测试数据
- 在 Git 仓库中创建配置文件，内容使用示例 1
- 在 Kubernetes 中创建 ConfigMap，内容使用示例 2

### 2. 功能测试

#### 2.1 基础对比测试
1. 访问配置对比页面
2. 选择测试应用和分支
3. 点击"获取配置内容"
4. 验证两侧内容正确显示

#### 2.2 字段排序测试
1. **开启排序**:
   - 打开排序开关
   - 观察两侧配置字段是否按字母顺序排列
   - 验证嵌套对象（如 database.pool）也被排序

2. **关闭排序**:
   - 关闭排序开关
   - 观察配置恢复原始顺序
   - 验证内容完整性

#### 2.3 视图模式测试
1. **并排显示模式**:
   - 选择"并排显示"
   - 验证左右分栏正常
   - 测试独立滚动功能
   - 检查语法高亮效果

2. **差异对比模式**:
   - 选择"差异对比"
   - 验证差异高亮显示
   - 检查颜色标识正确性
   - 测试统一滚动功能

#### 2.4 差异识别测试
在差异对比模式下，验证以下差异被正确标识：

1. **新增内容**（绿色）:
   - `features.new_feature: true`

2. **修改内容**（黄色背景）:
   - `app.version`: 1.9.0 → 2.0.0
   - `database.host`: prod-db-server → localhost
   - `database.password`: prod-secret → secret
   - `database.pool.max`: 20 → 10
   - `database.pool.min`: 5 → 2
   - `features.enable_metrics`: false → true
   - `logging.level`: WARN → INFO
   - `redis.host`: redis-cluster → redis-server
   - `redis.database`: 1 → 0
   - `server.timeout`: 60s → 30s

### 3. AI 分析测试
1. 点击"AI 智能对比"按钮
2. 验证 AI 分析结果包含：
   - 差异总结
   - 主要变更点列表
   - 更新建议

### 4. 边界情况测试

#### 4.1 空配置测试
- 测试一侧为空的情况
- 测试两侧都为空的情况

#### 4.2 格式错误测试
- 测试 YAML 格式错误的配置
- 验证错误处理和降级显示

#### 4.3 大文件测试
- 测试较大的配置文件（>1000 行）
- 验证性能和响应速度

## 验收标准

### 功能正确性
- ✅ 字段排序功能正常工作
- ✅ 视图模式切换正常
- ✅ 差异识别准确
- ✅ 颜色标识正确

### 用户体验
- ✅ 界面响应流畅
- ✅ 操作逻辑清晰
- ✅ 错误处理友好
- ✅ 视觉效果良好

### 性能要求
- ✅ 配置加载时间 < 3秒
- ✅ 排序处理时间 < 1秒
- ✅ 视图切换时间 < 0.5秒
- ✅ 内存使用合理

## 故障排除

### 常见问题及解决方案

1. **排序不生效**
   - 检查 YAML 格式是否正确
   - 查看浏览器控制台错误信息
   - 尝试关闭排序使用原始内容

2. **差异显示异常**
   - 刷新页面重新加载
   - 切换视图模式
   - 检查配置内容是否过大

3. **性能问题**
   - 减少配置文件大小
   - 关闭字段排序功能
   - 使用并排显示模式
