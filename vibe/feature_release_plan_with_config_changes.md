# Feature Release Plan with Config Changes

### 后端
1. 修改 webapi/internal/repository/release_application_tasks.go 增加一个 config_data 字段，string 类型，用于保存该 application 发布时需要新增的配置信息
2. 修改 webapi/internal/repository/release_plan.go 增加一个 发布手册 urls 的字段，string 类型，用于保存该 release 的发布手册 urls
3. 修改 webapi/desc/release.api 文件,修改 release_plan create & update 的新增的请求参数
4. 运行 goctl api go --api=webapi.api --dir=./ --style=goZero 生成代码
5. 修改 webapi/internal/logic/release/ 中相关的 Logic 文件

### 前端
1. 修改 web/src/pages/Release/ReleasePlan/CreateReleasePlan.tsx & ReleasePlanDetail.tsx 加入新增的字段
2. 在配置 config_data 的时候，参照web/src/pages/Release/ConfigCompare/index.tsx 的配置方式, 弹窗展示 diff ，然后让用户参考 diff 配置 config_data