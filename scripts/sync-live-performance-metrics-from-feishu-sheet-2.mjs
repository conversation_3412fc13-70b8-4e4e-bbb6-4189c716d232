import fs from 'fs';
import crypto from 'crypto';
import https from 'https';


const app_id = 'cli_a7d56bbafb7e100b';
const app_secret = '0pZBo8iFDwb9veX27Y22ygVdRfEbNHoQ';
const bitableId = 'KJPhbD1r5aOccbs1X5Ic20cwnSd';
const tableId = 'tblMVOaBnhI4jpvW';

const feishu_sheets = [
    { name: "Data Register  - Oh!SomeBeauty.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'sr1F8D' },
    { name: "Data Register  - BricksWorld.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '8UQa4o' },
    { name: "Data Register  - Oh!SomeCollections.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'or5hE6' },
    { name: "Data Register  - Oh!SomeCoolToys.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'BRBLHC' },
    { name: "Data Register  - Oh!SomeFunHouse.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'j1F7KN' },
    { name: "Data Register  - Oh!SomeHotselling.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'eYus5k' },
    { name: "Data Register  - Oh!SomeLovelyToys.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '2POo61' },
    { name: "Data Register  - Oh!SomeScents.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'TTbmdA' },
    { name: "Data Register  - Oh!SomeTravel.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'eTPqHt' },
    { name: "Data Register  - Oh!SomeTrends.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'hmxEJs' },
    { name: "Data Register  - Oh!SomeUnboxFun.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'v3IN5F' },
    { name: "Data Register  - Oh!SomeSkincare.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'WMM4Vl' },
    { name: "Data Register  - Yinerle.ID.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'J01MFO' },
    { name: "Data Register  - Sembo.ID.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '4ceD6t' },
    { name: "Data Register  - Blokees.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'z4cUCN' },
    { name: "Data Register  - Oh!SomePlushieWorld.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'UupIrA' },
    { name: "Data Register  - OhSomeTrendyHat.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'FCnzfp' },
    { name: "Data Register  - Oh!SomeMom.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '1reNf4' },
    { name: "Data Register  - Oh!SomeMakeup.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'EVGBAy' },
    { name: "Data Register  - ArtZone.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'ymh7QZ' },
    { name: "Data Register  - Oh!SomeTreasures.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '7e614q' },
    { name: "Data Register  - Oh!SomeStarPicks.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'w2I7td' },
    { name: "Data Register  - HomeEssentials.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'yj10TX' },
    // Wonderland (1OpSPZ) ,SoloCorner(3wOZiE) ,Home Goods (WVzGzy),CCCOVE (AEkaw2),Office Life(unSOqr)
    { name: "Data Register  - Oh!SomeWonderLand.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '1OpSPZ' },
    { name: "Data Register  - SoloCornerOfficial.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: '3wOZiE' },
    { name: "Data Register  - Oh!SomeHomeGoods.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'WVzGzy' },
    { name: "Data Register  - CCCOVEOfficial.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'AEkaw2' },
    { name: "Data Register  - Oh!SomeOfficeLife.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'unSOqr' },
    { name: "Data Register  - OhSomeFunSip.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'lPIVUP' },
    { name: "Data Register  - OhSomeDailyTech.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'ApSRzs' },
    { name: "Data Register  - OhSomeCook&Go.csv", spreadsheet_token: 'AVd6sVgp4hiq5Rt2M7pcviOanMf', sheet_id: 'lpSEJu' },
];

const httpsRequest = (options, data = null) => {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve(JSON.parse(body));
            });
        });
        req.on('error', (e) => {
            reject(e);
        });
        if (data) {
            req.write(data);
        }
        req.end();
    });
};

const serialDateToUnixTimestamp = (serial) => {
    try {
        const excelEpoch = new Date(Date.UTC(1899, 11, 30)).getTime();
        const timestamp = excelEpoch + serial * 86400000;
        const timestring = new Date(timestamp).toISOString().replace('T', ' ').replace('Z', 'UTC+7');
        return new Date(timestring).getTime();
    }catch(e){
        //console.log(serial);
        // 如果是 string 包含 : 尝试按照时间来处理
        if (typeof serial === 'string' && serial.includes(':')) {
            return new Date(serial).getTime();
        }
        console.log(serial);
    }

};
const getFeishuToken = async ({app_id, app_secret}) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: '/open-apis/auth/v3/app_access_token/internal',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    };
    const data = JSON.stringify({ app_id, app_secret });
    const response = await httpsRequest(options, data);
    return response.tenant_access_token;
};

const getSpreadsheetData = async ({ sheet, token }) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: `/open-apis/sheets/v2/spreadsheets/${sheet.spreadsheet_token}/values/${sheet.sheet_id}!A2:Y`,
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    };
    const response = await httpsRequest(options);
    return response.data.valueRange.values;
};

const getMonthFromUnixTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1}`;
}

// week 的格式为 2021-w01 代表第 2021 第一周,  2021-w50  代表第 2021 第50 周
const getWeekFromUnixTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    // 设置为当前日期所在周的周四
    const targetDate = new Date(date.getTime());
    targetDate.setHours(0, 0, 0, 0); // 清除时间部分
    targetDate.setDate(targetDate.getDate() + 3 - ((targetDate.getDay() + 6) % 7)); // ISO 周从周一开始
    // 获取目标年份和第 1 天
    const firstThursday = new Date(targetDate.getFullYear(), 0, 4);
    firstThursday.setHours(0, 0, 0, 0);
    firstThursday.setDate(firstThursday.getDate() + 3 - ((firstThursday.getDay() + 6) % 7));
    // 计算第几周
    const week = Math.floor((targetDate - firstThursday) / 604800000) + 1; // 一周的毫秒数：604800000
    // 返回结果
    return `${targetDate.getFullYear()}-w${week < 10 ? '0' + week : week}`;
};

const getDuration = (cell)=> {
    // 格式可能是 string 的 "9*3600", 也可能是 number 的 32400， 需要转换为 number, 规则是 a*b = a x b
    if (typeof cell === 'string' && cell.includes('*')) {
        const [a, b] = cell.split('*').map(Number);
        return a * b;
    }
    return parseFloat(cell) || 0;
}


const mapSpreadsheetData = ({values, liveRoom}) => {
    // map 每个字段
    const jsonData = values.filter(item => item[5] && item[1]).map(item => ({
        'Shop': liveRoom,
        'Live room': liveRoom,
        'Shift': item[0],
        'Host': item[1],
        'Session': parseFloat(item[2]) || 0,
        'Assistant': item[3],
        'Livestream title': item[4],
        // 值为 日期序数，需要转换为 11 位的 unix 时间戳
        'Start time': serialDateToUnixTimestamp(item[5]),
        'Start date': serialDateToUnixTimestamp(item[5]),
        // month 是 YYYY-MM 格式
        'Start month': getMonthFromUnixTimestamp(serialDateToUnixTimestamp(item[5])),
        'Start week': getWeekFromUnixTimestamp(serialDateToUnixTimestamp(item[5])),
        'Duration': getDuration(item[6]),
        'Duration hours': getDuration(item[6]) / 3600,
        'Gross revenue': parseFloat(item[7]) || 0,
        'Direct GMV': parseFloat(item[8]) || 0,
        'Direct GMV RMB': parseFloat(item[8])/2000 || 0,
        'Items sold': parseFloat(item[9]) || 0,
        'Buyers': parseFloat(item[10]) || 0,
        // 这个字段不用
        // 'Average price' : item[11],
        'Orders paid for': parseFloat(item[12]) || 0,
        'Show GPM': parseFloat(item[13]) || 0,
        'Watch GPM': parseFloat(item[14]) || 0,
        'Views': parseFloat(item[15]) || 0,
        'Viewers': parseFloat(item[16]) || 0,
        'PCU': parseFloat(item[17]) || 0,
        'New followers': parseFloat(item[18]) || 0,
        'AVD': parseFloat(item[19]) || 0,
        'Likes': parseFloat(item[20]) || 0,
        'Comments': parseFloat(item[21]) || 0,
        'Shares': parseFloat(item[22]) || 0,
        'Product impressions': parseFloat(item[23]) || 0,
        'Product clicks': parseFloat(item[24]) || 0,
    }));

    // 对数据按照时间进行分组处理，相同时间的为一组
    const grouped = jsonData.reduce((acc, item) => {
        const key = `${item['Live room']} - ${item['Start time']}`;
        acc[key] = acc[key] || [];
        acc[key].push(item);
        return acc;
    }, {});

    const result = [];
    Object.values(grouped).flatMap(group => {
        // 对 group 按照 Duration 排序
        group.sort((a, b) => a.Duration - b.Duration);
        
        // 合并相同 Duration 相同的记录
        const mergedGroup = [];
        let current = group[0];
        
        for (let i = 1; i < group.length; i++) {
            if (current.Duration === group[i].Duration) {
                // 合并 Host 和 Assistant
                const hosts = new Set([...current.Host.split(','), ...group[i].Host.split(',')]);
                const assistants = new Set([...current.Assistant.split(','), ...group[i].Assistant.split(',')]);
                current.Host = Array.from(hosts).join(',');
                current.Assistant = Array.from(assistants).join(',');
            } else {
                mergedGroup.push(current);
                current = group[i];
            }
        }
        mergedGroup.push(current);
        // 迭代 mergedGroup 对数值进行上下相减，得到正确的数值
        result.push(Object.assign(mergedGroup[0], {}));
        for (let i = 1; i < mergedGroup.length; i++) {
            // 不改变原始数据
            const prev = Object.assign({}, mergedGroup[i - 1]);
            const curr = Object.assign({}, mergedGroup[i]);
            curr.Duration -= prev.Duration;
            curr['Duration hours'] -= prev['Duration hours'];
            curr['Direct GMV'] -= prev['Direct GMV'];
            curr['Direct GMV RMB'] -= prev['Direct GMV RMB'];
            curr['Items sold'] -= prev['Items sold'];
            curr['Buyers'] -= prev['Buyers'];
            curr['Orders paid for'] -= prev['Orders paid for'];
            curr['Views'] -= prev['Views'];
            curr['Viewers'] -= prev['Viewers'];
            curr['PCU'] -= prev['PCU'];
            curr['New followers'] -= prev['New followers'];
            curr['AVD'] -= prev['AVD'];
            curr['Likes'] -= prev['Likes'];
            curr['Comments'] -= prev['Comments'];
            curr['Shares'] -= prev['Shares'];
            curr['Product impressions'] -= prev['Product impressions'];
            curr['Product clicks'] -= prev['Product clicks'];
            result.push(curr);
        }
        return group;
    });

    return result.flatMap(item => {
        const hosts = item.Host.split(',');
        const assistants = item.Assistant.split(',');
        const count = hosts.length * assistants.length;
        return hosts.flatMap(host => assistants.map(assistant => {
            const newItem = { ...item, Host: host, Assistant: assistant };
            Object.keys(newItem).forEach(key => {
                if (typeof newItem[key] === 'number') {
                    // ignore start time and start date
                    if (key === 'Start time' || key === 'Start date' || key === 'Start month' || key === 'Start week') return;
                    newItem[key] /= count;
                }
            });
            newItem['Interaction rate'] = newItem['Comments'] / newItem['Views'];
            newItem['Hash'] = crypto.createHash('md5').update(JSON.stringify(newItem)).digest('hex');
            return newItem;
        }));
    });
}
const sync2feishu = async ({token}) => {
    const report = {};
    const resultJson = [];
    for (const sheet of feishu_sheets) {
        const liveRoom = sheet.name.replace("Data Register  - ", "").replace(".csv", "");
        const values = await getSpreadsheetData({ sheet, token });
        const items = mapSpreadsheetData({ values, liveRoom });
        resultJson.push(...items);
    }
    const negativeGMV = resultJson.filter(item => item['Direct GMV RMB'] < 0);
    if (negativeGMV.length > 0) {
        let message = `Found ${negativeGMV.length} records with negative GMV\n`;
        message += negativeGMV.map(item => 
            `Shop: ${item['Shop']}, Date: ${new Date(item['Start time']).toLocaleString()}, Host: ${item['Host']}, Session: ${item['Session']}`
        ).join('\n');
        await sendBotMessage({ token, content: message });
    }
    // duration 为负数的时候也需要发送通知
    const negativeDuration = resultJson.filter(item => item['Duration'] < 0);
    if (negativeDuration.length > 0) {
        let message = `Found ${negativeDuration.length} records with negative Duration\n`;
        message += negativeDuration.map(item => 
            `Shop: ${item['Shop']}, Date: ${new Date(item['Start time']).toLocaleString()}, Host: ${item['Host']}, Session: ${item['Session']}, Duration: ${item['Duration']}`
        ).join('\n');
        await sendBotMessage({ token, content: message });
    }

    //fs.writeFileSync(`./feishu-${Date.now()}.json`, JSON.stringify(resultJson, null, 2));
    const records = await getBitableRecords({ bitableId, tableId, token });
    // 把 resultJson 中的数据处理之后写到新的 bitable 表格中
    await sync2feishuGoal({ records: resultJson, token });
    const newRecords = resultJson.filter(item => !records[item.Hash]);
    const toDeletedRecordIds = Object.keys(records).filter(hash => !resultJson.find(item => item.Hash === hash)).map(hash => records[hash]);
    if (toDeletedRecordIds.length > 0) {
        console.log(`删除 ${toDeletedRecordIds.length} 条数据`);
        await deleteBitableRecords({ recordIds: toDeletedRecordIds, bitableId, tableId, token });
    }
    if (newRecords.length > 0) {
        console.log(`新增 ${newRecords.length} 条数据`);
        await createBitableRecords({ records: newRecords, bitableId, tableId, token });
    }
    report.created = newRecords.length;
    report.deleted = toDeletedRecordIds.length;
    return report;
};


// 对 records 进行汇总之后，写入另外一个 bitable 表格中

const sync2feishuGoal = async ({records, token}) => {
    // 获取 新的 bitable 表格中的数据
    const baseGoalTableId = 'tblVVMnggXvarBkB';
    const goalTableId = 'tblUVduoniE95zjw';
    const items = await getGoalBitableRecords({ bitableId, tableId: baseGoalTableId, token });
    //console.log(items);
    // month 作为 key 进行数据的分组
    const grouped = items.reduce((acc, item) => {
        const key = `${item.fields['Month']}`;
        acc[key] = acc[key] || [];
        acc[key].push(item.fields);
        return acc;
    }, {});
    // 对原始的数据 records 也按照 live room 和 month 进行分组
    // month 需要依据 start time 来计算出来
    const result = records.reduce((acc, item) => {
        const date = new Date(item['Start time']);
        // key Month 的格式是 2021-08
        const key = `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1}`;
        acc[key] = acc[key] || [];
        acc[key].push(item);
        return acc;
    }, {});
    // 将 reslut 中的 Direct GMV sum 一下，然后写入到 grouped 的 Actual 字段中
    const goalRecords = [];
    for (const key in result) {
        const sum = result[key].reduce((acc, item) => acc + item['Direct GMV RMB'], 0);
        const group = grouped[key] || [];
        const item = group[0] || {};
        goalRecords.push({
            fields: {
                'Hash': item['Hash'],
                "Month": item['Month'],
                "Goal": Number(item['Goal']),
                "Goal online": Number(item['Goal online']),
                'Actual': sum,
                'Percent': sum / item['Goal'],
            }
        });
    }
    // todo 这里有重复的项目，不能插入重复项目
    // 将 goalRecords 写入到 bitable 表格中

    const items_exists = await getGoalBitableRecords({ bitableId, tableId: goalTableId, token });
    const records_to_created = goalRecords.filter(item => !items_exists.find(exist => exist.fields['Month'] === item.fields['Month']));
    console.log(records_to_created);

    if (records_to_created.length > 0){
        await createGoalBitableRecords({ records: records_to_created, bitableId, tableId: goalTableId, token });
    }
    return records_to_created;
};

const createGoalBitableRecords = async ({ records, bitableId, tableId, token }) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: `/open-apis/bitable/v1/apps/${bitableId}/tables/${tableId}/records/batch_create`,
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` }
    };
    const data = JSON.stringify({
        records,
    });
    const response = await httpsRequest(options, data);
    console.log(response);
    return response;
};




const deleteBitableRecords = async ({ recordIds, bitableId, tableId, token }) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: `/open-apis/bitable/v1/apps/${bitableId}/tables/${tableId}/records/batch_delete`,
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` }
    };
    const data = JSON.stringify({ records: recordIds });
    const response = await httpsRequest(options, data);
    return response;
};

const createBitableRecords = async ({ records, bitableId, tableId, token }) => {
    const batchSize = 1000;
    for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        const options = {
            hostname: 'open.feishu.cn',
            path: `/open-apis/bitable/v1/apps/${bitableId}/tables/${tableId}/records/batch_create`,
            method: 'POST',
            headers: { Authorization: `Bearer ${token}` }
        };
        const data = JSON.stringify({
            records: batch.map(record => ({
                fields: {
                    ...record,
                    'Gross revenue': record['Gross revenue'] || 0,
                    'Direct GMV': record['Direct GMV'] || 0,
                },
            }))
        });
        const response = await httpsRequest(options, data);
        console.log(response);
    }
};

const getBitableRecords = async ({ bitableId, tableId, token }) => {
    let records = {};
    let hasMore = true;
    let pageToken = '';

    while (hasMore) {
        const options = {
            hostname: 'open.feishu.cn',
            path: `/open-apis/bitable/v1/apps/${bitableId}/tables/${tableId}/records?field_names[]=Hash${pageToken ? `&page_token=${pageToken}` : ''}`,
            method: 'GET',
            headers: { Authorization: `Bearer ${token}` }
        };
        const response = await httpsRequest(options);
        if (!response.data.items) break;

        response.data.items.forEach(item => {
            records[item.fields.Hash] = item.record_id;
        });

        hasMore = response.data.has_more;
        pageToken = response.data.page_token;
    }

    return records;
};

const getGoalBitableRecords = async ({ bitableId, tableId, token }) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: `/open-apis/bitable/v1/apps/${bitableId}/tables/${tableId}/records`,
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    };
    const response = await httpsRequest(options);
    return response.data.items || [];
};

const sendBotMessage = async ({ token, content }) => {
    const options = {
        hostname: 'open.feishu.cn',
        path: '/open-apis/im/v1/messages?receive_id_type=chat_id',
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` }
    };
    const data = JSON.stringify({
        receive_id_type: 'chat_id',
        receive_id: process.env.OPENID || 'oc_d0c33406c29e6552117e333b33bf86d7',
        msg_type: 'text',
        content: JSON.stringify({text: content})
    });
    const response = await httpsRequest(options, data);
    return response;
};

let token = '';
try {
    token = await getFeishuToken({app_id, app_secret});
    const {created, deleted} = await sync2feishu({token});
    await sendBotMessage({ token, content: new Date().toISOString() + ' 数据同步成功，新增 ' + created + ' 条数据，删除 ' + deleted + ' 条数据' });
} catch (e) {
    console.error(e);
    await sendBotMessage({ token, content: new Date().toISOString() + ' 数据同步失败' + e.message });
}
