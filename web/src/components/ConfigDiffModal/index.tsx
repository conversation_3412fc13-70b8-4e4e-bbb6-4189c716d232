import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Select,
  Button,
  Row,
  Col,
  Typography,
  message,
  Alert,
  Space,
  Switch,
  Input,
} from 'antd';
import { DiffOutlined, RobotOutlined, SortAscendingOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';
import * as yaml from 'js-yaml';
import ReactDiffViewer from 'react-diff-viewer';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ConfigDiffModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (configData: string) => void;
  applicationId?: number;
  selectedBranch?: string;
  applications?: API.Application[];
}

const ConfigDiffModal: React.FC<ConfigDiffModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  applicationId,
  selectedBranch,
  applications = [],
}) => {
  const [selectedApp, setSelectedApp] = useState<API.Application | null>(null);
  const [currentBranch, setCurrentBranch] = useState<string>('');
  const [gitContent, setGitContent] = useState<string>('');
  const [configMapContent, setConfigMapContent] = useState<string>('');
  const [sortedGitContent, setSortedGitContent] = useState<string>('');
  const [sortedConfigMapContent, setSortedConfigMapContent] = useState<string>('');
  const [aiAnalysis, setAiAnalysis] = useState<API.AIConfigCompareResp | null>(null);
  const [loading, setLoading] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [enableSort, setEnableSort] = useState(true);
  const [configData, setConfigData] = useState<string>('');

  // YAML 排序工具函数
  const sortYamlObject = (obj: any): any => {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sortYamlObject);
    }

    if (typeof obj === 'object') {
      const sortedObj: any = {};
      const sortedKeys = Object.keys(obj).sort();

      for (const key of sortedKeys) {
        sortedObj[key] = sortYamlObject(obj[key]);
      }

      return sortedObj;
    }

    return obj;
  };

  const sortYamlContent = (yamlContent: string): string => {
    try {
      if (!yamlContent.trim()) {
        return yamlContent;
      }

      const parsed = yaml.load(yamlContent);
      const sorted = sortYamlObject(parsed);
      return yaml.dump(sorted, {
        indent: 2,
        lineWidth: -1,
        noRefs: true,
        sortKeys: false,
      });
    } catch (error) {
      console.warn('Failed to parse YAML for sorting:', error);
      return yamlContent;
    }
  };

  // 更新排序后的内容
  useEffect(() => {
    if (enableSort) {
      setSortedGitContent(sortYamlContent(gitContent));
      setSortedConfigMapContent(sortYamlContent(configMapContent));
    } else {
      setSortedGitContent(gitContent);
      setSortedConfigMapContent(configMapContent);
    }
  }, [gitContent, configMapContent, enableSort]);

  // 初始化选中的应用和分支
  useEffect(() => {
    if (visible && applicationId && applications.length > 0) {
      const app = applications.find((a) => a.id === applicationId);
      if (app) {
        setSelectedApp(app);
        // 如果传入了选中的分支，自动设置
        if (selectedBranch) {
          setCurrentBranch(selectedBranch);
        }
      }
    }
  }, [visible, applicationId, selectedBranch, applications]);

  // 重置状态
  useEffect(() => {
    if (visible) {
      // 只有在没有预设分支时才重置分支选择
      if (!selectedBranch) {
        setCurrentBranch('');
      }
      setGitContent('');
      setConfigMapContent('');
      setSortedGitContent('');
      setSortedConfigMapContent('');
      setAiAnalysis(null);
      setConfigData('');
    }
  }, [visible, selectedBranch]);

  // 获取配置文件内容
  const fetchConfigContents = async () => {
    if (!selectedApp || !currentBranch) {
      message.warning('请选择应用和分支');
      return;
    }

    setLoading(true);
    try {
      // 并行获取 Git 配置文件内容和 ConfigMap 内容
      const [gitResponse, configMapResponse] = await Promise.all([
        request(`/api/release/applications/${selectedApp.id}/config-file-content`, {
          params: { branch: currentBranch },
        }),
        request(`/api/release/applications/${selectedApp.id}/configmap-content`),
      ]);

      setGitContent(gitResponse.content || '');
      setConfigMapContent(configMapResponse.content || '');
      message.success('配置文件内容获取成功');
    } catch (error) {
      message.error('获取配置文件内容失败');
    } finally {
      setLoading(false);
    }
  };

  // AI 对比分析
  const performAIAnalysis = async () => {
    if (!gitContent || !configMapContent || !selectedApp) {
      message.warning('请先获取配置文件内容');
      return;
    }

    setAiLoading(true);
    try {
      const response = await request('/api/release/ai-config-compare', {
        method: 'POST',
        data: {
          git_content: gitContent,
          configmap_content: configMapContent,
          application_name: selectedApp.name,
        },
      });

      setAiAnalysis(response);
      message.success('AI 分析完成');
    } catch (error) {
      message.error('AI 分析失败');
    } finally {
      setAiLoading(false);
    }
  };

  const handleConfirm = () => {
    onConfirm(configData);
  };

  return (
    <Modal
      title={
        <Space>
          <DiffOutlined />
          配置差异对比
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      width={1200}
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      okText="确认配置"
      cancelText="取消"
    >
      {/* 选择区域 */}
      {applicationId && selectedBranch ? (
        // 当从应用任务调用时，显示已选择的应用和分支信息
        <div style={{ marginBottom: 16, padding: '12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Text strong>已选择应用：</Text>
              <div>{selectedApp?.name}</div>
            </Col>
            <Col span={6}>
              <Text strong>已选择分支：</Text>
              <div>{currentBranch}</div>
            </Col>
            <Col span={12}>
              <Button
                type="primary"
                onClick={fetchConfigContents}
                loading={loading}
                style={{ marginLeft: 'auto' }}
              >
                获取配置内容
              </Button>
            </Col>
          </Row>
        </div>
      ) : (
        // 当独立使用时，显示选择器
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Text strong>选择应用：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="请选择应用"
              value={selectedApp?.id}
              onChange={(value) => {
                const app = applications.find((a) => a.id === value);
                setSelectedApp(app || null);
                setCurrentBranch('');
                setGitContent('');
                setConfigMapContent('');
                setSortedGitContent('');
                setSortedConfigMapContent('');
                setAiAnalysis(null);
              }}
            >
              {applications.map((app) => (
                <Option key={app.id} value={app.id}>
                  {app.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Text strong>选择分支：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="请选择分支"
              value={currentBranch}
              onChange={setCurrentBranch}
              disabled={!selectedApp}
            >
              {selectedApp?.gitlab_repo_branches?.map((branch) => (
                <Option key={branch.name} value={branch.name}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <div style={{ marginTop: '32px' }}>
              <Button
                type="primary"
                onClick={fetchConfigContents}
                loading={loading}
                disabled={!selectedApp || !currentBranch}
              >
                获取配置内容
              </Button>
            </div>
          </Col>
        </Row>
      )}

      {/* 配置内容对比 */}
      {(gitContent || configMapContent) && (
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={4}>配置差异对比</Title>
            <Space>
              <Text>字段排序:</Text>
              <Switch
                checked={enableSort}
                onChange={setEnableSort}
                checkedChildren={<SortAscendingOutlined />}
                unCheckedChildren="关"
              />
              <Button
                type="primary"
                icon={<RobotOutlined />}
                onClick={performAIAnalysis}
                loading={aiLoading}
                disabled={!gitContent || !configMapContent}
              >
                AI 智能对比
              </Button>
            </Space>
          </div>
          <div style={{ border: '1px solid #d9d9d9', borderRadius: 6 }}>
            <ReactDiffViewer
              oldValue={enableSort ? sortedConfigMapContent : configMapContent}
              newValue={enableSort ? sortedGitContent : gitContent}
              splitView={true}
              leftTitle="生产环境 ConfigMap"
              rightTitle="Git 分支配置文件"
              showDiffOnly={false}
              useDarkTheme={false}
              styles={{
                variables: {
                  light: {
                    codeFoldGutterBackground: '#f7f7f7',
                    codeFoldBackground: '#f1f8ff',
                    diffViewerBackground: '#ffffff',
                    addedBackground: '#f6ffed',
                    removedBackground: '#fff2f0',
                    wordAddedBackground: '#b7eb8f',
                    wordRemovedBackground: '#ffccc7',
                    addedGutterBackground: '#f6ffed',
                    removedGutterBackground: '#fff2f0',
                    gutterBackground: '#fafafa',
                    gutterBackgroundDark: '#f5f5f5',
                    highlightBackground: '#fffbe6',
                    highlightGutterBackground: '#fff1b8',
                  },
                },
                diffContainer: {
                  fontSize: '13px',
                  fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                  lineHeight: '1.5',
                },
                marker: {
                  fontSize: '12px',
                },
              }}
            />
          </div>
        </div>
      )}

      {/* AI 分析结果 */}
      {aiAnalysis && (
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>AI 智能分析结果</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="分析总结"
              description={aiAnalysis.summary}
              type="info"
              showIcon
            />

            <Title level={5}>主要差异点：</Title>
            {aiAnalysis.differences.map((diff, index) => (
              <Alert
                key={index}
                message={`差异 ${index + 1}`}
                description={<pre style={{ whiteSpace: 'pre-wrap' }}>{diff}</pre>}
                type="warning"
                showIcon
                style={{ marginBottom: '8px' }}
              />
            ))}

            <Title level={5}>更新建议：</Title>
            {aiAnalysis.suggestions.map((suggestion, index) => (
              <Alert
                key={index}
                message={`建议 ${index + 1}`}
                description={suggestion}
                type="success"
                showIcon
                style={{ marginBottom: '8px' }}
              />
            ))}
          </Space>
        </div>
      )}

      {/* 配置数据输入 */}
      <div>
        <Text strong>配置数据：</Text>
        <TextArea
          rows={6}
          placeholder="请根据上面的差异对比，输入需要新增的配置信息（YAML格式）"
          value={configData}
          onChange={(e) => setConfigData(e.target.value)}
          style={{ marginTop: 8 }}
        />
      </div>
    </Modal>
  );
};

export default ConfigDiffModal;
