import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Button,
  Space,
  message,
  Spin,
  Typography,
  Switch,
  Collapse,
  Form,
  Input,
  Row,
  Col,
  Divider,
  Tag,
  Popconfirm,
  Modal,
  Select,
  Table,
  Tooltip,
  Descriptions,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { getFeatureControlConfig, updateFeatureControlConfig } from '@/services/ant-design-pro/api';

const { Title, Text } = Typography;
const { Panel } = Collapse;
const { TextArea } = Input;

interface FeatureConfig {
  enabled: boolean;
  [key: string]: any;
}

interface NamespaceConfig {
  description: string;
  enabled: boolean;
  features: Record<string, FeatureConfig>;
}

interface FeatureToggle {
  enabled: boolean;
  namespaces: Record<string, NamespaceConfig>;
}

interface FeatureControlConfig {
  feature_toggle: FeatureToggle;
}

const FeatureControl: React.FC = () => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<FeatureControlConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 新增命名空间相关状态
  const [addNamespaceModalVisible, setAddNamespaceModalVisible] = useState(false);
  const [addNamespaceForm] = Form.useForm();

  // 新增特性相关状态
  const [addFeatureModalVisible, setAddFeatureModalVisible] = useState(false);
  const [addFeatureForm] = Form.useForm();
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');

  // 编辑特性配置相关状态
  const [editFeatureModalVisible, setEditFeatureModalVisible] = useState(false);
  const [editFeatureForm] = Form.useForm();
  const [editingFeature, setEditingFeature] = useState<{
    namespace: string;
    featureName: string;
    config: any;
  } | null>(null);

  // 获取配置
  const fetchConfig = async () => {
    setLoading(true);
    try {
      const response = await getFeatureControlConfig();
      if (response.success) {
        setConfig(response.data);
        // 只设置全局开关的表单值，其他配置通过状态管理
        form.setFieldsValue({
          feature_toggle: {
            enabled: response.data.feature_toggle.enabled
          }
        });
      } else {
        message.error(response.errorMessage || '获取配置失败');
      }
    } catch (error) {
      message.error('获取配置失败');
      console.error('获取特性开关配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfig();
  }, []);

  // 处理全局开关变化
  const handleGlobalToggleChange = (checked: boolean) => {
    if (config) {
      setConfig({
        ...config,
        feature_toggle: {
          ...config.feature_toggle,
          enabled: checked
        }
      });
    }
  };

  // 处理命名空间开关变化
  const handleNamespaceToggleChange = (namespace: string, checked: boolean) => {
    if (config) {
      setConfig({
        ...config,
        feature_toggle: {
          ...config.feature_toggle,
          namespaces: {
            ...config.feature_toggle.namespaces,
            [namespace]: {
              ...config.feature_toggle.namespaces[namespace],
              enabled: checked
            }
          }
        }
      });
    }
  };

  // 处理命名空间描述变化
  const handleNamespaceDescriptionChange = (namespace: string, description: string) => {
    if (config) {
      setConfig({
        ...config,
        feature_toggle: {
          ...config.feature_toggle,
          namespaces: {
            ...config.feature_toggle.namespaces,
            [namespace]: {
              ...config.feature_toggle.namespaces[namespace],
              description
            }
          }
        }
      });
    }
  };

  // 处理特性开关变化
  const handleFeatureToggleChange = (namespace: string, featureName: string, checked: boolean) => {
    if (config) {
      setConfig({
        ...config,
        feature_toggle: {
          ...config.feature_toggle,
          namespaces: {
            ...config.feature_toggle.namespaces,
            [namespace]: {
              ...config.feature_toggle.namespaces[namespace],
              features: {
                ...config.feature_toggle.namespaces[namespace].features,
                [featureName]: {
                  ...config.feature_toggle.namespaces[namespace].features[featureName],
                  enabled: checked
                }
              }
            }
          }
        }
      });
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      setSaving(true);

      // 直接使用当前的 config 状态，因为所有修改都已经同步到 config 中
      if (!config) {
        message.error('配置数据为空');
        return;
      }

      const response = await updateFeatureControlConfig({
        feature_toggle: config.feature_toggle
      });

      if (response.success) {
        message.success('配置保存成功');
        await fetchConfig(); // 重新获取最新配置
      } else {
        message.error(response.errorMessage || '保存配置失败');
      }
    } catch (error) {
      message.error('保存配置失败');
      console.error('保存特性开关配置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 添加新命名空间
  const handleAddNamespace = async () => {
    try {
      const values = await addNamespaceForm.validateFields();
      const currentValues = form.getFieldsValue();

      // 检查命名空间是否已存在
      if (currentValues.feature_toggle?.namespaces?.[values.name]) {
        message.error('命名空间已存在');
        return;
      }

      // 添加新命名空间
      const newNamespaces = {
        ...currentValues.feature_toggle?.namespaces,
        [values.name]: {
          description: values.description,
          enabled: true,
          features: {}
        }
      };

      form.setFieldsValue({
        feature_toggle: {
          ...currentValues.feature_toggle,
          namespaces: newNamespaces
        }
      });

      // 更新本地状态
      if (config) {
        setConfig({
          ...config,
          feature_toggle: {
            ...config.feature_toggle,
            namespaces: {
              ...config.feature_toggle.namespaces,
              [values.name]: {
                description: values.description,
                enabled: true,
                features: {}
              }
            }
          }
        });
      }

      setAddNamespaceModalVisible(false);
      addNamespaceForm.resetFields();
      message.success('命名空间添加成功');
    } catch (error) {
      console.error('添加命名空间失败:', error);
    }
  };

  // 添加新特性
  const handleAddFeature = async () => {
    try {
      const values = await addFeatureForm.validateFields();
      const currentValues = form.getFieldsValue();

      // 检查特性是否已存在
      if (currentValues.feature_toggle?.namespaces?.[selectedNamespace]?.features?.[values.name]) {
        message.error('特性已存在');
        return;
      }

      // 添加新特性
      const newFeatures = {
        ...currentValues.feature_toggle?.namespaces?.[selectedNamespace]?.features,
        [values.name]: {
          enabled: true,
          ...values.config
        }
      };

      const newNamespaces = {
        ...currentValues.feature_toggle?.namespaces,
        [selectedNamespace]: {
          ...currentValues.feature_toggle?.namespaces?.[selectedNamespace],
          features: newFeatures
        }
      };

      form.setFieldsValue({
        feature_toggle: {
          ...currentValues.feature_toggle,
          namespaces: newNamespaces
        }
      });

      // 更新本地状态
      if (config) {
        setConfig({
          ...config,
          feature_toggle: {
            ...config.feature_toggle,
            namespaces: {
              ...config.feature_toggle.namespaces,
              [selectedNamespace]: {
                ...config.feature_toggle.namespaces[selectedNamespace],
                features: {
                  ...config.feature_toggle.namespaces[selectedNamespace].features,
                  [values.name]: {
                    enabled: true,
                    ...values.config
                  }
                }
              }
            }
          }
        });
      }

      setAddFeatureModalVisible(false);
      addFeatureForm.resetFields();
      message.success('特性添加成功');
    } catch (error) {
      console.error('添加特性失败:', error);
    }
  };

  // 编辑特性配置
  const handleEditFeature = (namespace: string, featureName: string, featureConfig: any) => {
    setEditingFeature({ namespace, featureName, config: featureConfig });

    // 设置编辑表单的初始值
    const configWithoutEnabled = { ...featureConfig };
    delete configWithoutEnabled.enabled;

    editFeatureForm.setFieldsValue({
      config: Object.keys(configWithoutEnabled).map(key => ({
        key,
        value: typeof configWithoutEnabled[key] === 'object'
          ? JSON.stringify(configWithoutEnabled[key])
          : String(configWithoutEnabled[key]),
        type: typeof configWithoutEnabled[key] === 'boolean' ? 'boolean' : 'string'
      }))
    });

    setEditFeatureModalVisible(true);
  };

  // 保存特性配置编辑
  const handleSaveFeatureEdit = async () => {
    try {
      const values = await editFeatureForm.validateFields();
      const currentValues = form.getFieldsValue();

      if (!editingFeature) return;

      // 构建新的配置对象
      const newConfig: any = { enabled: editingFeature.config.enabled };

      values.config?.forEach((item: any) => {
        if (item.key && item.value !== undefined) {
          if (item.type === 'boolean') {
            newConfig[item.key] = item.value === 'true' || item.value === true;
          } else {
            try {
              // 尝试解析 JSON
              newConfig[item.key] = JSON.parse(item.value);
            } catch {
              // 如果不是 JSON，就作为字符串处理
              newConfig[item.key] = item.value;
            }
          }
        }
      });

      // 更新表单值
      const newFeatures = {
        ...currentValues.feature_toggle?.namespaces?.[editingFeature.namespace]?.features,
        [editingFeature.featureName]: newConfig
      };

      const newNamespaces = {
        ...currentValues.feature_toggle?.namespaces,
        [editingFeature.namespace]: {
          ...currentValues.feature_toggle?.namespaces?.[editingFeature.namespace],
          features: newFeatures
        }
      };

      form.setFieldsValue({
        feature_toggle: {
          ...currentValues.feature_toggle,
          namespaces: newNamespaces
        }
      });

      // 更新本地状态
      if (config) {
        setConfig({
          ...config,
          feature_toggle: {
            ...config.feature_toggle,
            namespaces: {
              ...config.feature_toggle.namespaces,
              [editingFeature.namespace]: {
                ...config.feature_toggle.namespaces[editingFeature.namespace],
                features: {
                  ...config.feature_toggle.namespaces[editingFeature.namespace].features,
                  [editingFeature.featureName]: newConfig
                }
              }
            }
          }
        });
      }

      setEditFeatureModalVisible(false);
      setEditingFeature(null);
      editFeatureForm.resetFields();
      message.success('特性配置更新成功');
    } catch (error) {
      console.error('保存特性配置失败:', error);
    }
  };

  // 渲染特性配置项
  const renderFeatureConfig = (featureName: string, featureConfig: FeatureConfig, namespaceName: string) => {
    // 准备配置项数据
    const configItems = Object.entries(featureConfig)
      .filter(([key]) => key !== 'enabled')
      .map(([key, value]) => ({
        key,
        value: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
        type: typeof value
      }));

    return (
      <Card
        key={featureName}
        size="small"
        style={{
          marginBottom: 12,
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
        }}
      >
        <Row justify="space-between" align="middle" style={{ marginBottom: configItems.length > 0 ? 12 : 0 }}>
          <Col>
            <Space size="middle">
              <Text strong style={{ fontSize: '14px' }}>{featureName}</Text>
              <Switch
                size="small"
                checked={featureConfig.enabled}
                onChange={(checked) => handleFeatureToggleChange(namespaceName, featureName, checked)}
              />
              <Tag color={featureConfig.enabled ? 'green' : 'red'} size="small">
                {featureConfig.enabled ? '启用' : '禁用'}
              </Tag>
            </Space>
          </Col>
          <Col>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditFeature(namespaceName, featureName, featureConfig)}
            >
              编辑配置
            </Button>
          </Col>
        </Row>

        {configItems.length > 0 && (
          <div style={{
            background: '#fafafa',
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #f0f0f0'
          }}>
            <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
              额外配置:
            </Text>
            {configItems.map(({ key, value, type }) => (
              <div key={key} style={{ marginBottom: '6px' }}>
                <Space size="small" align="start">
                  <Tag
                    color={type === 'boolean' ? 'blue' : type === 'object' ? 'purple' : 'green'}
                    size="small"
                    style={{ minWidth: '50px', textAlign: 'center' }}
                  >
                    {type}
                  </Tag>
                  <Text strong style={{ minWidth: '80px', fontSize: '12px' }}>{key}:</Text>
                  <Text
                    code
                    style={{
                      fontSize: '11px',
                      maxWidth: '300px',
                      wordBreak: 'break-all',
                      whiteSpace: type === 'object' ? 'pre-wrap' : 'normal'
                    }}
                  >
                    {value}
                  </Text>
                </Space>
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  };

  // 渲染命名空间配置
  const renderNamespaceConfig = (namespaceName: string, namespaceConfig: NamespaceConfig) => {
    const featureCount = Object.keys(namespaceConfig.features || {}).length;

    return (
      <Panel
        key={namespaceName}
        header={
          <Row justify="space-between" align="middle" style={{ width: '100%', paddingRight: '40px' }}>
            <Col>
              <Space size="middle">
                <Text strong style={{ fontSize: '16px' }}>{namespaceName}</Text>
                <Tag color={namespaceConfig.enabled ? 'green' : 'red'} style={{ fontSize: '12px' }}>
                  {namespaceConfig.enabled ? '启用' : '禁用'}
                </Tag>
                <Tag color="blue" style={{ fontSize: '12px' }}>{featureCount} 个特性</Tag>
                <Text type="secondary" style={{ fontSize: '13px' }}>{namespaceConfig.description}</Text>
              </Space>
            </Col>
          </Row>
        }
        extra={
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              setSelectedNamespace(namespaceName);
              setAddFeatureModalVisible(true);
            }}
          >
            添加特性
          </Button>
        }
        style={{
          marginBottom: '16px',
          border: '1px solid #d9d9d9',
          borderRadius: '8px'
        }}
      >
        {/* 命名空间基本配置 */}
        <Card size="small" style={{ marginBottom: '16px', background: '#fafafa' }}>
          <Row gutter={[24, 16]}>
            <Col span={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary" style={{ fontSize: '12px' }}>启用状态</Text>
                <Switch
                  checked={namespaceConfig.enabled}
                  onChange={(checked) => handleNamespaceToggleChange(namespaceName, checked)}
                />
              </Space>
            </Col>
            <Col span={16}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>描述</Text>
                <Input
                  value={namespaceConfig.description}
                  onChange={(e) => handleNamespaceDescriptionChange(namespaceName, e.target.value)}
                  placeholder="请输入命名空间描述"
                  size="small"
                />
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 特性配置列表 */}
        <div>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '12px',
            padding: '8px 0',
            borderBottom: '1px solid #f0f0f0'
          }}>
            <Space>
              <Text strong style={{ fontSize: '14px' }}>特性配置</Text>
              <Tooltip title="点击右上角按钮添加新特性">
                <InfoCircleOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
              </Tooltip>
            </Space>
            <Button
              type="dashed"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => {
                setSelectedNamespace(namespaceName);
                setAddFeatureModalVisible(true);
              }}
            >
              添加特性
            </Button>
          </div>

          <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
            {featureCount > 0 ? (
              <Row gutter={[12, 12]}>
                {Object.entries(namespaceConfig.features || {}).map(([featureName, featureConfig]) => (
                  <Col span={24} key={featureName}>
                    {renderFeatureConfig(featureName, featureConfig, namespaceName)}
                  </Col>
                ))}
              </Row>
            ) : (
              <Alert
                message="暂无特性配置"
                description="点击上方的「添加特性」按钮来添加新的特性配置"
                type="info"
                showIcon
                style={{
                  textAlign: 'center',
                  margin: '20px 0',
                  background: '#f6ffed',
                  border: '1px solid #b7eb8f'
                }}
              />
            )}
          </div>
        </div>
      </Panel>
    );
  };

  if (loading) {
    return (
      <PageContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载配置中...</div>
          </div>
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="特性开关管理"
      subTitle="管理系统特性开关配置"
      extra={[
        <Button
          key="reload"
          icon={<ReloadOutlined />}
          onClick={fetchConfig}
          loading={loading}
        >
          刷新
        </Button>,
        <Button
          key="add-namespace"
          icon={<PlusOutlined />}
          onClick={() => setAddNamespaceModalVisible(true)}
        >
          添加命名空间
        </Button>,
        <Popconfirm
          key="save"
          title="确定要保存配置吗？"
          description="保存后配置将立即生效"
          onConfirm={handleSave}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
          >
            保存配置
          </Button>
        </Popconfirm>
      ]}
    >
      <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '0' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 全局开关 */}
          <Card
            title={
              <Space>
                <SettingOutlined style={{ color: '#1890ff' }} />
                <span style={{ fontSize: '16px', fontWeight: 600 }}>全局配置</span>
              </Space>
            }
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}
          >
            <Row align="middle" gutter={[24, 16]}>
              <Col span={6}>
                <Space direction="vertical" size="small">
                  <Text strong>全局特性开关</Text>
                  <Switch
                    checked={config?.feature_toggle?.enabled || false}
                    onChange={handleGlobalToggleChange}
                    size="default"
                  />
                </Space>
              </Col>
              <Col span={18}>
                <Alert
                  message="全局开关控制"
                  description="关闭后所有命名空间和特性开关都将失效，请谨慎操作"
                  type="info"
                  showIcon
                  style={{ background: '#e6f7ff', border: '1px solid #91d5ff' }}
                />
              </Col>
            </Row>
          </Card>

          {/* 命名空间配置 */}
          <Card
            title={
              <Space size="middle">
                <SettingOutlined style={{ color: '#52c41a' }} />
                <span style={{ fontSize: '16px', fontWeight: 600 }}>命名空间配置</span>
                <Tag color="blue" style={{ fontSize: '12px' }}>
                  {Object.keys(config?.feature_toggle?.namespaces || {}).length} 个命名空间
                </Tag>
                <Tag color="green" style={{ fontSize: '12px' }}>
                  {Object.values(config?.feature_toggle?.namespaces || {})
                    .reduce((total, ns) => total + Object.keys(ns.features || {}).length, 0)} 个特性
                </Tag>
              </Space>
            }
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setAddNamespaceModalVisible(true)}
                style={{ borderRadius: '6px' }}
              >
                添加命名空间
              </Button>
            }
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}
          >
            {config?.feature_toggle?.namespaces && Object.keys(config.feature_toggle.namespaces).length > 0 ? (
              <Collapse
                ghost
                expandIconPosition="end"
                style={{ background: 'transparent' }}
              >
                {Object.entries(config.feature_toggle.namespaces).map(([namespaceName, namespaceConfig]) =>
                  renderNamespaceConfig(namespaceName, namespaceConfig)
                )}
              </Collapse>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Alert
                  message="暂无命名空间配置"
                  description="点击右上角的「添加命名空间」按钮来添加新的命名空间"
                  type="info"
                  showIcon
                  style={{
                    background: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: '6px'
                  }}
                />
              </div>
            )}
          </Card>
        </Space>
      </div>

      {/* 添加命名空间弹窗 */}
      <Modal
        title="添加命名空间"
        open={addNamespaceModalVisible}
        onOk={handleAddNamespace}
        onCancel={() => {
          setAddNamespaceModalVisible(false);
          addNamespaceForm.resetFields();
        }}
        okText="添加"
        cancelText="取消"
      >
        <Form form={addNamespaceForm} layout="vertical">
          <Form.Item
            label="命名空间名称"
            name="name"
            rules={[
              { required: true, message: '请输入命名空间名称' },
              { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '名称必须以字母开头，只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="例如: user_service" />
          </Form.Item>
          <Form.Item
            label="描述"
            name="description"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <Input placeholder="例如: 用户服务相关功能控制" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加特性弹窗 */}
      <Modal
        title={`添加特性 - ${selectedNamespace}`}
        open={addFeatureModalVisible}
        onOk={handleAddFeature}
        onCancel={() => {
          setAddFeatureModalVisible(false);
          addFeatureForm.resetFields();
        }}
        okText="添加"
        cancelText="取消"
        width={600}
      >
        <Form form={addFeatureForm} layout="vertical">
          <Form.Item
            label="特性名称"
            name="name"
            rules={[
              { required: true, message: '请输入特性名称' },
              { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '名称必须以字母开头，只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="例如: user_login" />
          </Form.Item>
          <Form.Item label="初始配置（可选）">
            <Form.List name="config">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                      <Form.Item
                        {...restField}
                        name={[name, 'key']}
                        rules={[{ required: true, message: '请输入配置键' }]}
                      >
                        <Input placeholder="配置键" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'type']}
                        rules={[{ required: true, message: '请选择类型' }]}
                      >
                        <Select placeholder="类型" style={{ width: 100 }}>
                          <Select.Option value="string">字符串</Select.Option>
                          <Select.Option value="boolean">布尔值</Select.Option>
                          <Select.Option value="number">数字</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[{ required: true, message: '请输入配置值' }]}
                      >
                        <Input placeholder="配置值" />
                      </Form.Item>
                      <DeleteOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      添加配置项
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑特性配置弹窗 */}
      <Modal
        title={`编辑特性配置 - ${editingFeature?.featureName}`}
        open={editFeatureModalVisible}
        onOk={handleSaveFeatureEdit}
        onCancel={() => {
          setEditFeatureModalVisible(false);
          setEditingFeature(null);
          editFeatureForm.resetFields();
        }}
        okText="保存"
        cancelText="取消"
        width={700}
      >
        <Form form={editFeatureForm} layout="vertical">
          <Form.Item label="配置项">
            <Form.List name="config">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                      <Form.Item
                        {...restField}
                        name={[name, 'key']}
                        rules={[{ required: true, message: '请输入配置键' }]}
                      >
                        <Input placeholder="配置键" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'type']}
                        rules={[{ required: true, message: '请选择类型' }]}
                      >
                        <Select placeholder="类型" style={{ width: 100 }}>
                          <Select.Option value="string">字符串</Select.Option>
                          <Select.Option value="boolean">布尔值</Select.Option>
                          <Select.Option value="number">数字</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'value']}
                        rules={[{ required: true, message: '请输入配置值' }]}
                      >
                        <Input placeholder="配置值" />
                      </Form.Item>
                      <DeleteOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      添加配置项
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default FeatureControl;
