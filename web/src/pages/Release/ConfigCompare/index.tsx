import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Select,
  Button,
  Row,
  Col,
  Typography,
  message,
  Divider,
  Alert,
  Space,
  Switch,
} from 'antd';
import { DiffOutlined, RobotOutlined, SortAscendingOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';
import * as yaml from 'js-yaml';
import ReactDiffViewer from 'react-diff-viewer';
import './index.less';

const { Title, Text } = Typography;
const { Option } = Select;

const ConfigCompare: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedApp, setSelectedApp] = useState<Application | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [gitContent, setGitContent] = useState<string>('');
  const [configMapContent, setConfigMapContent] = useState<string>('');
  const [sortedGitContent, setSortedGitContent] = useState<string>('');
  const [sortedConfigMapContent, setSortedConfigMapContent] = useState<string>('');
  const [aiAnalysis, setAiAnalysis] = useState<AIConfigCompareResp | null>(null);
  const [loading, setLoading] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [enableSort, setEnableSort] = useState(true);

  // YAML 排序工具函数
  const sortYamlObject = (obj: any): any => {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sortYamlObject);
    }

    if (typeof obj === 'object') {
      const sortedObj: any = {};
      const sortedKeys = Object.keys(obj).sort();

      for (const key of sortedKeys) {
        sortedObj[key] = sortYamlObject(obj[key]);
      }

      return sortedObj;
    }

    return obj;
  };

  const sortYamlContent = (yamlContent: string): string => {
    try {
      if (!yamlContent.trim()) {
        return yamlContent;
      }

      const parsed = yaml.load(yamlContent);
      const sorted = sortYamlObject(parsed);
      return yaml.dump(sorted, {
        indent: 2,
        lineWidth: -1,
        noRefs: true,
        sortKeys: false, // 我们已经手动排序了
      });
    } catch (error) {
      console.warn('Failed to parse YAML for sorting:', error);
      return yamlContent; // 如果解析失败，返回原内容
    }
  };

  // 更新排序后的内容
  useEffect(() => {
    if (enableSort) {
      setSortedGitContent(sortYamlContent(gitContent));
      setSortedConfigMapContent(sortYamlContent(configMapContent));
    } else {
      setSortedGitContent(gitContent);
      setSortedConfigMapContent(configMapContent);
    }
  }, [gitContent, configMapContent, enableSort]);

  // 获取应用列表
  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      const response = await request('/api/release/applications');
      setApplications(response.data || []);
    } catch (error) {
      message.error('获取应用列表失败');
    }
  };

  // 获取配置文件内容
  const fetchConfigContents = async () => {
    if (!selectedApp || !selectedBranch) {
      message.warning('请选择应用和分支');
      return;
    }

    setLoading(true);
    try {
      // 并行获取 Git 配置文件内容和 ConfigMap 内容
      const [gitResponse, configMapResponse] = await Promise.all([
        request(`/api/release/applications/${selectedApp.id}/config-file-content`, {
          params: { branch: selectedBranch },
        }),
        request(`/api/release/applications/${selectedApp.id}/configmap-content`),
      ]);

      setGitContent(gitResponse.content || '');
      setConfigMapContent(configMapResponse.content || '');
      message.success('配置文件内容获取成功');
    } catch (error) {
      message.error('获取配置文件内容失败');
    } finally {
      setLoading(false);
    }
  };

  // AI 对比分析
  const performAIAnalysis = async () => {
    if (!gitContent || !configMapContent || !selectedApp) {
      message.warning('请先获取配置文件内容');
      return;
    }

    setAiLoading(true);
    try {
      const response = await request('/api/release/ai-config-compare', {
        method: 'POST',
        data: {
          git_content: gitContent,
          configmap_content: configMapContent,
          application_name: selectedApp.name,
        },
      });

      setAiAnalysis(response);
      message.success('AI 分析完成');
    } catch (error) {
      message.error('AI 分析失败');
    } finally {
      setAiLoading(false);
    }
  };

  return (
    <div className="config-compare" style={{ padding: '24px' }}>
      <Title level={2}>
        <DiffOutlined /> 配置文件对比
      </Title>

      {/* 选择区域 */}
      <Card title="选择应用和分支" style={{ marginBottom: '24px' }}>
        <Row gutter={16}>
          <Col span={8}>
            <Text strong>选择应用：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="请选择应用"
              value={selectedApp?.id}
              onChange={(value) => {
                const app = applications.find((a) => a.id === value);
                setSelectedApp(app || null);
                setSelectedBranch('');
                setGitContent('');
                setConfigMapContent('');
                setSortedGitContent('');
                setSortedConfigMapContent('');
                setAiAnalysis(null);
              }}
            >
              {applications.map((app) => (
                <Option key={app.id} value={app.id}>
                  {app.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <Text strong>选择分支：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="请选择分支"
              value={selectedBranch}
              onChange={setSelectedBranch}
              disabled={!selectedApp}
            >
              {selectedApp?.gitlab_repo_branches?.map((branch) => (
                <Option key={branch.name} value={branch.name}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <div style={{ marginTop: '32px' }}>
              <Button
                type="primary"
                onClick={fetchConfigContents}
                loading={loading}
                disabled={!selectedApp || !selectedBranch}
              >
                获取配置内容
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 配置内容对比 */}
      {(gitContent || configMapContent) && (
        <Card
          title="配置内容对比"
          style={{ marginBottom: '24px' }}
          extra={
            <div className="control-panel">
              <Text>字段排序:</Text>
              <Switch
                className="sort-switch"
                checked={enableSort}
                onChange={setEnableSort}
                checkedChildren={<SortAscendingOutlined />}
                unCheckedChildren="关"
              />
              <Button
                type="primary"
                icon={<RobotOutlined />}
                onClick={performAIAnalysis}
                loading={aiLoading}
                disabled={!gitContent || !configMapContent}
              >
                AI 智能对比
              </Button>
            </div>
          }
        >
          <div className="comparison-container">
            <div className="diff-view">
              <div className="config-header">
                <Title level={4}>配置差异对比</Title>
              </div>
              <div className="diff-container">
                <ReactDiffViewer
                  oldValue={enableSort ? sortedConfigMapContent : configMapContent}
                  newValue={enableSort ? sortedGitContent : gitContent}
                  splitView={true}
                  leftTitle="生产环境 ConfigMap"
                  rightTitle="Git 分支配置文件"
                  showDiffOnly={false}
                  useDarkTheme={false}
                  styles={{
                    variables: {
                      light: {
                        codeFoldGutterBackground: '#f7f7f7',
                        codeFoldBackground: '#f1f8ff',
                        diffViewerBackground: '#ffffff',
                        addedBackground: '#f6ffed',
                        removedBackground: '#fff2f0',
                        wordAddedBackground: '#b7eb8f',
                        wordRemovedBackground: '#ffccc7',
                        addedGutterBackground: '#f6ffed',
                        removedGutterBackground: '#fff2f0',
                        gutterBackground: '#fafafa',
                        gutterBackgroundDark: '#f5f5f5',
                        highlightBackground: '#fffbe6',
                        highlightGutterBackground: '#fff1b8',
                      },
                    },
                    diffContainer: {
                      fontSize: '13px',
                      fontFamily: 'Monaco, Menlo, Ubuntu Mono, monospace',
                      lineHeight: '1.5',
                    },
                    marker: {
                      fontSize: '12px',
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* AI 分析结果 */}
      {aiAnalysis && (
        <Card title="AI 智能分析结果" className="ai-analysis" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="分析总结"
              description={aiAnalysis.summary}
              type="info"
              showIcon
            />

            <Divider />

            <Title level={4}>主要差异点：</Title>
            {aiAnalysis.differences.map((diff, index) => (
              <Alert
                key={index}
                message={`差异 ${index + 1}`}
                description={<pre style={{ whiteSpace: 'pre-wrap' }}>{diff}</pre>}
                type="warning"
                showIcon
                style={{ marginBottom: '8px' }}
              />
            ))}

            <Divider />

            <Title level={4}>更新建议：</Title>
            {aiAnalysis.suggestions.map((suggestion, index) => (
              <Alert
                key={index}
                message={`建议 ${index + 1}`}
                description={suggestion}
                type="success"
                showIcon
                style={{ marginBottom: '8px' }}
              />
            ))}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default ConfigCompare;
