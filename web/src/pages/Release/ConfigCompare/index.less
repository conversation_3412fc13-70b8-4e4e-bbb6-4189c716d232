.config-compare {
  .diff-viewer {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    
    .diff-gutter {
      background-color: #fafafa;
      border-right: 1px solid #e8e8e8;
    }
    
    .diff-line {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
    
    .diff-line-added {
      background-color: #f6ffed;
      border-left: 3px solid #52c41a;
    }
    
    .diff-line-removed {
      background-color: #fff2f0;
      border-left: 3px solid #ff4d4f;
    }
    
    .diff-line-normal {
      background-color: #ffffff;
    }
  }
  
  .sort-switch {
    .ant-switch-checked {
      background-color: #1890ff;
    }
  }
  

  
  .config-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    
    .ant-typography {
      margin-bottom: 0;
    }
  }
  
  .ai-analysis {
    .ant-alert {
      margin-bottom: 16px;
      
      .ant-alert-description {
        white-space: pre-wrap;
        font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.6;
      }
    }
  }
  
  .control-panel {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    
    .ant-typography {
      margin-bottom: 0;
      font-weight: 500;
    }
    
    .ant-switch {
      margin: 0 4px;
    }
  }
  
  .comparison-container {
    min-height: 500px;

    .diff-view {
      .diff-container {
        max-height: 600px;
        overflow: auto;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
      }
    }
  }
}
