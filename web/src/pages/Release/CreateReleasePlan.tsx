import React, { useState, useEffect } from 'react';
import { message, Card, Button, Space, Select, Input } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  PageContainer,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormList,
  ProFormSelect,
  ProTable
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { PlusOutlined, DeleteOutlined, AppstoreOutlined, DatabaseOutlined, DiffOutlined } from '@ant-design/icons';
import {
  createReleasePlan,
  getApplications,
  getApprovalFlows
} from '@/services/ant-design-pro/api';
import ConfigDiffModal from '@/components/ConfigDiffModal';
import { RegionOptions, SystemOptions } from '@/constants/enum';

const CreateReleasePlan: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [applications, setApplications] = useState<API.Application[]>([]);
  const [approvalFlows, setApprovalFlows] = useState<API.ApprovalFlow[]>([]);
  const [selectedApps, setSelectedApps] = useState<{[key: number]: number}>({});
  const [formRef] = ProForm.useForm();
  const navigate = useNavigate();
  const [configDiffModalVisible, setConfigDiffModalVisible] = useState(false);
  const [currentConfigIndex, setCurrentConfigIndex] = useState<number>(-1);
  const [applicationTasks, setApplicationTasks] = useState<any[]>([{
    key: Date.now(),
    application_id: undefined,
    git_branch: undefined,
    config_data: '',
  }]);
  const [dbMigrationTasks, setDbMigrationTasks] = useState<any[]>([{
    key: Date.now() + 1,
    site: undefined,
    regions: [], // 改为数组支持多选
    sql_script: '',
  }]);
  const [relatedLinks, setRelatedLinks] = useState<any[]>([
    { key: Date.now() + 2, type: 'merge_request', url: '', label: '代码合并请求链接' },
    { key: Date.now() + 3, type: 'prd', url: '', label: '产品PRD文档地址' },
    { key: Date.now() + 4, type: 'tech_design', url: '', label: '技术方案文档地址' },
    { key: Date.now() + 5, type: 'release_manual', url: '', label: '发布手册链接' },
  ]);

  // 获取应用列表
  const fetchApplications = async () => {
    try {
      const response = await getApplications({});
      setApplications(response.data || []);
    } catch (error) {
      message.error('获取应用列表失败');
    }
  };

  // 获取审批流程列表
  const fetchApprovalFlows = async () => {
    try {
      const response = await getApprovalFlows({ type: 'release', enabled: 'true' });
      setApprovalFlows(response.list || []);
    } catch (error) {
      message.error('获取审批流程失败');
    }
  };

  useEffect(() => {
    fetchApplications();
    fetchApprovalFlows();
  }, []);

  // 应用任务表格列定义
  const applicationTaskColumns: ProColumns<any>[] = [
    {
      title: '应用',
      dataIndex: 'application_id',
      width: 200,
      renderFormItem: (_, { record, recordKey }) => (
        <Select
          placeholder="请选择应用"
          value={record?.application_id}
          onChange={(value) => {
            let index = applicationTasks.findIndex(task => task.key === recordKey);

            // 如果通过key找不到，尝试通过record对象查找
            if (index === -1 && record) {
              index = applicationTasks.findIndex(task => task.key === record.key);
            }

            // 如果还是找不到，尝试使用recordKey作为数组索引
            if (index === -1 && typeof recordKey === 'number') {
              index = recordKey;
            }

            if (index > -1 && index < applicationTasks.length) {
              const newTasks = [...applicationTasks];
              newTasks[index] = { ...newTasks[index], application_id: value, git_branch: undefined };
              setApplicationTasks(newTasks);
            }
          }}
          options={applications.map(app => ({
            label: app.name,
            value: app.id,
          }))}
        />
      ),
      render: (_, record) => {
        const app = applications.find(a => a.id === record.application_id);
        return app?.name || '-';
      },
    },
    {
      title: 'Git分支',
      dataIndex: 'git_branch',
      width: 200,
      renderFormItem: (_, { record, recordKey }) => {
        const app = applications.find(a => a.id === record?.application_id);
        return (
          <Select
            placeholder="请选择Git分支"
            disabled={!record?.application_id}
            value={record?.git_branch}
            onChange={(value) => {
              let index = applicationTasks.findIndex(task => task.key === recordKey);

              // 如果通过key找不到，尝试通过record对象查找
              if (index === -1 && record) {
                index = applicationTasks.findIndex(task => task.key === record.key);
              }

              // 如果还是找不到，尝试使用recordKey作为数组索引
              if (index === -1 && typeof recordKey === 'number') {
                index = recordKey;
              }

              if (index > -1 && index < applicationTasks.length) {
                const newTasks = [...applicationTasks];
                newTasks[index] = { ...newTasks[index], git_branch: value };
                setApplicationTasks(newTasks);
              }
            }}
            options={app?.gitlab_repo_branches?.map(branch => ({
              label: branch.name,
              value: branch.name,
            })) || []}
          />
        );
      },
    },
    {
      title: '配置数据',
      dataIndex: 'config_data',
      width: 400,
      renderFormItem: (_, { record, recordKey }) => (
        <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-start' }}>
          <Button
            type="primary"
            icon={<DiffOutlined />}
            size="small"
            onClick={() => {
              const index = applicationTasks.findIndex(task => task.key === recordKey);
              setCurrentConfigIndex(index);
              setConfigDiffModalVisible(true);
            }}
            disabled={!record?.application_id || !record?.git_branch}
            style={{ flexShrink: 0 }}
          >
            对比
          </Button>
          <Input.TextArea
            placeholder="请根据配置差异对比，输入需要新增的配置信息（YAML格式）"
            rows={2}
            style={{ flex: 1 }}
            value={record?.config_data}
            onChange={(e) => {
              let index = applicationTasks.findIndex(task => task.key === recordKey);

              // 如果通过key找不到，尝试通过record对象查找
              if (index === -1 && record) {
                index = applicationTasks.findIndex(task => task.key === record.key);
              }

              // 如果还是找不到，尝试使用recordKey作为数组索引
              if (index === -1 && typeof recordKey === 'number') {
                index = recordKey;
              }

              if (index > -1 && index < applicationTasks.length) {
                const newTasks = [...applicationTasks];
                newTasks[index] = { ...newTasks[index], config_data: e.target.value };
                setApplicationTasks(newTasks);
              }
            }}
          />
        </div>
      ),
      render: (text) => text ? (
        <div style={{ maxWidth: 350, wordBreak: 'break-all' }}>
          {text.length > 100 ? `${text.substring(0, 100)}...` : text}
        </div>
      ) : '-',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      render: (_, record, index) => [
        <Button
          key="delete"
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => {
            const newTasks = applicationTasks.filter(task => task.key !== record.key);
            setApplicationTasks(newTasks);
            formRef.setFieldsValue({ application_tasks: newTasks });
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  // 数据库迁移任务表格列定义
  const dbMigrationTaskColumns: ProColumns<any>[] = [
    {
      title: '站点',
      dataIndex: 'site',
      width: 150,
      renderFormItem: (_, { record, recordKey }) => (
        <Select
          placeholder="请选择站点"
          value={record?.site}
          onChange={(value) => {
            let index = dbMigrationTasks.findIndex(task => task.key === recordKey);

            // 如果通过key找不到，尝试通过record对象查找
            if (index === -1 && record) {
              index = dbMigrationTasks.findIndex(task => task.key === record.key);
            }

            // 如果还是找不到，尝试使用recordKey作为数组索引
            if (index === -1 && typeof recordKey === 'number') {
              index = recordKey;
            }

            if (index > -1 && index < dbMigrationTasks.length) {
              const newTasks = [...dbMigrationTasks];
              newTasks[index] = { ...newTasks[index], site: value };
              setDbMigrationTasks(newTasks);
            }
          }}
          options={SystemOptions}
        />
      ),
    },
    {
      title: '区域',
      dataIndex: 'regions',
      width: 200,
      renderFormItem: (_, { record, recordKey }) => (
        <Select
          mode="multiple"
          placeholder="请选择区域（可多选）"
          value={record?.regions || []}
          onChange={(value) => {
            let index = dbMigrationTasks.findIndex(task => task.key === recordKey);

            // 如果通过key找不到，尝试通过record对象查找
            if (index === -1 && record) {
              index = dbMigrationTasks.findIndex(task => task.key === record.key);
            }

            // 如果还是找不到，尝试使用recordKey作为数组索引
            if (index === -1 && typeof recordKey === 'number') {
              index = recordKey;
            }

            if (index > -1 && index < dbMigrationTasks.length) {
              const newTasks = [...dbMigrationTasks];
              newTasks[index] = { ...newTasks[index], regions: value };
              setDbMigrationTasks(newTasks);
            }
          }}
          options={RegionOptions}
        />
      ),
      render: (_, record) => {
        const regions = record.regions || [];
        if (regions.length === 0) return '-';
        const regionLabels = regions.map((region: string) => {
          const option = RegionOptions.find(opt => opt.value === region);
          return option?.label || region;
        });
        return regionLabels.join(', ');
      },
    },
    {
      title: 'SQL脚本',
      dataIndex: 'sql_script',
      renderFormItem: (_, { record, recordKey }) => (
        <Input.TextArea
          placeholder="请输入SQL脚本内容"
          rows={3}
          value={record?.sql_script}
          onChange={(e) => {
            let index = dbMigrationTasks.findIndex(task => task.key === recordKey);

            // 如果通过key找不到，尝试通过record对象查找
            if (index === -1 && record) {
              index = dbMigrationTasks.findIndex(task => task.key === record.key);
            }

            // 如果还是找不到，尝试使用recordKey作为数组索引
            if (index === -1 && typeof recordKey === 'number') {
              index = recordKey;
            }

            if (index > -1 && index < dbMigrationTasks.length) {
              const newTasks = [...dbMigrationTasks];
              newTasks[index] = { ...newTasks[index], sql_script: e.target.value };
              setDbMigrationTasks(newTasks);
            }
          }}
        />
      ),
      render: (text) => text ? (
        <div style={{ maxWidth: 400, wordBreak: 'break-all' }}>
          {text.length > 100 ? `${text.substring(0, 100)}...` : text}
        </div>
      ) : '-',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 80,
      render: (_, record) => [
        <Button
          key="delete"
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => {
            const newTasks = dbMigrationTasks.filter(task => task.key !== record.key);
            setDbMigrationTasks(newTasks);
            formRef.setFieldsValue({ db_migration_tasks: newTasks });
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  // 相关链接表格列定义
  const relatedLinksColumns: ProColumns<any>[] = [
    {
      title: '链接类型',
      dataIndex: 'label',
      width: 200,
      editable: false,
      render: (text) => <span style={{ fontWeight: '500' }}>{text}</span>,
    },
    {
      title: '链接地址',
      dataIndex: 'url',
      renderFormItem: (_, { record, recordKey }) => (
        <Input
          placeholder={`请输入${record?.label || '链接地址'}`}
          value={record?.url}
          onChange={(e) => {
            let index = relatedLinks.findIndex(link => link.key === recordKey);

            // 如果通过key找不到，尝试通过record对象查找
            if (index === -1 && record) {
              index = relatedLinks.findIndex(link => link.key === record.key);
            }

            // 如果还是找不到，尝试使用recordKey作为数组索引
            if (index === -1 && typeof recordKey === 'number') {
              index = recordKey;
            }

            if (index > -1 && index < relatedLinks.length) {
              const newLinks = [...relatedLinks];
              newLinks[index] = { ...newLinks[index], url: e.target.value };
              setRelatedLinks(newLinks);
            }
          }}
        />
      ),
      render: (text) => text ? (
        <a href={text} target="_blank" rel="noopener noreferrer" style={{ wordBreak: 'break-all' }}>
          {text.length > 60 ? `${text.substring(0, 60)}...` : text}
        </a>
      ) : '-',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (_, record) => [
        <Button
          key="add"
          type="link"
          icon={<PlusOutlined />}
          onClick={() => {
            const newLink = {
              key: Date.now(),
              type: record.type,
              url: '',
              label: record.label,
            };
            const index = relatedLinks.findIndex(link => link.key === record.key);
            const newLinks = [...relatedLinks];
            newLinks.splice(index + 1, 0, newLink);
            setRelatedLinks(newLinks);
          }}
        >
          添加
        </Button>,
        relatedLinks.filter(link => link.type === record.type).length > 1 && (
          <Button
            key="delete"
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              const newLinks = relatedLinks.filter(link => link.key !== record.key);
              setRelatedLinks(newLinks);
            }}
          >
            删除
          </Button>
        ),
      ].filter(Boolean),
    },
  ];

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      // 处理URL数组，从ProFormList对象中提取字符串值
      const processUrlArray = (urlArray: any[]): string[] => {
        if (!urlArray || !Array.isArray(urlArray)) return [];
        const urls = urlArray.map(item => {
          if (typeof item === 'string') return item;
          if (typeof item === 'object' && item !== null) {
            // ProFormList返回格式: [{0: "url_value"}]
            const values = Object.values(item);
            return values.find(v => typeof v === 'string' && (v as string).trim() !== '') || '';
          }
          return '';
        }).filter((url) => typeof url === 'string' && url.trim() !== '');
        return urls as string[];
      };

      // 处理ProFormList返回的对象数组，提取实际数据
      const processFormListArray = (formListArray: any[]) => {
        if (!formListArray || !Array.isArray(formListArray)) return [];
        return formListArray.map(item => {
          if (typeof item === 'object' && item !== null) {
            // ProFormList返回的格式是 {"0": {actual_data}}
            const values = Object.values(item);
            return values[0]; // 取第一个值，通常是索引"0"对应的实际数据
          }
          return item;
        }).filter(item => item != null);
      };

      const submitData: API.CreateReleasePlanReq = {
        name: values.name,
        description: values.description,
        approval_flow_id: Number(values.approval_flow_id),
        merge_request_urls: relatedLinks.filter(link => link.type === 'merge_request' && link.url.trim()).map(link => link.url.trim()),
        prd_urls: relatedLinks.filter(link => link.type === 'prd' && link.url.trim()).map(link => link.url.trim()),
        tech_design_urls: relatedLinks.filter(link => link.type === 'tech_design' && link.url.trim()).map(link => link.url.trim()),
        release_manual_urls: relatedLinks.filter(link => link.type === 'release_manual' && link.url.trim()).map(link => link.url.trim()),
        application_tasks: applicationTasks.map(task => ({
          application_id: task.application_id,
          git_branch: task.git_branch,
          config_data: task.config_data || '',
        })).filter(task => task.application_id && task.git_branch),
        db_migration_tasks: dbMigrationTasks.flatMap(task => {
          // 如果没有选择regions或regions为空，返回空数组
          if (!task.regions || task.regions.length === 0) {
            return [];
          }
          // 为每个选中的region创建一个单独的任务
          return task.regions.map((region: string) => ({
            site: task.site,
            region: region,
            sql_script: task.sql_script,
          }));
        }).filter(task => task.site && task.region && task.sql_script),
      };


      await createReleasePlan(submitData);
      message.success('发布计划创建成功');
      navigate('/release/plans/list');
    } catch (error) {
      message.error('创建发布计划失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer
      title="创建发布计划"
      onBack={() => navigate('/release/plans/list')}
    >
      <ProForm
        form={formRef}
        layout="vertical"
        onFinish={handleSubmit}
        submitter={{
          render: (props) => {
            return (
              <Space>
                <Button onClick={() => navigate('/release/plans/list')}>
                  取消
                </Button>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => props.form?.submit?.()}
                >
                  创建发布计划
                </Button>
              </Space>
            );
          },
        }}
      >
        <Card title="基本信息" style={{ marginBottom: 24 }}>
          <ProFormText
            name="name"
            label="发布计划名称"
            placeholder="请输入发布计划名称"
            rules={[{ required: true, message: '请输入发布计划名称' }]}
          />
          
          <ProFormTextArea
            name="description"
            label="描述"
            placeholder="请输入发布计划描述"
            rules={[{ required: true, message: '请输入发布计划描述' }]}
          />

          <ProFormSelect
            name="approval_flow_id"
            label="审批流程"
            placeholder="请选择审批流程"
            options={approvalFlows.map(flow => ({
              label: flow.name,
              value: flow.id
            }))}
            rules={[{ required: true, message: '请选择审批流程' }]}
          />
        </Card>

        <Card title="相关链接" style={{ marginBottom: 24 }}>
          <ProTable
            columns={relatedLinksColumns}
            dataSource={relatedLinks}
            rowKey="key"
            pagination={false}
            search={false}
            options={false}
            toolBarRender={false}
            editable={{
              type: 'multiple',
              editableKeys: relatedLinks.map(link => link.key),
              actionRender: () => [],
            }}
          />
        </Card>

        <Card
          title={
            <Space>
              <AppstoreOutlined />
              应用部署任务
            </Space>
          }
          style={{ marginBottom: 24 }}
        >
          <ProTable
            columns={applicationTaskColumns}
            dataSource={applicationTasks}
            rowKey="key"
            pagination={false}
            search={false}
            options={false}
            toolBarRender={() => [
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newTask = {
                    key: Date.now(),
                    application_id: undefined,
                    git_branch: undefined,
                    config_data: '',
                  };
                  const newTasks = [...applicationTasks, newTask];
                  setApplicationTasks(newTasks);
                  formRef.setFieldsValue({ application_tasks: newTasks });
                }}
              >
                添加应用部署任务
              </Button>,
            ]}
            editable={{
              type: 'multiple',
              editableKeys: applicationTasks.map(task => task.key),
              actionRender: () => [],
            }}
          />
        </Card>

        <Card
          title={
            <Space>
              <DatabaseOutlined />
              数据库迁移任务
            </Space>
          }
          style={{ marginBottom: 24 }}
        >
          <ProTable
            columns={dbMigrationTaskColumns}
            dataSource={dbMigrationTasks}
            rowKey="key"
            pagination={false}
            search={false}
            options={false}
            toolBarRender={() => [
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  const newTask = {
                    key: Date.now(),
                    site: undefined,
                    regions: [], // 改为数组
                    sql_script: '',
                  };
                  const newTasks = [...dbMigrationTasks, newTask];
                  setDbMigrationTasks(newTasks);
                  formRef.setFieldsValue({ db_migration_tasks: newTasks });
                }}
              >
                添加数据库迁移任务
              </Button>,
            ]}
            editable={{
              type: 'multiple',
              editableKeys: dbMigrationTasks.map(task => task.key),
              actionRender: () => [],
            }}
          />
        </Card>
      </ProForm>

      <ConfigDiffModal
        visible={configDiffModalVisible}
        onCancel={() => {
          setConfigDiffModalVisible(false);
          setCurrentConfigIndex(-1);
        }}
        onConfirm={(configData: string) => {
          if (currentConfigIndex >= 0 && currentConfigIndex < applicationTasks.length) {
            const newTasks = [...applicationTasks];
            newTasks[currentConfigIndex] = { ...newTasks[currentConfigIndex], config_data: configData };
            setApplicationTasks(newTasks);
            formRef.setFieldsValue({ application_tasks: newTasks });
          }
          setConfigDiffModalVisible(false);
          setCurrentConfigIndex(-1);
        }}
        applicationId={currentConfigIndex >= 0 && currentConfigIndex < applicationTasks.length ?
          applicationTasks[currentConfigIndex]?.application_id : undefined}
        selectedBranch={currentConfigIndex >= 0 && currentConfigIndex < applicationTasks.length ?
          applicationTasks[currentConfigIndex]?.git_branch : undefined}
        applications={applications}
      />
    </PageContainer>
  );
};

export default CreateReleasePlan;
