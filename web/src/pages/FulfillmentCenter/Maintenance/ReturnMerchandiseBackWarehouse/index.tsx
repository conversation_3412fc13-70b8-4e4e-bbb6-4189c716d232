import React, { useState } from 'react';
import { Card, message, Table, Tag } from 'antd';
import {
  PageContainer,
  ProForm,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { returnMerchandiseBackWarehouse } from '@/services/ant-design-pro/api';
import { RegionEnum } from '@/constants/enum';

interface WarehouseStatus {
  warehouseInNo: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
}

const ReturnMerchandiseBackWarehouse: React.FC = () => {
  const [warehouses, setWarehouses] = useState<WarehouseStatus[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (values: any) => {
    const warehouseInNos = values.warehouseInNos.split('\n').filter((no: string) => no.trim());

    // 初始化退货单状态
    const initialWarehouses = warehouseInNos.map((warehouseInNo: string) => ({
      warehouseInNo: warehouseInNo.trim(),
      status: 'pending' as const,
      error: '',
    }));
    setWarehouses(initialWarehouses);
    setIsProcessing(true);

    // 逐个处理退货单
    const results: WarehouseStatus[] = [];

    for (const warehouseInNo of warehouseInNos) {
      const trimmedNo = warehouseInNo.trim();
      if (!trimmedNo) continue;

      try {
        const response = await returnMerchandiseBackWarehouse({
          region: values.region,
          warehouseInNo: trimmedNo,
        });

        // 根据 API 响应判断成功或失败
        if (response.success !== false) {
          results.push({
            warehouseInNo: trimmedNo,
            status: 'success',
          });
        } else {
          results.push({
            warehouseInNo: trimmedNo,
            status: 'error',
            error: response.errorMessage || '处理失败',
          });
        }
      } catch (error: any) {
        results.push({
          warehouseInNo: trimmedNo,
          status: 'error',
          error: error?.message || '请求失败',
        });
      }
    }

    setWarehouses(results);
    
    // 统计结果
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    if (errorCount === 0) {
      message.success(`全部处理成功，共 ${successCount} 个退货单`);
    } else if (successCount === 0) {
      message.error(`全部处理失败，共 ${errorCount} 个退货单`);
    } else {
      message.warning(`处理完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
    }
    
    setIsProcessing(false);
  };

  const columns = [
    {
      title: '退货入库单号',
      dataIndex: 'warehouseInNo',
      key: 'warehouseInNo',
      width: '30%',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '20%',
      render: (status: string) => {
        const colorMap = {
          pending: 'blue',
          success: 'green',
          error: 'red',
        };
        const textMap = {
          pending: '处理中',
          success: '成功',
          error: '失败',
        };
        return <Tag color={colorMap[status as keyof typeof colorMap]}>{textMap[status as keyof typeof textMap]}</Tag>;
      },
    },
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      width: '50%',
      render: (error: string) => (
        <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
          {error}
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <Card title="退货落回库处理">
        <div style={{ marginBottom: 16, color: '#666', fontSize: '14px' }}>
          <p>此功能用于处理退货入库单，将退货商品信息同步到 RMA 系统中。</p>
          <p>请输入需要处理的退货入库单号，每行一个。系统会自动验证单号有效性并创建对应的 RMA 记录。</p>
        </div>
        <ProForm
          onFinish={handleSubmit}
          grid={true}
          layout="horizontal"
          submitter={{
            submitButtonProps: {
              loading: isProcessing,
            },
            searchConfig: {
              submitText: '开始处理',
            },
          }}
        >
          <ProFormSelect
            name="region"
            label="区域"
            placeholder="请选择区域"
            rules={[{ required: true, message: '请选择区域' }]}
            colProps={{ span: 6, style: { marginBottom: 0 } }}
            valueEnum={RegionEnum}
          />
          <ProFormTextArea
            name="warehouseInNos"
            label="退货入库单号"
            placeholder="请输入退货入库单号，每行一个&#10;例如：&#10;R-ORDER123456&#10;R-ORDER789012"
            rules={[{ required: true, message: '请输入退货入库单号' }]}
            colProps={{ span: 24, style: { marginBottom: 0 } }}
            fieldProps={{
              autoSize: { minRows: 4, maxRows: 8 },
            }}
          />
        </ProForm>
      </Card>
      {warehouses.length > 0 && (
        <Card title="处理状态" style={{ marginTop: 16 }}>
          <Table
            columns={columns}
            dataSource={warehouses}
            rowKey="warehouseInNo"
            pagination={false}
            size="small"
          />
        </Card>
      )}
    </PageContainer>
  );
};

export default ReturnMerchandiseBackWarehouse;
